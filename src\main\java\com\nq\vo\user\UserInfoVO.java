package com.nq.vo.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(description = "用户信息视图对象")
public class UserInfoVO {
    @ApiModelProperty(value = "用户ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "手机号码", example = "13800138000")
    private String phone;
    @ApiModelProperty(value = "昵称", example = "用户昵称")
    private String nickName;
    @ApiModelProperty(value = "真实姓名", example = "张三")
    private String realName;
    @ApiModelProperty(value = "身份证号", example = "110101199001011234")
    private String idCard;


    @ApiModelProperty(value = "是否锁定", example = "0")
    private Integer isLock;

    @ApiModelProperty(value = "注册时间", example = "2024-01-01 12:00:00")
    private Date regTime;

    @ApiModelProperty(value = "注册IP", example = "***********")
    private String regIp;

    @ApiModelProperty(value = "注册地址", example = "北京市")
    private String regAddress;

    @ApiModelProperty(value = "身份证正面照片", example = "img1.jpg")
    private String img1Key;

    @ApiModelProperty(value = "身份证反面照片", example = "img2.jpg")
    private String img2Key;
    @ApiModelProperty(value = "是否激活(0:未实名 1 提交申请 2:已实名 3 实名失败)", example = "0")
    private Integer isActive;
    @ApiModelProperty(value = "0 未绑定 1 绑定银行卡", example = "0")
    private Integer isBingBank;
    @ApiModelProperty(value = "0 未设置 1 已设置", example = "0")
    private Integer isWithPwd;
    @ApiModelProperty(value = "认证信息", example = "认证通过")
    private String authMsg;
    @ApiModelProperty(value = "总资产", example = "8000.00")
    private BigDecimal userAmt;
    @ApiModelProperty(value = "可用资产", example = "8000.00")
    private BigDecimal enableAmt;
    @ApiModelProperty(value = "保证金", example = "8000.00")
    private BigDecimal bzjAmt;
    @ApiModelProperty(value = "今日跟单收益", example = "0.00")
    private BigDecimal todayFollowIncome;

    @ApiModelProperty(value = "跟单总收益", example = "0.00")
    private BigDecimal totalFollowIncome;

    @ApiModelProperty(value = "今日队长收益", example = "0.00")
    private BigDecimal todayLeadIncome;

    @ApiModelProperty(value = "队长总收益", example = "0.00")
    private BigDecimal totalLeadIncome;

    @ApiModelProperty(value = "今日跟单数", example = "0")
    private Integer todayFollowNum;

    @ApiModelProperty(value = "邀请码", example = "ABC123")
    private String yqm;

    @ApiModelProperty(value = "客服链接", example = "ABC123")
    private String CustomerLink;

    @ApiModelProperty(value = "总跟单数", example = "0")
    private Integer totalFollowNum;
    @ApiModelProperty(value = "等级名称", example = "0")
    private String levelName;

    @ApiModelProperty(value = "首冲时间", example = "2024-01-01 12:00:00")
    private Date firstRechargeTime;

    @ApiModelProperty(value = "第一次跟单的结束时间")
    private Date firstFollowStopTime;
}
