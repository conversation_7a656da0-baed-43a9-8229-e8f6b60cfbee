package com.nq.dto.app;

import com.nq.dto.common.CommonPage;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/22/022 12:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AmountChangeParam  extends CommonPage {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("金额类型(1 平台账户, 2 证券账户)")
    private Integer amountType;
    @ApiModelProperty("帐变类型(1.充值 ,2.提现 ,3 挂单买入 4买入股票 5委托退会 6 卖出股票7股票退回{新股和增发认购退回} 8申请投顾9投顾驳回 10投顾退回(本金)  11 投顾收益 12投顾追加 13 投顾追加退回  14 证转银 15银转证  16 团队工资 17 奖金'")
    private Integer accountType;
}
