package com.nq.pojo;

import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;

@Data
@ApiModel(description = "股票实时行情数据")
@AllArgsConstructor
@NoArgsConstructor
public class RealTime extends BaseEntity {
    @ApiModelProperty(value = "主键ID", example = "1")
    private int id;

    @ApiModelProperty(value = "时间", example = "2024-01-01 10:00:00")
    private String time;

    @ApiModelProperty(value = "成交量", example = "1000")
    private int volumes;

    @ApiModelProperty(value = "当前价格", example = "10.5")
    private double price;

    @ApiModelProperty(value = "涨跌幅", example = "0.05")
    private double rates;

    @ApiModelProperty(value = "成交额", example = "1000000")
    private int amounts;

    @ApiModelProperty(value = "股票代码", example = "000001")
    private String stockCode;
    /*均价*/
    @ApiModelProperty(value = "均价", example = "10.2")
    private Double averagePrice;

    public String toString() {
        return "RealTime{id=" + this.id + ", time='" + this.time + '\'' + ", volumes=" + this.volumes + ", price=" + this.price + ", rates=" + this.rates  + ", averagePrice=" + this.averagePrice + ", amounts=" + this.amounts + ", stockCode='" + this.stockCode + '\'' + '}';
    }

    public int getId() {
        return this.id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTime() {
        return this.time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public int getVolumes() {
        return this.volumes;
    }

    public void setVolumes(int volumes) {
        this.volumes = volumes;
    }

    public double getPrice() {
        return this.price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public double getRates() {
        return this.rates;
    }

    public void setRates(double rates) {
        this.rates = rates;
    }

    public int getAmounts() {
        return this.amounts;
    }

    public void setAmounts(int amounts) {
        this.amounts = amounts;
    }

    public String getStockCode() {
        return this.stockCode;
    }

    public void setStockCode(String stockCode) {
        this.stockCode = stockCode;
    }

    public Double getAveragePrice() {
        return this.averagePrice;
    }

    public void setAveragePrice(Double averagePrice) {
        this.averagePrice = averagePrice;
    }
}
