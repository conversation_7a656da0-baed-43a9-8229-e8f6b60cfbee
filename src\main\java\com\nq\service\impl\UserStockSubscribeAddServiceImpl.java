package com.nq.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nq.pojo.UserStockSubscribeAdd;
import com.nq.dao.UserStockSubscribeAddMapper;
import com.nq.service.UserStockSubscribeAddService;
@Service
public class UserStockSubscribeAddServiceImpl extends ServiceImpl<UserStockSubscribeAddMapper, UserStockSubscribeAdd> implements UserStockSubscribeAddService{

}
