<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nq.dao.MentorApplyMapper">
    
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.nq.pojo.MentorApply">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="name" property="name"/>
        <result column="age" property="age"/>
        <result column="invest_years" property="investYears"/>
        <result column="salary_rate" property="salaryRate"/>
        <result column="guarantee_fund" property="guaranteeFund"/>
        <result column="company_name" property="companyName"/>
        <result column="introduction" property="introduction"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="audit_time" property="auditTime"/>
        <result column="audit_remark" property="auditRemark"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, name, age, invest_years, salary_rate, guarantee_fund,
        company_name, introduction, status, create_time, update_time, 
        audit_time, audit_remark
    </sql>

    <!-- 可以在这里添加自定义的查询方法 -->
    
</mapper> 