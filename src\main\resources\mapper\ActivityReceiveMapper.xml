<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nq.dao.ActivityReceiveMapper">
  <resultMap id="BaseResultMap" type="com.nq.pojo.ActivityReceive">
    <!--@mbg.generated-->
    <!--@Table activity_receive-->
    <id column="receive_id" jdbcType="INTEGER" property="receiveId" />
    <result column="order_sn" jdbcType="VARCHAR" property="orderSn" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="activity_name" jdbcType="VARCHAR" property="activityName" />
    <result column="activity_id" jdbcType="INTEGER" property="activityId" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    receive_id, order_sn, user_id, nick_name, activity_name, activity_id, amount, 
    create_time
  </sql>
</mapper>