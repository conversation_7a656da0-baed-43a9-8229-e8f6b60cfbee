package com.nq.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(description = "用户提现记录")
@AllArgsConstructor
@NoArgsConstructor
public class UserWithdraw extends BaseEntity {

    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    @Excel(name = "用户id")
    @ApiModelProperty(value = "用户ID", example = "1")
    private Integer userId;

    @Excel(name = "订单号")
    @ApiModelProperty(value = "订单号", example = "sdfsdafds")
    private String orderSn;

    @Excel(name = "用户名")
    @ApiModelProperty(value = "用户昵称", example = "张三")
    private String nickName;


    @Excel(name = "提现金额")
    @ApiModelProperty(value = "提现金额", example = "10000.00")
    private BigDecimal withAmt;

    @Excel(name = "实际出款金额")
    @ApiModelProperty(value = "实际出款金额", example = "10000.00")
    private BigDecimal withOutAmt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "申请时间", databaseFormat = "yyyyMMddHHmmss", format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "申请时间", example = "2024-01-01 09:30:00")
    private Date applyTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "出金时间", databaseFormat = "yyyyMMddHHmmss", format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "提现时间", example = "2024-01-01 10:30:00")
    private Date transTime;

    @ApiModelProperty(value = "收款人姓名", example = "张三")
    private String withName;

    @Excel(name = "银行卡号")
    @ApiModelProperty(value = "银行卡号", example = "6222021234567890123")
    private String bankNo;

    @Excel(name = "银行名称")
    @ApiModelProperty(value = "银行名称", example = "中国工商银行")
    private String bankName;

    @Excel(name = "银行支行")
    @ApiModelProperty(value = "银行支行", example = "北京分行朝阳支行")
    private String bankAddress;

    @Excel(name = "状态" ,replace = { "审核中_0", "成功_1", "失败_2", "取消_3", "上游下单成功_4", "上游下单失败_5"  })
    @ApiModelProperty(value = "提现状态：0-审核中，1-成功，2-失败，3-取消, 4-上游下单成功, 5-上游下单失败", example = "0")
    private Integer withStatus;

    @Excel(name = "手续费")
    @ApiModelProperty(value = "手续费", example = "10.00")
    private BigDecimal withFee;

    @Excel(name = "原因")
    @ApiModelProperty(value = "提现原因/备注", example = "提现申请")
    private String withMsg;
}