


#cookie config start
admin.super.name=18916320007


#?? ?? - ????
admin.auth.email.subject=???? - ??????
#?? ?? - ????
admin.auth.email=<EMAIL>
#?? ?? - ????  118318asd  XY118318asd
admin.auth.email.pwd=XY118318asd
#cookie config end

# close order task config start
#   ???? 50s ????????? 5s
close.order.task.time.hour=2
lock.timeout=50000
# close order task config start
user.password=abcd1234abcd1234
#aliyun sms config
sms.aliyun.accessKeyId=LTAI4FqEYw8va2Bgak2fd9pL
sms.aliyun.accessKeySecret=******************************

#juhe api
juhe.stock.key=e27571e4f5f2f07440bf4996d01d5770
juhe.ip.key=e5ad6f81997d4f101cc3d17409e18d96
#??juhe.ip.key1=ea84726df1d952b8271e118eca51be34//a86451534a6e72728b8cea430dabc633//e5ad6f81997d4f101cc3d17409e18d96//ea84726df1d952b8271e118eca51be34

#juhe api end

#site setting
site.email.auth.url=/api/admin/authCharge.do
site.pc.reg.url=/homes/#/register?code=
site.m.reg.url=/wap/#/register?code=

#??????
#pc.refresh.time=3000


# ?? API ??
#sina.market.url=https://hq.sinajs.cn/rn=1520407404627&list=s_sh000001,s_sz399001,s_sz399006,s_sz399300,s_sz399005,s_sz399673
sina.market.url=https://hq.sinajs.cn/rn=1520407404627&list=s_sh000001,s_sz399001,s_sz399006,s_sz399300,s_sz399005,s_sz399673,s_sz399106,s_sz399004,s_sz399100
sina.single.market.url=https://hq.sinajs.cn/rn=1520407404627&list=s_

sina.index.market.url=https://ws.api.cnyes.com/ws/api/v4/universal/quote?type=LITQ&column=L

sina.single.stock.url=https://hq.sinajs.cn/list=
#????
sina.single.stock.introduction.url=https://quotes.sina.cn/cn/api/openapi.php/CompanyF10Service.getCompanyInformation?market=cn&symbol=
nq.single.stock.url=http://192.168.10.5/stock/?type=
sina.single.stock.min.url=http://image.sinajs.cn/newchart/min/n/
sina.single.stock.day.url=http://image.sinajs.cn/newchart/daily/n/
sina.single.stock.week.url=http://image.sinajs.cn/newchart/weekly/n/
sina.single.stock.month.url=http://image.sinajs.cn/newchart/monthly/n/
getUrl=http://192.168.10.3
#????cookle
cookle=xq_a_token=d269ad4aee7ece063038900846f9541a7d0ead07
#\u7F8E\u80A1\u6E2F\u80A1\u540C\u6B65
hk.stock.url = https://32.push2delay.eastmoney.com/api/qt/clist/get?pn=1&pz=9999&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=%7C0%7C0%7C0%7Cweb&fid=f3&fs=m:128+t:3,m:128+t:4,m:128+t:1,m:128+t:2&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f19,f20,f21,f23,f24,f25,f26,f22,f33,f11,f62,f128,f136,f115,f152&_=
hk.stock.introduction.url =https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_INFO_ORGPROFILE&columns=SECUCODE,SECURITY_CODE,ORG_NAME,ORG_EN_ABBR,BELONG_INDUSTRY,FOUND_DATE,CHAIRMAN,SECRETARY,ACCOUNT_FIRM,REG_ADDRESS,ADDRESS,YEAR_SETTLE_DAY,EMP_NUM,ORG_TEL,ORG_FAX,ORG_EMAIL,ORG_WEB,ORG_PROFILE,REG_PLACE&quoteColumns=&filter=(SECUCODE=
hk.index.url = https://56.push2.eastmoney.com/api/qt/clist/get?pn=1&pz=9999&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=%7C0%7C0%7C0%7Cweb&fid=f5&fs=m:124,m:125,m:305&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f26,f22,f33,f11,f62,f128,f136,f115,f152&_=
hk.index.introduction.url = https://newsinfo.eastmoney.com/kuaixun/v2/api/Channel/5/0/0/50
#\u7F8E\u80A1\u4FE1\u606F\u542F\u52A8\u52A0\u8F7D
us.stock.url = https://1.push2.eastmoney.com/api/qt/clist/get?cb=jQuery1124024849404112285045_1668165974609&pn=1&pz=********&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=%7C0%7C0%7C0%7Cweb&fid=f2&fs=m:105,m:106,m:107&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f26,f22,f33,f11,f62,f128,f136,f115,f152&_=
us.stock.introduction.url =https://emweb.eastmoney.com/pc_usf10/CompanyInfo/PageAjax?fullCode=
us.stock.buy.url = https://push2.eastmoney.com/api/qt/stock/details/get?fields1=f1,f2,f3,f4&fields2=f51,f52,f53,f54,f55&fltt=2&pos=-14&secid=
us.index.url = https://56.push2.eastmoney.com/api/qt/clist/get?pn=1&pz=999&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=%7C0%7C0%7C0%7Cweb&fid=f3&fs=i:100.NDX,i:100.DJIA,i:100.SPX&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f26,f22,f33,f11,f62,f128,f136,f115,f152,f124,f107&_=

#\u6539\u53D8\u73B0\u4EF7
#changePrice.url =http://116.124.132.181/stock/base/cx_gp
#????
home.index.recommend.url=https://guba.sina.cn/api/?s=community&a=get_hot_stock_list&type=cn&num=20&page=1&platform=ios&version=6.10.0
#\u9F99\u864E\u699C
longhu.url =https://datacenter-web.eastmoney.com/api/data/v1/get?&sortColumns=SECURITY_CODE%2CTRADE_DATE&sortTypes=1%2C-1&pageSize=999&pageNumber=1&reportName=RPT_DAILYBILLBOARD_DETAILSNEW&columns=SECURITY_CODE%2CSECUCODE%2CSECURITY_NAME_ABBR%2CTRADE_DATE%2CEXPLAIN%2CCLOSE_PRICE%2CCHANGE_RATE%2CBILLBOARD_NET_AMT%2CBILLBOARD_BUY_AMT%2CBILLBOARD_SELL_AMT%2CBILLBOARD_DEAL_AMT%2CACCUM_AMOUNT%2CDEAL_NET_RATIO%2CDEAL_AMOUNT_RATIO%2CTURNOVERRATE%2CFREE_MARKET_CAP%2CEXPLANATION%2CD1_CLOSE_ADJCHRATE%2CD2_CLOSE_ADJCHRATE%2CD5_CLOSE_ADJCHRATE%2CD10_CLOSE_ADJCHRATE%2CSECURITY_TYPE_CODE&source=WEB&client=WEB&filter=(TRADE_DATE%3C%3D%272022-12-02%27)(TRADE_DATE%3E%3D%272022-12-02%27)
#\u5341\u5927\u6210\u4EA4\u80A1 \u6CAA 1 \u6DF13 1%22)
top10.url =https://datacenter-web.eastmoney.com/api/data/v1/get?&sortColumns=TRADE_DATE%2CRANK&sortTypes=-1%2C1&pageSize=10&pageNumber=1&reportName=RPT_MUTUAL_TOP10DEAL&columns=ALL&source=WEB&client=WEB&filter=(MUTUAL_TYPE%3D%2200
#\u6BCF\u65E5\u505C\u724C
stop.url =https://datacenter-web.eastmoney.com/api/data/v1/get?&sortColumns=SUSPEND_START_DATE&sortTypes=-1&pageSize=500&pageNumber=1&reportName=RPT_CUSTOM_SUSPEND_DATA_INTERFACE&columns=ALL&source=WEB&client=WEB&filter=(MARKET%3D%22%E5%85%A8%E9%83%A8%22)(DATETIME%3D%272022-12-02%27)
#\u6DA8\u505C\u677F
ztb.url =https://push2ex.eastmoney.com/getTopicZTPool?&ut=7eea3edcaed734bea9cbfc24409ed989&dpt=wz.ztzt&Pageindex=0&pagesize=999&sort=fbt%3Aasc&date=20221202&_=


#\u540C\u82B1\u987A\u6DA8\u8DCC\u5E45\u5E02\u573A\u6982\u51B5
ths.market.zdfb=http://q.10jqka.com.cn/api.php?t=indexflash&
dfcf.market.zdfb=http://push2.eastmoney.com/api/qt/ulist/get?fltt=1&invt=2&fields=f12%2Cf13%2Cf14%2Cf1%2Cf2%2Cf4%2Cf3%2Cf152%2Cf6%2Cf104%2Cf105%2Cf106&secids=1.000001%2C0.399001&ut=fa5fd1943c7b386f172d6893dbfba10b&pn=1&np=1&wbp2u=%7C0%7C0%7C0%7Cweb&_=1664682077781

#\u4E1C\u65B9\u8D22\u5BCC\u65B0\u80A1\u65E5\u5386\u63A5\u53E3
dfcf.new.stock.url=https://datacenter-web.eastmoney.com/api/data/v1/get?sortColumns=APPLY_DATE,SECURITY_CODE&sortTypes=-1,-1&pageSize=35&pageNumber=1&reportName=RPTA_APP_IPOAPPLY&columns=SECURITY_CODE,SECURITY_NAME,TRADE_MARKET_CODE,APPLY_CODE,TRADE_MARKET,MARKET_TYPE,ORG_TYPE,ISSUE_NUM,ONLINE_ISSUE_NUM,OFFLINE_PLACING_NUM,TOP_APPLY_MARKETCAP,PREDICT_ONFUND_UPPER,ONLINE_APPLY_UPPER,PREDICT_ONAPPLY_UPPER,ISSUE_PRICE,LATELY_PRICE,CLOSE_PRICE,APPLY_DATE,BALLOT_NUM_DATE,BALLOT_PAY_DATE,LISTING_DATE,AFTER_ISSUE_PE&quoteColumns=f2~01~SECURITY_CODE~NEWEST_PRICE&quoteType=0&filter=(APPLY_DATE%3E%272022-06-20%27)&source=WEB&client=WEB
#\u65B0\u6D6A\u8D22\u7ECF\u6DA8\u8DCC\u5E45\u67E5\u8BE2\u63A5\u53E3
sina.market.zdf.url=https://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeData?


#\u67E5\u8BE2\u671F\u8D27\u8BE6\u60C5\u4FE1\u606F
sina.single.futures.url=https://hq.sinajs.cn/list=
sina.single.futures.introduction.url=https://gu.sina.cn/ft/api/openapi.php/FuturesService.getInfoBySymbol?symbol=
#????
sina.single.exchange.url=https://hq.sinajs.cn/list=

# ????K?-???
sina.k.min.url=http://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData
sina.k.min.max.size=200
# ????K?-???
sina.futures.k.min.url=https://gu.sina.cn/ft/api/jsonp.php/var%20_XAU_{time}_{stamp}=/GlobalService.getMink?symbol={code}&type={time}
sina.futures.k.min.max.size=50
# ????K?-???
sina.futures.day.min.url=https://stock2.finance.sina.com.cn/futures/api/jsonp.php/var%20_{code}{date}=/GlobalFuturesService.getGlobalFuturesDailyKLine?symbol={code}&_={date}&source=web
sina.futures.day.min.max.size=50
# ????K?-???
sina.index.k.min.url=https://quotes.sina.cn/cn/api/jsonp_v2.php/var%20_{code}_{time}_{stamp}=/CN_MarketDataService.getKLineData?symbol={code}&scale={time}&ma=no&datalen=1023
sina.index.k.min.max.size=50
# ????K?-???
sina.index.day.min.url=http://web.ifzq.gtimg.cn/appstock/app/fqkline/get?_var=kline_dayqfq&param={code},day,,,50,qfq&r=0.8531244563261586
sina.index.day.min.max.size=50
# ??K?-??
qq.k.min.url=http://data.gtimg.cn/flashdata/hushen/daily/20/code.js?visitDstTime=1
qq.k.min.max.size=50
#??????????
qq.month.min.url=http://web.ifzq.gtimg.cn/appstock/app/fqkline/get?param=sz300750,month,,,320,qfq
#???API
sina.TwMarket.url=https://ws.api.cnyes.com/ws/api/v1/quote/quotes/
sina.TwMarket.list.url=http://b.workingman.icu/stock/api/stock/getStockType?exchange=TAI
sina.StockType.list.url=http://b.workingman.icu/stock/api/stock/getStockType?exchange=
sina.StockCategory.list.url=http://b.workingman.icu/stock/api/stock/getWapStocks

http.proxyHost =
http.proxyPort =
https.proxyHost =
https.proxyPort =

# ????
#????
juhe1.pay.callbackurl=http://xx.com
#??????
juhe1.pay.notifyurl=http://xx.com

#Fly ??
fly.pay.merchantid=SLPNXG
fly.pay.token=0a17a92f45816a88431fa326b3b61baf

#Cmc ??
#??????
cmc.pay.key=0a17a92f45816a88431fa326b3b61baf
#????appid
cmc.pay.uid=74021809
#??????
cmc.pay.return_url=https://www.bldzb666888.com/homes/#/rechargelist
#??????
cmc.pay.notify_url=https://www.bldzb666888.com/api/pay/juhenewpayNotify.do
#???????
cmc.pay.url=http://pay.phu889.com/api.php/webRequest/tradePay



fly.pay.serverbackurl=http://www.honghoo.top/api/pay/flyNotify.do
fly.pay.callbackurl=http://www.honghoo.top/wap/#/user

fly.pay.payurl=https://zf.flyotcpay.com/payment/


#????
#???
wj.sms.uid=yhi1993
#??
wj.sms.key=5802bf8c5e3c5bc46c48
#??gbk/utf8
wj.sms.coding=utf8
#\u77ED\u4FE1\u5B9D
#dxb.sms.USERNAME=banbao
#dxb.sms.PASSWORD=a705312041274b72b77d43b0ce136913



#?????
website.domain.url=http://www.huijuwang888.com
website.token=0DC8F78384C7AAFF3192A9C60A473FEE7F89C62888689616B98A06910E86B510

#?????
news.main.url=http://eminfo.eastmoney.com

spring.main.allow-circular-references=true
