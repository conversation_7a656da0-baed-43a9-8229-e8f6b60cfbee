package com.nq.dto.app;

import com.nq.dto.common.CommonPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@ApiModel(description = "各种人数")
public class TeamInfoDTO extends CommonPage {

    @NotNull(message = "0 团队总人数 1 直属人数 2 三日未跟单人数 3 今日首充人数")
    @ApiModelProperty(value = "类型", required = true, example = "1")
    private Integer type;

} 