<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nq.dao.SiteMessageMapper">

    <resultMap id="BaseResultMap" type="com.nq.pojo.SiteMessage" >
        <result column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="type_name" property="typeName" />
        <result column="status" property="status" />
        <result column="add_time" property="addTime" />
        <result column="update_time" property="updateTime" />
        <result column="content" property="content" />
        <result column="message_type" property="messageType" />
    </resultMap>

    <sql id="Base_Column_List">
                id,
                user_id,
                user_name,
                type_name,
                status,
                add_time,
                update_time,
                content,
                message_type
    </sql>
    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM site_message
        WHERE id = #{id}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM site_message
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM site_message
    </select>

    <select id="getSiteMessageByUserIdList" resultType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM site_message
        WHERE 1=1
        <if test="userId != null and userId != 0">
            and user_id = #{userId}
        </if>
        order by id desc
    </select>

    <select id="getUnReadSiteMessageByType" resultType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM site_message
        WHERE status=1
        <if test="type != null">
            and type_name = #{type}
        </if>
        order by id desc
    </select>


    <select id="queryByMessageType" resultType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM site_message
        WHERE status=1
        <if test="messageType != null">
            and message_type = #{messageType}
        </if>
        order by id desc
    </select>


    <select id="getIsDayCount" resultType="java.lang.Integer">
         SELECT count(1)
        FROM site_message where user_id = #{userId} and type_name = #{typeName}
        and TO_DAYS(add_time) = TO_DAYS(NOW())
    </select>

    <!--用户站内消息状态变已读-->
    <update id="updateMessageStatus">
        UPDATE site_message set status = 2,update_time = now()
        WHERE user_id = #{userId} and status= 1
    </update>

    <!--查询用户未读消息数-->
    <select id="getUnreadCount" resultType="java.lang.Integer">
         SELECT count(1)
        FROM site_message where user_id = #{userId} and status=1 and message_type=1
    </select>

</mapper>