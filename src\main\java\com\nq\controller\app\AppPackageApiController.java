package com.nq.controller.app;


import com.nq.common.ServerResponse;
import com.nq.dto.app.MoreFollowApplyDTO;
import com.nq.dto.app.PackageInfoDTO;
import com.nq.dto.app.SingleFollowApplyDTO;
import com.nq.pojo.User;
import com.nq.service.IUserService;
import com.nq.service.LevelService;
import com.nq.service.PackageService;
import com.nq.vo.app.AppDayPackageVO;
import com.nq.vo.app.AppMorePackageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;


@Api(tags = "APP-跟单套餐")
@RestController
@RequestMapping("/api/package")
public class AppPackageApiController {
    @Resource
    private IUserService iUserService;
    @Resource
    private LevelService levelService;
    @Resource
    private PackageService packageService;

    @ApiOperation("查询跟单日套餐")
    @PostMapping("/queryDayPackage.do")
    public ServerResponse<AppDayPackageVO> queryDayPackage(HttpServletRequest request,@RequestBody @Validated PackageInfoDTO dto) {
        User user = iUserService.getCurrentUser();
        if (user == null) {
            return ServerResponse.createBySuccessMsg("用户未登录");
        }
        AppDayPackageVO vo =  levelService.queryDayPackage(user,dto.getMentorId());
        return ServerResponse.createBySuccess(vo);
    }
    @ApiOperation("查询跟单日下面的信息")
    @PostMapping("/queryDayPackageInfo.do")
    public ServerResponse<List<AppDayPackageVO>> queryDayPackageInfo(@RequestBody @Validated PackageInfoDTO dto) {
        List<AppDayPackageVO> vos =  levelService.queryDayPackageInfo(dto.getMentorId());
        return ServerResponse.createBySuccess(vos);
    }

    @ApiOperation("查询多日跟单")
    @PostMapping("/queryMorePackage.do")
    public ServerResponse<List<AppMorePackageVO>> queryMorePackage(@RequestBody @Validated PackageInfoDTO dto) {
        List<AppMorePackageVO> vos =  packageService.queryMorePackage();
        return ServerResponse.createBySuccess(vos);
    }

    @ApiOperation("申请单日跟单")
    @PostMapping("/applySingleFollow.do")
    public ServerResponse<Void> applySingleFollow(@RequestBody @Validated SingleFollowApplyDTO dto, HttpServletRequest request) {
        User user = iUserService.getCurrentUser();
        if (user == null) {
            return ServerResponse.createByErrorMsg("用户未登录");
        }
        return packageService.applySingleFollow(dto, user);
    }
    @ApiOperation("申请多日跟单")
    @PostMapping("/applyMoreFollow.do")
    public ServerResponse<Void> applyMoreFollow(@RequestBody @Validated MoreFollowApplyDTO dto, HttpServletRequest request) {
        User user = iUserService.getCurrentUser();
        if (user == null) {
            return ServerResponse.createByErrorMsg("用户未登录");
        }
        return packageService.applyMoreFollow(dto, user);
    }

}

