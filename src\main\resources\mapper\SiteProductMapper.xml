<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nq.dao.SiteProductMapper" >
  
  <resultMap id="BaseResultMap" type="com.nq.pojo.SiteProduct">
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="real_name_display" property="realNameDisplay" jdbcType="BIT"/>
    <result column="holiday_display" property="holidayDisplay" jdbcType="BIT"/>
  </resultMap>

  <sql id="Base_Column_List" >
    id, real_name_display, holiday_display
  </sql>


  
</mapper>




