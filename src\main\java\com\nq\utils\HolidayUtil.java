package com.nq.utils;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class HolidayUtil {
    @Resource
    private RedisUtil redisUtil;

    public Date saveDateRedis(Integer day) {
        String redisKey = RedisKey.WORKING_DAYS_LIST_KEY;
        if (redisUtil.exists(redisKey)) {
            List<String> list = redisUtil.getListStringRange(redisKey);
            String s = list.get(day );
            Date endTime = DateUtils.stringToDate(s,DateUtils.DATE_TIME_PATTERN);
            return endTime;

        };
        List<String> datesToQuery = new ArrayList<>();
        Date today = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");

        for (int i = 0; i < 360; i++) {
            Date futureDate = DateUtils.addDateDays(today, i);
            datesToQuery.add(dateFormat.format(futureDate));
        }

        String datesArg = String.join(",", datesToQuery);
        String jsonResult = HolidayUtil.request(datesArg);

        if (jsonResult == null || jsonResult.isEmpty()) {
            return new Date();
        }
        ArrayList<Date> arr = CollectionUtil.newArrayList();
        ArrayList<String> stringsArr = CollectionUtil.newArrayList();


        Map<String, Integer> map = JSON.parseObject(jsonResult, Map.class);
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            String key = entry.getKey();
            Integer value = entry.getValue();
            if (value == 0){
                Date date = DateUtils.getYYYMMDDEndTime(key);
                arr.add(date);
            }
        }
        Collections.sort(arr);
        for (Date date : arr) {
            String format = DateUtils.format(date, DateUtils.DATE_TIME_PATTERN);
            stringsArr.add(format);
        }
        redisUtil.lPushAllString(redisKey, stringsArr,24*60*60);
        String s = stringsArr.get(day);
        Date endTime = DateUtils.stringToDate(s,DateUtils.DATE_TIME_PATTERN);
        return endTime;
    }
    /**
     *            :請求接口
     * @param httpArg
     *            :參數
     * @return 返回結果
     */
    public static String request(String httpArg) {
        String httpUrl = "http://tool.bitefu.net/jiari/";
        BufferedReader reader = null;
        String result = null;
        StringBuffer sbf = new StringBuffer();
        httpUrl = httpUrl + "?d=" +httpArg;
        try {
            URL url = new URL(httpUrl);
            HttpURLConnection connection = (HttpURLConnection) url
                    .openConnection();
            connection.setRequestMethod("GET");
            connection.connect();
            InputStream is = connection.getInputStream();
            reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
            String strRead = null;
            while ((strRead = reader.readLine()) != null) {
                sbf.append(strRead);
            }
            reader.close();
            result = sbf.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }



}
