package com.nq.common;


import com.nq.enums.ResultCode;

public final class ResultUtils {

    private ResultUtils() {
    }

    public static <T> ServerResponse<T> success() {
        ServerResponse<T> result = new ServerResponse<>();
        result.setStatus(ResultCode.SUCCESS.getCode());
        result.setMsg(ResultCode.SUCCESS.getMsg());
        return result;
    }

    public static <T> ServerResponse<T> success(T data) {
        ServerResponse<T> result = new ServerResponse<>();
        result.setStatus(ResultCode.SUCCESS.getCode());
        result.setMsg(ResultCode.SUCCESS.getMsg());
        result.setData(data);
        return result;
    }

    public static <T> ServerResponse<T> success(T data, String messsage) {
        ServerResponse<T> result = new ServerResponse<>();
        result.setStatus(ResultCode.SUCCESS.getCode());
        result.setMsg(messsage);
        result.setData(data);
        return result;
    }

    public static <T> ServerResponse<T> success(String messsage) {
        ServerResponse<T> result = new ServerResponse<>();
        result.setStatus(ResultCode.SUCCESS.getCode());
        result.setMsg(messsage);
        return result;
    }

    public static <T> ServerResponse<T> error(Integer code, String messsage) {
        ServerResponse<T> result = new ServerResponse<>();
        result.setStatus(code);
        result.setMsg(messsage);
        return result;
    }


    public static <T> ServerResponse<T> error(ResultCode resultCode, String messsage) {
        ServerResponse<T> result = new ServerResponse<>();
        result.setStatus(resultCode.getCode());
        result.setMsg(messsage);
        return result;
    }


    public static <T> ServerResponse<T> error(ResultCode resultCode) {
        ServerResponse<T> result = new ServerResponse<>();
        result.setStatus(resultCode.getCode());
        result.setMsg(resultCode.getMsg());
        return result;
    }


}
