package com.nq.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dao.SiteNewsMapper;
import com.nq.dao.SitePopupMapper;
import com.nq.pojo.SiteNews;
import com.nq.pojo.SitePopup;
import com.nq.service.ISiteNewsService;
import com.nq.service.SitePopupService;
import com.nq.utils.DateUtils;
import com.nq.utils.PageUtil;
import com.nq.vo.admin.SitePopupVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class SitePopupServiceImpl extends ServiceImpl<SitePopupMapper, SitePopup> implements SitePopupService {
    @Resource
    private SitePopupMapper sitePopupMapper;

    @Override
    public int insert(SitePopup popup) {
        return sitePopupMapper.insert(popup);
    }

    @Override
    public int update(SitePopup popup) {
        return sitePopupMapper.updateById(popup);
    }

    @Override
    public int deleteById(Integer id) {
        return sitePopupMapper.deleteById(id);
    }

    @Override
    public SitePopup selectById(Integer id) {
        return sitePopupMapper.selectById(id);
    }

    @Override
    public SitePopupVO selectVOById(Integer id) {
        SitePopup popup = sitePopupMapper.selectById(id);
        if (popup == null) return null;
        SitePopupVO vo = new SitePopupVO();
        BeanUtils.copyProperties(popup, vo);
        return vo;
    }

    @Override
    public ServerResponse<SitePopupVO> getPopup() {
        LambdaQueryWrapper<SitePopup> q = new LambdaQueryWrapper<>();
        q.eq(SitePopup::getStatus, 1);
        q.orderByDesc(SitePopup::getCreateTime);
        q.ge(SitePopup::getEndTime, new Date());
        q.le(SitePopup::getStartTime, new Date());
        q.last("limit 1");
        List<SitePopup> list = this.list(q);
        if(list.isEmpty()) {
            return ServerResponse.createBySuccess(null);
        }
        SitePopup sitePopup = list.get(0);
        SitePopupVO vo = new SitePopupVO();
        BeanUtils.copyProperties(sitePopup, vo);
        return ServerResponse.createBySuccess(vo);
    }

    @Override
    public PageInfo<SitePopupVO> selectAllVO(Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        LambdaQueryWrapper<SitePopup> q = new LambdaQueryWrapper<>();

        List<SitePopup> list = sitePopupMapper.selectList(q);
        List<SitePopupVO> voList = new ArrayList<>();
        for (SitePopup popup : list) {
            SitePopupVO vo = new SitePopupVO();
            BeanUtils.copyProperties(popup, vo);
            voList.add(vo);
        }
        return PageUtil.buildPageDto(list, voList);
    }
} 