package com.nq.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nq.pojo.StockMarketsDay;
import java.math.BigDecimal;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
public interface StockMarketsDayMapper extends BaseMapper<StockMarketsDay> {

  
  BigDecimal selectRateByDaysAndStockCode(@Param("stockId") Integer paramInteger1, @Param("days") Integer paramInteger2);
}
