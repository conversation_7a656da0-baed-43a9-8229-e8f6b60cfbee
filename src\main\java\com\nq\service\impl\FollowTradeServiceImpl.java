package com.nq.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.constant.OrderConstant;
import com.nq.dao.FollowTradeMapper;
import com.nq.dto.app.TradeRecordQueryDTO;
import com.nq.pojo.*;
import com.nq.service.FollowDetailService;
import com.nq.service.FollowService;
import com.nq.service.FollowTradeService;
import com.nq.service.MentorService;
import com.nq.service.IUserService;
import com.nq.utils.BeanCopyUtil;
import com.nq.utils.DateUtils;
import com.nq.utils.PageUtil;
import com.nq.utils.SnowIdUtil;
import com.nq.vo.admin.FollowTradeVO;
import com.nq.vo.app.TradeDetailVO;
import com.nq.vo.app.TradeRecordVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class FollowTradeServiceImpl extends ServiceImpl<FollowTradeMapper, FollowTrade> implements FollowTradeService {

    @Resource
    private MentorService mentorService;

    @Resource
    private FollowDetailService followDetailService;

    @Resource
    private FollowService followService;

    @Resource
    private IUserService userService;

    @Override
    public PageInfo<FollowTradeVO> getFollowTradeList(Integer page, Integer size, Integer mentorId, String stockCode,
                                                      Integer status) {
        PageHelper.startPage(page, size);
        LambdaQueryWrapper<FollowTrade> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(mentorId != null, FollowTrade::getMentorId, mentorId)
                .eq(stockCode != null && !stockCode.trim().isEmpty(), FollowTrade::getStockCode, stockCode)
                .eq(status != null, FollowTrade::getStatus, status)
                .orderByDesc(FollowTrade::getCreateTime);
        List<FollowTrade> list = this.list(wrapper);
        List<FollowTradeVO> voList = list.stream().map(this::convertToVO).collect(Collectors.toList());
        return PageUtil.buildPageDto(list, voList);
    }

    @Override
    public FollowTradeVO getFollowTradeById(Integer id) {
        FollowTrade followTrade = getById(id);
        if (followTrade == null) {
            return null;
        }
        return convertToVO(followTrade);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createFollowTrade(FollowTrade followTrade) {
        // 设置交易单号
        String orderNo = SnowIdUtil.getId(OrderConstant.followTrade);
        followTrade.setTradeNo(orderNo);
        boolean result = save(followTrade);

        // 处理跟单详情 另一个页面的数据
        if (result) {
            processFollowDetails(followTrade);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFollowTrade(FollowTrade followTrade) {
        // 获取原始数据
        FollowTrade oldTrade = getById(followTrade.getId());
        System.out.println("更新跟单交易: " + followTrade);
        if (oldTrade == null) {
            return false;
        }

        // 确保必要的字段不为空
        if (followTrade.getMentorId() == null) {
            followTrade.setMentorId(oldTrade.getMentorId());
        }
        if (followTrade.getStockCode() == null) {
            followTrade.setStockCode(oldTrade.getStockCode());
        }
        if (followTrade.getStockName() == null) {
            followTrade.setStockName(oldTrade.getStockName());
        }

        // 如果前端传了卖出价格，则认为是清仓操作
        if (followTrade.getSellPrice() != null) {
            // 设置状态为已清仓
            followTrade.setStatus(2);

            // 设置卖出时间（如果未设置）
            if (followTrade.getSellTime() == null) {
                followTrade.setSellTime(new Date());
            }

            // 确保买入价格不为空
            if (followTrade.getBuyPrice() == null) {
                followTrade.setBuyPrice(oldTrade.getBuyPrice());
            }

            // 确保买入时间不为空
            if (followTrade.getBuyTime() == null) {
                followTrade.setBuyTime(oldTrade.getBuyTime());
            }

            // 计算盈亏百分比
            if (followTrade.getBuyPrice() != null) {
                BigDecimal profitLossPercent = followTrade.getSellPrice().subtract(followTrade.getBuyPrice())
                        .divide(followTrade.getBuyPrice(), 4, BigDecimal.ROUND_HALF_UP)
                        .multiply(new BigDecimal("100"));

                followTrade.setProfitLossPercent(profitLossPercent);
            }
        }
        // 如果前端传了状态为持仓中(1)
        else if (followTrade.getStatus() != null && followTrade.getStatus() == 1) {
            // 设置发布时间（如果未设置）
            if (followTrade.getPublishTime() == null) {
                followTrade.setPublishTime(new Date());
            }
        }
        boolean result = updateById(followTrade);
        // 处理跟单详情 另一个页面的数据
        if (result) {
            processFollowDetails(followTrade);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFollowTrade(Integer id) {
        return removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean publishFollowTrade(Integer id) {
        // 获取原始数据
        FollowTrade oldTrade = getById(id);
        if (oldTrade == null) {
            return false;
        }

        // 更新状态和发布时间
        FollowTrade followTrade = new FollowTrade();
        followTrade.setId(id);
        followTrade.setStatus(1); // 1-持仓中
        followTrade.setPublishTime(new Date());

        return updateById(followTrade);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean closeFollowTrade(Integer id, BigDecimal sellPrice) {
        // 获取原始数据
        FollowTrade oldTrade = getById(id);
        if (oldTrade == null) {
            return false;
        }

        // 创建更新对象，保留原始数据中的重要字段
        FollowTrade followTrade = new FollowTrade();
        followTrade.setId(id);
        followTrade.setMentorId(oldTrade.getMentorId());
        followTrade.setStockCode(oldTrade.getStockCode());
        followTrade.setStockName(oldTrade.getStockName());
        followTrade.setBuyPrice(oldTrade.getBuyPrice());
        followTrade.setBuyTime(oldTrade.getBuyTime()); // 保留买入时间
        followTrade.setSellPrice(sellPrice);
        followTrade.setStatus(2); // 设置状态为已清仓
        followTrade.setSellTime(new Date());

        // updateFollowTrade方法会自动计算盈亏百分比
        return updateFollowTrade(followTrade);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processFollowDetails(FollowTrade dto) { // followTrade 这个页面的数据 isCreate
        // 是否是创建操作
        // 获取导师ID 去跟单页面查是否有人跟单
        Integer mentorId = dto.getMentorId();
        if (mentorId == null) {
            return;
        }
        // 获取交易信息 跟单发布页面创建或者更新时候填写的信息
        BigDecimal buyPrice = dto.getBuyPrice();
        BigDecimal sellPrice = dto.getSellPrice();
        Integer status = dto.getStatus();
        //先写卖出
        LambdaQueryWrapper<FollowDetail> detailWrapper = new LambdaQueryWrapper<>();
        if (status == 1) {
            //买如
            // 查询该导师下的所有跟单
            // 查询指定导师的跟单中记录 一个单子只能发布一个跟单
            LambdaQueryWrapper<Follow> followWrapper = new LambdaQueryWrapper<>();
            followWrapper.eq(Follow::getMentorId, mentorId)
                    .eq(Follow::getStatus, 2)
                    .and(w -> w
                            .le(Follow::getToday, DateUtils.formatStart(new Date()))
                            .or()
                            .isNull(Follow::getToday)
                    )

            ; // 2-跟单中
            List<Follow> follows = followService.list(followWrapper);

            // 如果没有找到跟单记录，直接返回，不创建虚拟的跟单记录
            if (follows.isEmpty()) {
                System.out.println("未找到任何跟单记录，不创建跟单详情");
                return;
            }
            for (Follow follow : follows) {
                FollowDetail followDetail = new FollowDetail();
                followDetail.setFollowId(follow.getId());
                String orderNo = SnowIdUtil.getId(OrderConstant.followDetail);
                followDetail.setFollowNo(orderNo);
                followDetail.setFollowTradeId(dto.getId());
                followDetail.setUserId(follow.getUserId());
                followDetail.setMentorId(follow.getMentorId());
                followDetail.setPackageId(follow.getPackageId());
                followDetail.setStockCode(dto.getStockCode());
                followDetail.setStockName(dto.getStockName());
                followDetail.setAmount(follow.getAmount());
                followDetail.setPositionRate(follow.getPositionRate());
                //数量
                BigDecimal amount = follow.getAmount().add(follow.getAddAmount());
                Integer buyQuantity = amount.multiply(follow.getPositionRate()).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP).divide(buyPrice, 0, BigDecimal.ROUND_HALF_UP).intValue();
                followDetail.setBuyQuantity(buyQuantity);
                followDetail.setCurrentQuantity(buyQuantity);
                followDetail.setBuyPrice(buyPrice);
                BigDecimal buyAmount = new BigDecimal(buyQuantity).multiply(buyPrice);
                followDetail.setBuyAmount(buyAmount);
                followDetail.setBuyTime(dto.getBuyTime());
                followDetail.setSettlementTime(follow.getEndTime());
                followDetail.setPackageType(follow.getPackageDays() > 1 ? 2 : 1);
                followDetail.setStatus(1);
                followDetail.setSettlementStatus(0);
                followDetail.setMaxAmount(follow.getMaxAmount());
                followDetail.setMinAmount(follow.getMinAmount());
                followDetail.setSalaryRate(follow.getSalaryRate());
                follow.setToday(DateUtils.formatStart(new Date()));
                //跟单成功 要形成跟单信息
                followDetailService.save(followDetail);
                //需要跟单数据
                userService.addTodayFollowBuy(follow.getUserId());
            }
        } else {
            // 卖出操作，更新跟单详情
            detailWrapper.eq(FollowDetail::getFollowTradeId, dto.getId()) // 更新的时候只影响跟单中的订单
                    .eq(FollowDetail::getStatus, 1); // 1-跟单中，确保只更新持仓中的记录 跟单交易详情的 positionStatus 字段
            List<FollowDetail> followDetailList = followDetailService.list(detailWrapper);// 获取一条数据
            for (FollowDetail followDetail : followDetailList) {
                followDetail.setSellPrice(sellPrice);
                followDetail.setCurrentQuantity(0);
                followDetail.setSellAmount(BigDecimal.valueOf(followDetail.getBuyQuantity()).multiply(sellPrice));
                BigDecimal yiBai = new BigDecimal("100");
                BigDecimal eranBig = followDetail.getSellAmount().subtract(followDetail.getBuyAmount());
                followDetail.setSalary(eranBig.multiply(followDetail.getSalary()).divide(yiBai, 2, BigDecimal.ROUND_HALF_UP));
                followDetail.setProfit(followDetail.getSellAmount().subtract(followDetail.getBuyAmount()));
                followDetail.setSellQuantity(followDetail.getBuyQuantity());
                followDetail.setSellTime(dto.getSellTime());
                followDetail.setStatus(2);
                Integer followId = followDetail.getFollowId();
                Follow follow = followService.getById(followId);
                follow.setUsedDays(follow.getUsedDays() + 1);
                //用定是任务来做
//                if(follow.getUsedDays() >= follow.getPackageDays()) {
//                    follow.setStatus(5);
//                    followDetail.setStatus(1);
//                    followDetail.setSettlementTime(new Date());
//                }
                followService.update(follow);
                followDetailService.updateById(followDetail);
                //需要跟单数据
                userService.addTodayFollowSell(follow.getUserId(), followDetail.getProfit());
            }
        }
    }

    @Override
    public TradeRecordVO getTradingRecords(TradeRecordQueryDTO dto) {
        TradeRecordVO tradeRecordVO = new TradeRecordVO();
        Date startDate = DateUtils.addDateDaysStart(new Date(), -22);
        // 22天内的已清仓记录
        Integer mentorId = dto.getMentorId();
        LambdaQueryWrapper<FollowTrade> q = new LambdaQueryWrapper<>();
        q.eq(FollowTrade::getMentorId, mentorId);
        q.eq(FollowTrade::getStatus, 2); // 2-已清仓
        q.gt(FollowTrade::getCreateTime, startDate);
        List<FollowTrade> records = this.list(q);
        List<TradeDetailVO> voList = BeanCopyUtil.copyToList(records, TradeDetailVO.class);
        tradeRecordVO.setTotalProfit(
                voList.stream().map(TradeDetailVO::getProfitLossPercent).reduce(BigDecimal.ZERO, BigDecimal::add));
        tradeRecordVO.setTradeList(voList);
        return tradeRecordVO;
    }

    @Override
    public List<String> getMentorHoldingStocks(Integer mentorId) {
        if (mentorId == null) {
            return new ArrayList<>();
        }

        // 查询导师持仓中的股票
        LambdaQueryWrapper<FollowTrade> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FollowTrade::getMentorId, mentorId)
                .eq(FollowTrade::getStatus, 1) // 1-持仓中
                .select(FollowTrade::getStockCode, FollowTrade::getStockName);

        List<FollowTrade> list = this.list(wrapper);

        // 提取股票代码并去重
        return list.stream()
                .map(FollowTrade::getStockCode)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<FollowTrade> searchStocks(String keyword, Integer limit) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return new ArrayList<>();
        }

        // 设置默认限制数量
        if (limit == null || limit <= 0) {
            limit = 10;
        }

        // 构建查询条件
        LambdaQueryWrapper<FollowTrade> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(FollowTrade::getStockCode, keyword)
                .or()
                .like(FollowTrade::getStockName, keyword)
                .select(FollowTrade::getStockCode, FollowTrade::getStockName)
                .groupBy(FollowTrade::getStockCode) // 按股票代码分组，避免重复
                .last("LIMIT " + limit);

        // 直接返回查询结果
        return this.list(wrapper);
    }

    /**
     * 将Follow实体转换为VO
     */
    private FollowTradeVO convertToVO(FollowTrade followTrade) {
        if (followTrade == null) {
            return null;
        }

        FollowTradeVO vo = new FollowTradeVO();
        BeanUtils.copyProperties(followTrade, vo);

        // 设置状态描述
        if (followTrade.getStatus() != null) {
            switch (followTrade.getStatus()) {
                case 1:
                    vo.setStatusDesc("持仓中");
                    break;
                case 2:
                    vo.setStatusDesc("已清仓");
                    break;
                default:
                    vo.setStatusDesc("未知状态");
                    break;
            }
        }

        // 设置导师名称
        if (followTrade.getMentorId() != null) {
            try {
                Mentor mentor = mentorService.getById(followTrade.getMentorId());
                if (mentor != null) {
                    vo.setMentorName(mentor.getMentorName());
                }
            } catch (Exception e) {
                // 如果获取导师信息失败，不影响其他数据的返回
                System.err.println("获取导师信息失败: " + e.getMessage());
            }
        }

        return vo;
    }
}