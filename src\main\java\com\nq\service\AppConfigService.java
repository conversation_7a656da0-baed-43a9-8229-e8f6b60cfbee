package com.nq.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.pojo.AppConfig;
import com.nq.vo.app.AppConfigVO;

import java.util.List;

public interface AppConfigService extends IService<AppConfig> {
    
    /**
     * 分页查询配置列表
     */
    PageInfo<AppConfig> pageList(Integer pageNum, Integer pageSize, String groupName, String configName, String type);
    
    /**
     * 新增配置
     */
    void add(AppConfig appConfig);
    
    /**
     * 修改配置
     */
    void update(AppConfig appConfig);
    
    /**
     * 删除配置
     */
    void delete(Integer id);
    
    /**
     * 根据组名和配置名获取配置
     */
    AppConfig getByGroupAndName(String groupName, String configName);
    /**
     * 根据配置名获取配置
     */
    AppConfigVO queryConfig(String key);
    /**
     * 问题
     */
    List<AppConfigVO> queryGroupConfig(String groupName);
}