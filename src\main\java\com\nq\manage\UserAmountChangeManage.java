package com.nq.manage;

import cn.hutool.core.util.StrUtil;
import com.nq.enums.OrderTypeEnum;
import com.nq.enums.TypeEnum;
import com.nq.function.AmountConsumer;
import com.nq.service.UserAmountService;
import lombok.SneakyThrows;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;


@Component
public class UserAmountChangeManage {

    @Resource
    private RedissonClient redissonClient;


    @Resource
    private UserAmountService userAmountService;

    /**
     * 获取用户处理金额的锁
     * @param userId 用户ID
     * @return
     */
    public static String getLockStr(Integer userId){
        return StrUtil.format("follow:user:{}", userId);
    }

    @SneakyThrows
    public void changeBalance(Integer userId, BigDecimal amount, String orderNo, OrderTypeEnum orderTypeEnum , TypeEnum typeEnum, String remark, String operator, AmountConsumer amountConsumer) {
        RLock lock = redissonClient.getLock(getLockStr(userId));
        if (lock.tryLock(10, TimeUnit.SECONDS)) {
            try {
                userAmountService.changeAmount(userId, amount, orderNo, orderTypeEnum, typeEnum, remark, operator, amountConsumer);
            } finally {
                if (lock.isLocked()) {
                    lock.unlock();
                }
            }
        } else {
            throw new RuntimeException("请稍后再试 !");
        }

    }

    /** 这里用户结算跟单使用
     * @param userId
     * @param followNo
     * @param benji
     * @param totalEarn 利润
     * @param consumer
     */
    @SneakyThrows
    public void changeFollowBalance(Integer userId, String followNo, BigDecimal benji, BigDecimal totalEarn, AmountConsumer consumer) {
        RLock lock = redissonClient.getLock(getLockStr(userId));
        if (lock.tryLock(10, TimeUnit.SECONDS)) {
            try {
                 userAmountService.changeFollowBalance(userId, followNo,benji, totalEarn,consumer);
            } finally {
                if (lock.isLocked()) {
                    lock.unlock();
                }
            }
        } else {
            throw new RuntimeException("请稍后再试 !");
        }
    }
}
