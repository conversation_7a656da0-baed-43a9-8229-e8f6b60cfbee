<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nq.dao.SiteSmsLogMapper" >
  <resultMap id="BaseResultMap" type="com.nq.pojo.SiteSmsLog" >
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="sms_phone" property="smsPhone" jdbcType="VARCHAR"/>
    <result column="sms_title" property="smsTitle" jdbcType="VARCHAR"/>
    <result column="sms_cnt" property="smsCnt" jdbcType="VARCHAR"/>
    <result column="sms_template" property="smsTemplate" jdbcType="VARCHAR"/>
    <result column="sms_status" property="smsStatus" jdbcType="INTEGER"/>
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
  </resultMap>
  <sql id="Base_Column_List" >
    id, sms_phone, sms_title, sms_cnt, sms_template, sms_status, add_time
  </sql>

  <select id="smsList" resultMap="BaseResultMap" parameterType="string">
    SELECT
    <include refid="Base_Column_List"/>
    FROM site_sms_log
    <where>
      <if test="phoneNum != null and phoneNum != '' ">
        and sms_phone like CONCAT('%','${phoneNum}','%')
      </if>
    </where>
    order by id desc
  </select>


</mapper>