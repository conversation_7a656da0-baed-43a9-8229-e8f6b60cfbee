package com.nq.controller.app;

import com.nq.common.ServerResponse;
import com.nq.pojo.User;
import com.nq.service.ActivityService;
import com.nq.service.IUserService;
import com.nq.vo.app.AppActivityVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping({"/activity/"})
@Api(tags = "APP-任务中心接口")
public class AppActivityController {
    private static final Logger log = LoggerFactory.getLogger(AppActivityController.class);
    @Resource
    private ActivityService activityService;
    @Resource
    private IUserService iUserService;



    @ApiOperation(value = "查询日常队长任务", notes = "查询日常队长任务")
    @PostMapping({"queryActivityTeam.do"})
    public ServerResponse<AppActivityVO> queryActivityTeam(HttpServletRequest request) {
        User user = iUserService.getCurrentUser();
        if (user == null) {
            return ServerResponse.createBySuccessMsg("用户未登录");
        }
        return ServerResponse.createBySuccess(this.activityService.queryActivityTeam(user));
    }

    @ApiOperation(value = "查询日常邀请任务", notes = "查询日常邀请任务")
    @PostMapping({"queryActivityInvite.do"})
    public ServerResponse<AppActivityVO> queryActivityInvite( HttpServletRequest request) {
        User user = iUserService.getCurrentUser();
        if (user == null) {
            return ServerResponse.createBySuccessMsg("用户未登录");
        }
        return ServerResponse.createBySuccess(this.activityService.queryActivityInvite(user));
    }


}
