package com.nq.controller.admin;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.SiteNews;
import com.nq.service.ISiteNewsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/admin/news")
@Api(tags = {"后台-新闻资讯管理"})
public class AdminSiteNewsController {
    private static final Logger log = LoggerFactory.getLogger(AdminSiteNewsController.class);

    @Resource
    private ISiteNewsService iSiteNewsService;

    @ApiOperation("分页查询新闻资讯")
    @PostMapping("/list.do")
    public ServerResponse<PageInfo<SiteNews>> list(
            @ApiParam("页码") @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam("每页大小") @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @ApiParam("新闻类型") @RequestParam(value = "type", required = false) Integer type,
            @ApiParam("排序方式") @RequestParam(value = "sort", required = false) String sort,
            @ApiParam("关键词") @RequestParam(value = "keyword", required = false) String keyword,
            HttpServletRequest request) {
        PageInfo<SiteNews> pageInfo = iSiteNewsService.getList(pageNum, pageSize, type, sort, keyword, request);
        return ServerResponse.createBySuccess(pageInfo);
    }

    @ApiOperation("获取新闻资讯详情")
    @PostMapping("/detail.do")
    public ServerResponse<SiteNews> detail(
            @ApiParam("新闻ID") @RequestParam("id") int id,
            HttpServletRequest request) {
        SiteNews siteNews = iSiteNewsService.getDetail(id, request);
        if (siteNews != null) {
            return ServerResponse.createBySuccess(siteNews);
        }
        return ServerResponse.createByErrorMsg("未找到该新闻");
    }

    @ApiOperation("添加新闻资讯")
    @PostMapping("/add.do")
    public ServerResponse<String> add(SiteNews siteNews) {
        return iSiteNewsService.saveOne(siteNews);
    }

    @ApiOperation("修改新闻资讯")
    @PostMapping("/update.do")
    public ServerResponse<String> update(SiteNews siteNews) {
        if (siteNews.getId() == null) {
            return ServerResponse.createByErrorMsg("新闻ID不能为空");
        }
        return iSiteNewsService.saveOne(siteNews);
    }

    @ApiOperation("删除新闻资讯")
    @PostMapping("/delete.do")
    public ServerResponse<String> delete(@ApiParam("新闻ID") @RequestParam("id") Long id) {
        if (id == null) {
            return ServerResponse.createByErrorMsg("新闻ID不能为空");
        }
        SiteNews siteNews = new SiteNews();
        siteNews.setId(id);
        siteNews.setStatus(0); // 设置为停用状态
        int result = iSiteNewsService.update(siteNews);
        if (result > 0) {
            return ServerResponse.createBySuccessMsg("删除成功");
        }
        return ServerResponse.createByErrorMsg("删除失败");
    }

    @ApiOperation("抓取新闻资讯")
    @PostMapping("/grab.do")
    public ServerResponse<String> grab() {
        int count = iSiteNewsService.grabNews();
        return ServerResponse.createBySuccessMsg("成功抓取" + count + "条新闻");
    }
}