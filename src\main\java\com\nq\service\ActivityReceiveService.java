package com.nq.service;

import com.github.pagehelper.PageInfo;
import com.nq.dto.common.CommonPage;
import com.nq.pojo.Activity;
import com.nq.pojo.ActivityReceive;
import com.baomidou.mybatisplus.extension.service.IService;
public interface ActivityReceiveService extends IService<ActivityReceive>{


    PageInfo<ActivityReceive> listByAdmin(ActivityReceive activityReceive, CommonPage commonPage);
}
