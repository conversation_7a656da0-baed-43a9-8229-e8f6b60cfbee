package com.nq.controller.agent;


import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.service.IUserPositionService;
import com.nq.service.IUserService;
import com.nq.vo.position.UserPositionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RestController
@RequestMapping({"/agent/position/"})
@Api(tags = "代理后台-持仓管理")
public class AgentPositionController {
    private static final Logger log = LoggerFactory.getLogger(AgentPositionController.class);

    @Resource
    private IUserPositionService iUserPositionService;
    @Resource
    private IUserService iUserService;

    //分页查询持仓管理 融资持仓单信息/融资平仓单信息及模糊查询
    @PostMapping({"list.do"})
    @ApiOperation("分页查询持仓管理信息")
    public ServerResponse<PageInfo<UserPositionVO>> list(
            @RequestParam(value = "userId", required = false) Integer userId,
            @RequestParam(value = "stockCode", required = false) String stockCode,
            @RequestParam(value = "stockSpell", required = false) String stockSpell,
            @RequestParam(value = "state", required = false) Integer state,
            @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @RequestParam(value = "pageSize", defaultValue = "12") int pageSize) {
        //userId stockCode, stockSpell, state, pageNum, pageSize
        PageInfo<UserPositionVO> adminPositionVOPageInfo = this.iUserPositionService.findPositionByCodeByState(userId, stockCode, stockSpell,state, pageNum, pageSize, true);
        return ServerResponse.createBySuccess(adminPositionVOPageInfo);
    }


}

