package com.nq.vo.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(description = "跟单交易展示对象")
public class FollowTradeVO {
    
    @ApiModelProperty(value = "交易ID", example = "1")
    private Integer id;
    
    @ApiModelProperty(value = "交易单号", example = "FT202403010001")
    private String tradeNo;
    
    @ApiModelProperty(value = "导师ID", example = "1")
    private Integer mentorId;
    
    @ApiModelProperty(value = "导师名称", example = "张三")
    private String mentorName;
    
    @ApiModelProperty(value = "股票代码", example = "000001")
    private String stockCode;
    
    @ApiModelProperty(value = "股票名称", example = "平安银行")
    private String stockName;
    
    @ApiModelProperty(value = "买入价格", example = "10.50")
    private BigDecimal buyPrice;
    
    @ApiModelProperty(value = "卖出价格", example = "11.20")
    private BigDecimal sellPrice;
    
    @ApiModelProperty(value = "个股盈亏百分比", example = "6.67")
    private BigDecimal profitLossPercent;
    
    @ApiModelProperty(value = "买入时间")
    private Date buyTime;

    @ApiModelProperty(value = "卖出时间")
    private Date sellTime;
    
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    @ApiModelProperty(value = "发布时间")
    private Date publishTime;
    
    @ApiModelProperty(value = "状态(1-持仓中，2-已清仓)", example = "1")
    private Integer status;
    
    @ApiModelProperty(value = "状态描述", example = "持仓中")
    private String statusDesc;
} 