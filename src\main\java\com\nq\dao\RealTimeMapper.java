package com.nq.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nq.pojo.RealTime;

import java.util.List;

import org.apache.ibatis.annotations.*;

@Mapper
public interface RealTimeMapper extends BaseMapper<RealTime> {
    @Select({"select * from realtime where stockCode=#{stockCode}"})
    List<RealTime> findStock(String paramString);

    /*删除股票*/
    @Delete({"DELETE FROM realtime WHERE locate('hf_',`stockCode`)=0"})
    int deleteStockCode();

    /*删除期货*/
    @Delete({"DELETE FROM realtime WHERE locate('hf_',`stockCode`)=1"})
    int deleteStockFuturesCode();

    @Insert({"<script>", "insert into realtime (time,volumes,price,rates,averagePrice,amounts,stockCode) values ", "<foreach  collection='areaLists' item='realTime' index='index' separator=','>", "(#{realTime.time},#{realTime.volumes},#{realTime.price},#{realTime.rates},#{realTime.averagePrice},#{realTime.amounts},#{realTime.stockCode})", "</foreach>", "</script>"})
    int insertRealTime(@Param("areaLists") List<RealTime> paramList);
}
