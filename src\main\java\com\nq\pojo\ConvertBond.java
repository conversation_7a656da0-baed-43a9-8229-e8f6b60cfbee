package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;

/**
 * @TableName convert_bond
 */
@TableName(value ="convert_bond")
@Data
@ApiModel(description = "可转债信息")
@AllArgsConstructor
@NoArgsConstructor
public class ConvertBond extends BaseEntity implements Serializable {
    @TableId(type = IdType.AUTO,value = "id")
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "债券申购代码", example = "123456")
    private String bondBuyCode;

    @ApiModelProperty(value = "债券名称", example = "XX转债")
    private String bondName;

    @ApiModelProperty(value = "债券类型", example = "可转债")
    private String bondType;

    @ApiModelProperty(value = "债券代码", example = "123456")
    private String bondCode;

    @ApiModelProperty(value = "正股代码", example = "600000")
    private String stockCode;

    @ApiModelProperty(value = "价格", example = "100.00")
    private BigDecimal price;

    @ApiModelProperty(value = "申购日期", example = "2024-01-01")
    private Date applyDate;

    @ApiModelProperty(value = "发行日期", example = "2024-01-01")
    private Date pubDate;

    @ApiModelProperty(value = "上市日期", example = "2024-01-01")
    private Date listDate;

    @ApiModelProperty(value = "剩余数量", example = "1000000")
    private Integer surplus;

    @ApiModelProperty(value = "申购上限", example = "10000")
    private Integer applyLimit;

    @ApiModelProperty(value = "状态：0-未开始，1-申购中，2-已结束", example = "1")
    private Integer status;

    @ApiModelProperty(value = "创建时间", example = "2024-01-01 12:00:00")
    private Date createTime;

    private static final long serialVersionUID = 1L;
}