package com.nq.controller.app;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.User;
import com.nq.service.IUserPositionService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.nq.service.IUserService;
import com.nq.vo.position.UserPositionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping({"/user/position/"})
@Api(tags = "APP-持仓")
public class AppUserPositionController {
    private static final Logger log = LoggerFactory.getLogger(AppUserPositionController.class);

    @Resource
    private IUserPositionService iUserPositionService;
    @Resource
    private IUserService iUserService;

    //查询所有融资平仓/持仓信息
    @PostMapping({"list.do"})
    @ApiOperation("查询所有融资平仓/持仓信息")
    public ServerResponse<PageInfo<UserPositionVO>> list(@RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "10") int pageSize, @RequestParam(value = "state", required = false) Integer state, @RequestParam(value = "stockCode", required = false) String stockCode, @RequestParam(value = "stockSpell", required = false) String stockSpell) {
        User user = this.iUserService.getCurrentUser();
        PageInfo<UserPositionVO> myPositionByCodeAndSpell = iUserPositionService.findPositionByCodeByState(user.getId(),stockCode, stockSpell, state, pageNum, pageSize);
        return ServerResponse.createBySuccess(myPositionByCodeAndSpell);
    }


    //撤销挂单
    @PostMapping({"revocation.do"})
    @ApiOperation("撤销挂单")
    public ServerResponse<String> revocation(@RequestParam(value = "id") Integer id) {
        iUserPositionService.revocation(id);
        return ServerResponse.createBySuccess();
    }
}

