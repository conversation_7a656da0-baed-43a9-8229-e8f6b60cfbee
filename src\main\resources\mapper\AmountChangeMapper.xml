<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nq.dao.AmountChangeMapper">

    <resultMap id="BaseResultMap" type="com.nq.pojo.AmountChange">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="nick_name" property="nickName"/>
        <result column="order_no" property="orderNo"/>
        <result column="order_type" property="orderType"/>
        <result column="account_type" property="accountType"/>
        <result column="type" property="type"/>
        <result column="amount" property="amount"/>
        <result column="old_amount" property="oldAmount"/>
        <result column="new_amount" property="newAmount"/>
        <result column="remark" property="remark"/>
        <result column="operator" property="operator"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, nick_name, order_no, order_type, account_type,
        type, amount, old_amount, new_amount, remark, operator, create_time
    </sql>

</mapper> 