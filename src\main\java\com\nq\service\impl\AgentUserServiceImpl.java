package com.nq.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.common.satoken.StpAgentUtil;
import com.nq.dao.AgentUserMapper;
import com.nq.dto.common.CommonPage;
import com.nq.excepton.CustomException;
import com.nq.pojo.AgentUser;
import com.nq.service.IAgentUserService;
import com.nq.utils.SymmetricCryptoUtil;
import com.nq.vo.agent.AgentLoginResultVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;


@Service("iAgentUserService")
public class AgentUserServiceImpl extends ServiceImpl<AgentUserMapper, AgentUser> implements IAgentUserService {
    private static final Logger log = LoggerFactory.getLogger(AgentUserServiceImpl.class);


    @Resource
    private AgentUserMapper agentUserMapper;


    @Override
    public AgentUser getCurrentAgent() {
        Integer loginId = StpAgentUtil.getUserId();
        return this.agentUserMapper.selectById(loginId);
    }


    @Override
    public AgentLoginResultVO login(String agentPhone, String agentPwd, String verifyCode, HttpServletRequest request) {
        if (StringUtils.isBlank(agentPhone) || StringUtils.isBlank(agentPwd)) {
            throw new CustomException("参数不能为空");
        }
        agentPwd = SymmetricCryptoUtil.encryptPassword(agentPwd);
        AgentUser agentUser = this.agentUserMapper.login(agentPhone, agentPwd);
        if (agentUser == null) {
            throw new CustomException("用户密码不正确");
        }
        if (agentUser.getIsLock() == 1) {
            throw new CustomException("登陆失败，您的账号已被锁定！");
        }
        StpAgentUtil.login(agentUser.getId());
        StpAgentUtil.setAgentUserId(agentUser.getUserId());
        String tokenValue = StpAgentUtil.getTokenValue();
        AgentLoginResultVO vo = new AgentLoginResultVO();
        vo.setToken(tokenValue);
        return vo;
    }
    //String oldPwd, String newPwd

    @Override
    public void updatePwd(String oldPwd, String newPwd) {
        if (StringUtils.isBlank(oldPwd) || StringUtils.isBlank(newPwd)) {
            throw new CustomException("参数不能为空");
        }
        AgentUser agentUser = getCurrentAgent();
        if (agentUser == null) {
            throw new CustomException("請先登錄");
        }
        oldPwd = SymmetricCryptoUtil.encryptPassword(oldPwd);
        if (!oldPwd.equals(agentUser.getAgentPwd())) {
            throw new CustomException("密码错误");
        }
        agentUser.setAgentPwd(SymmetricCryptoUtil.encryptPassword(newPwd));
        int updateCount = this.agentUserMapper.updateById(agentUser);
        if (updateCount <= 0) {
            throw new CustomException("修改失败");
        }
    }


    @Override
    public PageInfo<AgentUser> listByAdmin(AgentUser agentUser, CommonPage commonPage) {
        PageHelper.startPage(commonPage.getPageNum(), commonPage.getPageSize());
        LambdaQueryWrapper<AgentUser> q = new LambdaQueryWrapper<>();
        this.buildQuery(q,agentUser);
        List<AgentUser> arr = this.list(q);
        return new PageInfo<AgentUser>(arr);
    }

    private void buildQuery(LambdaQueryWrapper<AgentUser> q, AgentUser agentUser) {
        q.like(StringUtils.isNotBlank(agentUser.getAgentPhone()), AgentUser::getAgentPhone, agentUser.getAgentPhone());
        q.like(StringUtils.isNotBlank(agentUser.getAgentName()), AgentUser::getAgentName, agentUser.getAgentName());
        q.like(StringUtils.isNotEmpty(agentUser.getUserRealName()), AgentUser::getUserRealName, agentUser.getUserRealName());
        q.like(StringUtils.isNotEmpty(agentUser.getUserNickName()), AgentUser::getUserNickName, agentUser.getUserNickName());
        q.eq(agentUser.getIsLock() != null, AgentUser::getIsLock, agentUser.getIsLock());
        q.orderByDesc(AgentUser::getUserId);

    }

    @Override
    public void add(AgentUser agentUser) {
        if (StringUtils.isBlank(agentUser.getAgentName()) || StringUtils.isBlank(agentUser.getAgentPhone())  || StringUtils.isBlank(agentUser.getAgentPwd())) {
            throw new CustomException("参数不能为空");
        }
        AgentUser nameAgent = this.findByName(agentUser.getAgentName());
        if (nameAgent != null) {
            throw new CustomException("代理账户已存在");
        }
        AgentUser userAgent =  this.findByUserId(agentUser.getUserId());
        if (userAgent != null) {
            throw new CustomException("用户已经是代理");
        }
        AgentUser dbAgent = new AgentUser();
        dbAgent.setAgentName(agentUser.getAgentName());
        dbAgent.setAgentPwd(SymmetricCryptoUtil.encryptPassword(agentUser.getAgentPwd()));
        dbAgent.setAgentPhone(agentUser.getAgentPhone());
        dbAgent.setUserId(agentUser.getUserId());
        dbAgent.setUserRealName(agentUser.getUserRealName());
        dbAgent.setUserNickName(agentUser.getUserNickName());
        dbAgent.setAddTime(new Date());
        dbAgent.setIsLock(0);
        int insertCount = this.agentUserMapper.insert(dbAgent);
        if (insertCount <= 0) {
            throw new CustomException("添加代理失败");
        }
    }

    private AgentUser findByUserId(Integer userId) {
        LambdaQueryWrapper<AgentUser> q = new LambdaQueryWrapper<>();
        q.eq(AgentUser::getUserId, userId);
        q.last("LIMIT 1");
        return this.getOne(q);
    }

    private AgentUser findByName(String agentName) {
        LambdaQueryWrapper<AgentUser> q = new LambdaQueryWrapper<>();
        q.eq(AgentUser::getAgentName, agentName);
        q.last("LIMIT 1");
        return this.getOne(q);
    }


    private AgentUser findByPhone(String agentPhone) {
        LambdaQueryWrapper<AgentUser> q = new LambdaQueryWrapper<>();
        q.eq(AgentUser::getAgentPhone, agentPhone);
        q.last("LIMIT 1");
        return this.getOne(q);
    }

    @Override
    public void update(AgentUser agentUser) {
        AgentUser userAgent = this.findByUserId(agentUser.getUserId());
        if(!userAgent.getId().equals(agentUser.getId())){
            throw new CustomException("绑定用户已经是代理");
        }

        AgentUser dbAgent = new AgentUser();
        dbAgent.setId(agentUser.getId());
        if (StringUtils.isNotBlank(agentUser.getAgentPwd())) {
            dbAgent.setAgentPwd(SymmetricCryptoUtil.encryptPassword(agentUser.getAgentPwd()));
        }


        if (agentUser.getIsLock() != null) {
            dbAgent.setIsLock(agentUser.getIsLock());
        }

        int updateCount = this.agentUserMapper.updateById(dbAgent);
        if (updateCount <= 0) {
            throw new CustomException("修改代理失败");
        }
    }


    /*删除代理*/
    @Override
    public void delAgent(Integer agentId) {
        AgentUser dbAgent = this.agentUserMapper.selectById(agentId);
        if (dbAgent == null) {
            throw new CustomException("代理不存在，请正常操作！");
        }
        int updateCount = this.agentUserMapper.deleteById(agentId);
        if (updateCount <= 0) {
            throw new CustomException("删除代理失败");
        }
    }
}

