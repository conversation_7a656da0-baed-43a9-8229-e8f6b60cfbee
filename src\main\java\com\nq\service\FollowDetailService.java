package com.nq.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.FollowDetail;
import com.nq.vo.admin.FollowAdminDetailVO;

import java.util.List;

public interface FollowDetailService extends IService<FollowDetail> {
    
    /**
     * 分页查询跟单详情列表
     */
    PageInfo<FollowAdminDetailVO>  pageList(Integer page, Integer size, String keyword, Integer status, Integer settleStatus);
    
    /**
     * 获取跟单详情
     */
    FollowAdminDetailVO getDetail(Integer id);
    
    /**
     * 创建跟单详情
     */
    ServerResponse<String> create(FollowDetail followDetail);
    
    /**
     * 更新跟单详情
     */
    ServerResponse<String> update(FollowDetail followDetail);
    
    /**
     * 结算跟单
     */
    ServerResponse<String> settle(Integer id);

}