package com.nq.service;

import cn.hutool.core.lang.Assert;
import com.nq.dao.AmountChangeMapper;
import com.nq.dao.UserMapper;
import com.nq.enums.AccountTypeEnum;
import com.nq.enums.TypeEnum;
import com.nq.enums.OrderTypeEnum;
import com.nq.function.AmountConsumer;
import com.nq.manage.UserTDto;
import com.nq.pojo.AmountChange;
import com.nq.pojo.User;
import com.nq.utils.Decimal;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

@Service
public class UserAmountService {
    @Resource
    private UserMapper userMapper;
    @Resource
    private AmountChangeMapper amountChangeMapper;

    @Transactional(rollbackFor = Exception.class)
    public UserTDto changeAmount(Integer userId, BigDecimal amount, String orderNo, OrderTypeEnum orderTypeEnum, TypeEnum typeEnum, String remark, String operator, AmountConsumer amountConsumer) {
//        RECHARGE(1, "充值"),
//                WITHDRAW(2, "提现"),
//                BUY_PING_STOCK(3, "买入委托"),
//                STOCK_BUY(4, "买入股票"),
//                RETURN_PING_STOCK(5, "委托退回"),
//                STOCK_SELL(6, "卖出股票"),
//                STOCK_REFUND(7, "股票退回"),
//                ADVISOR_APPLY(8, "申请投顾"),
//                ADVISOR_REJECT(9, "投顾驳回"),
//                ADVISOR_REFUND(10, "投顾退回"),
//                ADVISOR_PROFIT(11, "投顾收益"),
//                ADVISOR_APPEND(12, "投顾追加"),
//                ADVISOR_APPEND_REFUND(13, "投顾追加退回"),
//                STOCK_TO_BANK(14, "证转银"),
//                BANK_TO_STOCK(15, "银转证"),
//                TEAM_SALARY(16, "团队工资"),
//                BONUS(17, "奖金");
        //moneyType   1 平台账户, 2 证券账户
        Integer moneyType = 0;
        //当金额为超过4位的小数时,直接向下取整
        amount = amount.abs().setScale(2, BigDecimal.ROUND_HALF_DOWN);
        //金额不能为零
        Assert.isFalse(Decimal.of(amount).eq(BigDecimal.ZERO), "金额错误");
        User oldTUser = userMapper.selectById(userId);
        //账变后的用户信息
        User newTUser = new User();
        newTUser.setId(userId);
        newTUser.setUserAmt(oldTUser.getUserAmt());
        newTUser.setEnableAmt(oldTUser.getEnableAmt());
        newTUser.setNickName(oldTUser.getNickName());
        newTUser.setIsLock(oldTUser.getIsLock());
        //收入还是支出 1 收入, 2 支出
        //收入还是支出 1 收入, 2 支出
        Integer accountType = AccountTypeEnum.INCOME.getCode();
        //订单类型
        Boolean result = false;
        Integer type = typeEnum.getCode();
        Integer orderType = orderTypeEnum.getCode();
        //enable_amt
        if (Objects.equals(typeEnum, TypeEnum.RECHARGE)) {
            //充值
            newTUser.setUserAmt(newTUser.getUserAmt().add(amount));
            moneyType = 1;
            userMapper.addRechargeMoney(userId, amount);
        } else if (Objects.equals(typeEnum, TypeEnum.WITHDRAW)) {
            //提现
            accountType = AccountTypeEnum.EXPENSE.getCode();
            //判断金额是否充足
            if (Decimal.of(newTUser.getUserAmt()).lt(amount)) {
                throw new RuntimeException("用户余额不足");
            }
            newTUser.setUserAmt(newTUser.getUserAmt().subtract(amount));
            // 1 平台账户, 2 证券账户
            moneyType = 1;
            userMapper.addWithdrawMoney(userId, amount);
        } else if (Objects.equals(typeEnum, TypeEnum.BUY_PING_STOCK)) {
            //股票买入
            accountType = AccountTypeEnum.EXPENSE.getCode();
            if (Decimal.of(newTUser.getEnableAmt()).lt(amount)) {
                throw new RuntimeException("用户余额不足");
            }
            newTUser.setEnableAmt(newTUser.getEnableAmt().subtract(amount));
            // 1 平台账户, 2 证券账户
            moneyType = 2;
        } else if (Objects.equals(typeEnum, TypeEnum.STOCK_BUY)) {
            //股票买入
            accountType = AccountTypeEnum.EXPENSE.getCode();
            if (Decimal.of(newTUser.getEnableAmt()).lt(amount)) {
                throw new RuntimeException("用户余额不足");
            }
            newTUser.setEnableAmt(newTUser.getEnableAmt().subtract(amount));
            // 1 平台账户, 2 证券账户
            moneyType = 2;
        }
        if (Objects.equals(typeEnum, TypeEnum.RETURN_PING_STOCK)) {
            //股票
            newTUser.setEnableAmt(newTUser.getEnableAmt().add(amount));
            // 1 平台账户, 2 证券账户
            moneyType = 2;
        } else if (Objects.equals(typeEnum, TypeEnum.STOCK_SELL)) {
            //股票卖出
            newTUser.setEnableAmt(newTUser.getEnableAmt().add(amount));
            // 1 平台账户, 2 证券账户
            moneyType = 2;
        } else if (Objects.equals(typeEnum, TypeEnum.STOCK_REFUND)) {
            //股票退回
            newTUser.setEnableAmt(newTUser.getEnableAmt().add(amount));
            // 1 平台账户, 2 证券账户
            moneyType = 2;
        } else if (Objects.equals(typeEnum, TypeEnum.ADVISOR_APPLY)) {
            //申请投顾
            accountType = AccountTypeEnum.EXPENSE.getCode();
            if (Decimal.of(newTUser.getUserAmt()).lt(amount)) {
                throw new RuntimeException("用户余额不足");
            }
            newTUser.setUserAmt(newTUser.getUserAmt().subtract(amount));
            // 1 平台账户, 2 证券账户
            moneyType = 1;
        } else if (Objects.equals(typeEnum, TypeEnum.ADVISOR_REJECT)) {
            //投顾驳回
            newTUser.setUserAmt(newTUser.getUserAmt().add(amount));
            // 1 平台账户, 2 证券账户
            moneyType = 1;
        } else if (Objects.equals(typeEnum, TypeEnum.ADVISOR_REFUND)) {
            //投顾退回
            newTUser.setUserAmt(newTUser.getUserAmt().add(amount));
            // 1 平台账户, 2 证券账户
            moneyType = 1;
        } else if (Objects.equals(typeEnum, TypeEnum.ADVISOR_PROFIT)) {
            //投顾收益
            newTUser.setUserAmt(newTUser.getUserAmt().add(amount));
            // 1 平台账户, 2 证券账户
            moneyType = 1;
        } else if (Objects.equals(typeEnum, TypeEnum.ADVISOR_APPEND)) {
            //投顾追加
            accountType = AccountTypeEnum.EXPENSE.getCode();
            if (Decimal.of(newTUser.getUserAmt()).lt(amount)) {
                throw new RuntimeException("用户余额不足");
            }
            newTUser.setUserAmt(newTUser.getUserAmt().subtract(amount));
            // 1 平台账户, 2 证券账户
            moneyType = 1;
        } else if (Objects.equals(typeEnum, TypeEnum.ADVISOR_APPEND_REFUND)) {
            //投顾追加退回
            newTUser.setUserAmt(newTUser.getUserAmt().add(amount));
            // 1 平台账户, 2 证券账户
            moneyType = 1;
        } else if (Objects.equals(typeEnum, TypeEnum.STOCK_TO_BANK)) {
            //证转银
            accountType = AccountTypeEnum.INCOME.getCode();
            if (Decimal.of(newTUser.getEnableAmt()).lt(amount)) {
                throw new RuntimeException("用户证券账户余额不足");
            }
            newTUser.setUserAmt(newTUser.getUserAmt().add(amount));
            newTUser.setEnableAmt(newTUser.getEnableAmt().subtract(amount));
            // 1 平台账户, 2 证券账户
            moneyType = 3;

        } else if (Objects.equals(typeEnum, TypeEnum.BANK_TO_STOCK)) {
            //银转证
            accountType = AccountTypeEnum.EXPENSE.getCode();
            if (Decimal.of(newTUser.getUserAmt()).lt(amount)) {
                throw new RuntimeException("用户余额不足");
            }
            newTUser.setUserAmt(newTUser.getUserAmt().subtract(amount));
            newTUser.setEnableAmt(newTUser.getEnableAmt().add(amount));
            // 1 平台账户, 2 证券账户
            moneyType = 3;
        } else if (Objects.equals(typeEnum, TypeEnum.TEAM_SALARY)) {
            //团队工资
            newTUser.setUserAmt(newTUser.getUserAmt().add(amount));
            // 1 平台账户, 2 证券账户
            moneyType = 1;
        } else if (Objects.equals(typeEnum, TypeEnum.BONUS)) {
            //奖金
            newTUser.setUserAmt(newTUser.getUserAmt().add(amount));
            // 1 平台账户, 2 证券账户
            moneyType = 1;
        } else if (Objects.equals(typeEnum, TypeEnum.BACKUP)) {
            //申请投顾
            newTUser.setUserAmt(newTUser.getUserAmt().add(amount));
            // 1 平台账户, 2 证券账户
            moneyType = 1;
            userMapper.addRechargeMoney(userId, amount);
        }
        else if (Objects.equals(typeEnum, TypeEnum.BACKDOWN)) {
            //申请投顾
            accountType = AccountTypeEnum.EXPENSE.getCode();
            if (Decimal.of(newTUser.getUserAmt()).lt(amount)) {
                throw new RuntimeException("用户余额不足");
            }
            newTUser.setUserAmt(newTUser.getUserAmt().subtract(amount));
            // 1 平台账户, 2 证券账户
            moneyType = 1;
            userMapper.addWithdrawMoney(userId, amount);
        }else if (Objects.equals(typeEnum, TypeEnum.WITHDRAWBACK)) {
            //申请投顾
            newTUser.setUserAmt(newTUser.getUserAmt().add(amount));
            // 1 平台账户, 2 证券账户
            moneyType = 1;
        }
        //moneyType ==1 代表是证转银
        if (moneyType == 1||moneyType == 2) {
            AmountChange amountChange = new AmountChange();
            amountChange.setUserId(newTUser.getId());
            amountChange.setNickName(newTUser.getNickName());
            amountChange.setOrderNo(orderNo);
            amountChange.setOrderType(orderType);
            amountChange.setAccountType(accountType);
            amountChange.setAmountType(moneyType);
            amountChange.setType(type);
            amountChange.setAmount(amount);
            amountChange.setOldAmount(moneyType == 1 ? oldTUser.getUserAmt() : oldTUser.getEnableAmt());
            amountChange.setNewAmount(moneyType == 1 ? newTUser.getUserAmt() : newTUser.getEnableAmt());
            amountChange.setRemark(remark);
            amountChange.setOperator(operator);
            result = amountChangeMapper.insert(amountChange) > 0;
        }
        if (moneyType == 3) {
            // 1 平台账户, 2 证券账户
            AmountChange amountChange = new AmountChange();
            amountChange.setUserId(newTUser.getId());
            amountChange.setNickName(newTUser.getNickName());
            amountChange.setOrderNo(orderNo);
            amountChange.setOrderType(orderType);
            amountChange.setAccountType(accountType);
            amountChange.setAmountType(1);
            amountChange.setType(type);
            amountChange.setAmount(amount);
            amountChange.setOldAmount(oldTUser.getUserAmt());
            amountChange.setNewAmount(newTUser.getUserAmt());
            amountChange.setRemark(remark);
            amountChange.setOperator(operator);
            Boolean  result1 = amountChangeMapper.insert(amountChange) > 0;
            AmountChange amountChange2 = new AmountChange();
            amountChange2.setUserId(newTUser.getId());
            amountChange2.setNickName(newTUser.getNickName());
            amountChange2.setOrderNo(orderNo);
            amountChange2.setOrderType(orderType);
            amountChange2.setAccountType(accountType);
            amountChange2.setAmountType(2);
            amountChange2.setType(type);
            amountChange2.setAmount(amount);
            amountChange2.setOldAmount(oldTUser.getEnableAmt());
            amountChange2.setNewAmount(newTUser.getEnableAmt());
            amountChange2.setRemark(remark);
            amountChange2.setOperator(operator);
            Boolean  result2 = amountChangeMapper.insert(amountChange2) > 0;
            result = result2&result1;
        }
        //添加账变
        if (result) {
            //添加账变日志
            result = userMapper.updateById(newTUser) > 0;
            if (result) {

            } else {
                throw new RuntimeException("账变未成功 !");
            }
            if (amountConsumer != null) {
                amountConsumer.accept(oldTUser, newTUser);
            }
            //执行之后的处理逻辑
            UserTDto userTwoDto = new UserTDto();
            userTwoDto.setNewUser(newTUser);
            userTwoDto.setOldUser(oldTUser);
            return userTwoDto;
        } else {
            throw new RuntimeException("账变未成功 !");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void changeFollowBalance(Integer userId, String followNo, BigDecimal benji, BigDecimal total, AmountConsumer amountConsumer) {

        benji = benji.abs().setScale(2, BigDecimal.ROUND_HALF_DOWN);
        total = total.abs().setScale(2, BigDecimal.ROUND_HALF_DOWN);
        //金额不能为零
        User oldTUser = userMapper.selectById(userId);
        //账变后的用户信息
        User newTUser = new User();
        BeanUtils.copyProperties(oldTUser, newTUser);
        //收入还是支出 1 收入, 2 支出
        Integer accountType = AccountTypeEnum.INCOME.getCode();
        //订单类型
        Boolean resultOne = false;
        Boolean resultTwo = false;
        Integer typeOne = TypeEnum.ADVISOR_REFUND.getCode();
        Integer typeTwo = TypeEnum.ADVISOR_PROFIT.getCode();
        Integer orderType = OrderTypeEnum.INVESTMENT_ADVISOR.getCode();
        newTUser.setEnableAmt(newTUser.getEnableAmt().add(benji));
        //enable_amt
        AmountChange amountChangeOne = new AmountChange();
        amountChangeOne.setUserId(newTUser.getId());
        amountChangeOne.setNickName(newTUser.getNickName());
        amountChangeOne.setOrderNo(followNo);
        amountChangeOne.setOrderType(orderType);
        amountChangeOne.setAccountType(accountType);
        amountChangeOne.setType(typeOne);
        amountChangeOne.setAmount(benji);
        amountChangeOne.setOldAmount(oldTUser.getEnableAmt());
        amountChangeOne.setNewAmount(newTUser.getEnableAmt());
        amountChangeOne.setRemark("");
        amountChangeOne.setOperator("");
        resultOne = amountChangeMapper.insert(amountChangeOne) > 0;

        newTUser.setEnableAmt(newTUser.getEnableAmt().add(total));
        AmountChange amountChangeTwo = new AmountChange();
        amountChangeTwo.setUserId(newTUser.getId());
        amountChangeTwo.setNickName(newTUser.getNickName());
        amountChangeTwo.setOrderNo(followNo + "SY");
        amountChangeTwo.setOrderType(orderType);
        amountChangeTwo.setAccountType(accountType);
        amountChangeTwo.setType(typeTwo);
        amountChangeTwo.setAmount(total);
        amountChangeTwo.setOldAmount(oldTUser.getEnableAmt());
        amountChangeTwo.setNewAmount(newTUser.getEnableAmt());
        amountChangeTwo.setRemark("");
        amountChangeTwo.setOperator("");
        resultTwo = amountChangeMapper.insert(amountChangeTwo) > 0;
        //添加账变
        Boolean resultThree = false;
        if (resultOne && resultTwo) {
            //添加账变日志
            resultThree = userMapper.updateById(newTUser) > 0;
            if (resultThree) {

            } else {
                throw new RuntimeException("账变未成功 !");
            }
            //执行之后的处理逻辑
            amountConsumer.accept(oldTUser, newTUser);
            UserTDto userTwoDto = new UserTDto();
            userTwoDto.setNewUser(newTUser);
            userTwoDto.setOldUser(oldTUser);
        } else {
            throw new RuntimeException("账变未成功 !");
        }


    }
}
