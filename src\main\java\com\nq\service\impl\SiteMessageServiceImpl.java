package com.nq.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.UpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import com.nq.common.ServerResponse;
import com.nq.dao.SiteLoginLogMapper;
import com.nq.dao.SiteMessageMapper;
import com.nq.dao.UserMapper;
import com.nq.excepton.CustomException;
import com.nq.pojo.SiteLoginLog;
import com.nq.pojo.SiteMessage;
import com.nq.pojo.SiteSetting;
import com.nq.pojo.User;
import com.nq.service.ISiteMessageService;
import com.nq.service.IUserService;
import com.nq.vo.agent.AgentAgencyFeeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * site_message
 *
 * <AUTHOR>
 * @date 2020/07/16
 */
@Service("ISiteMessageService")
public class SiteMessageServiceImpl  extends ServiceImpl<SiteMessageMapper, SiteMessage> implements ISiteMessageService {

    @Resource
    private SiteMessageMapper siteMessageMapper;

    @Resource
    private  IUserService iUserService;

    @Resource
    private UserMapper userMapper;
    

    @Override
    public int insert(SiteMessage siteMessage) {
        int ret = 0;
        if (siteMessage == null) {
            return 0;
        }

        ret = siteMessageMapper.insert(siteMessage);
        return ret;
    }

    @Override
    public int update(SiteMessage siteMessage) {
        int ret = siteMessageMapper.updateById(siteMessage);
        return Math.max(ret, 0);
    }

    @Override
    public int delete(int id) {
        int ret = siteMessageMapper.deleteById(id);
        return Math.max(ret, 0);
    }

    /*查询用户站内消息列表
     * */
    @Override
    public PageInfo<SiteMessage> getSiteMessageByUserIdList(int pageNum, int pageSize, int userId, HttpServletRequest request) {
        PageHelper.startPage(pageNum, pageSize);
        int uid = 0;
        //总后台查所有,其他人走登录信息userid
        if (userId != 999) {
            User user = this.iUserService.getCurrentUser();
            if (user == null) {
                throw new CustomException("請先登錄");
            }
            uid = user.getId();
        }
        List<SiteMessage> siteMessageList = this.siteMessageMapper.getSiteMessageByUserIdList(uid);
        List<SiteMessage> list = new ArrayList<>();
        //客户端端只查询客户端站内信，管理后台查询所有的站内信
        if (userId != 999) {
            for (SiteMessage siteMessage : siteMessageList) {
                if (siteMessage.getMessageType()!=null&&siteMessage.getMessageType() == SiteMessage.MSG_TYPE_WAP) {
                    list.add(siteMessage);
                }
            }
        } else {
            list = siteMessageList;
        }
        PageInfo<SiteMessage> pageInfo = new PageInfo<SiteMessage>(list);
        pageInfo.setList(list);
        return pageInfo;
    }

    @Override
    public int getIsDayCount(Integer userId, String typeName) {
        int ret = siteMessageMapper.getIsDayCount(userId, typeName);
        return Math.max(ret, 0);
    }

    @Override
    public void updateMessageStatus(HttpServletRequest request) {
        User user = this.iUserService.getCurrentUser();
        if (user == null) {
            return;
        }
        int ret = siteMessageMapper.updateMessageStatus(user.getId());
    }

    @Override
    public int getUnreadCount(HttpServletRequest request) {
        User user = this.iUserService.getCurrentUser();
        if (user == null) {
            return 0;
        }
        int ret = siteMessageMapper.getUnreadCount(user.getId());
        return Math.max(ret, 0);
    }

    @Override
    public void del(Integer id, HttpServletRequest request) {
        if (id == null) {
            throw new CustomException("id不能为空");
        }
        int updateCount = this.siteMessageMapper.deleteById(id);
        if (updateCount <= 0) {
            throw new CustomException("删除失败");
        }
    }

    @Override
    public void updateNoticeStatus(Integer noticeId, HttpServletRequest request) {
        SiteMessage siteMessage = this.siteMessageMapper.load(noticeId);
        if (siteMessage == null) {
            return;
        }
        siteMessage.setStatus(2);
        this.siteMessageMapper.updateById(siteMessage);
    }

    @Override
    public void createSiteMessage(String userIds, String type, String content, Integer messageType, HttpServletRequest request) {
        if (StringUtil.isEmpty(userIds)) {
            throw new CustomException("请选择用户");
        }
        String[] ids = userIds.split(",");
        for (String id : ids) {
            User user = this.userMapper.selectById(Integer.parseInt(id));
            if (user == null) {
                throw new CustomException("用户不存在");
            }
            SiteMessage siteMessage = new SiteMessage();
            siteMessage.setUserId(Integer.parseInt(id));
            siteMessage.setUserName(user.getRealName());
            siteMessage.setTypeName(type);
            siteMessage.setContent(content);
            siteMessage.setMessageType(messageType);
            siteMessage.setAddTime(new Date());
            this.siteMessageMapper.insert(siteMessage);
        }
    }

    @Override
    public void readAll() {
        User currentUser = iUserService.getCurrentUser();
        LambdaUpdateWrapper<SiteMessage> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SiteMessage::getUserId, currentUser.getId()).set(SiteMessage::getStatus, 2);
        this.update(updateWrapper);
    }

}
