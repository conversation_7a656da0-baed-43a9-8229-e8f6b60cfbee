package com.nq.controller.app;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.AppConfig;
import com.nq.pojo.User;
import com.nq.pojo.UserWithdraw;
import com.nq.service.AppConfigService;
import com.nq.service.IUserService;
import com.nq.service.IUserWithdrawService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


@RestController
@RequestMapping({"/user/invite/"})
@Api(tags = "APP-邀请链接")
public class AppInviteController {
    private static final Logger log = LoggerFactory.getLogger(AppInviteController.class);

    @Resource
    private AppConfigService appConfigService;

    /**
     * 获取二维码
     */
    @GetMapping("/inviteCode")
    @ApiOperation("获取二维码邀请链接")
    public ServerResponse<String> inviteCode() {
        LambdaQueryWrapper<AppConfig> q = new LambdaQueryWrapper<>();
        q.eq(AppConfig::getConfigName, "二维码分享域名");
        AppConfig one = appConfigService.getOne(q);
        return ServerResponse.createBySuccess(one.getConfigValue());
    }


}