package com.nq.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dao.FollowMapper;
import com.nq.dto.app.FollowAppendDTO;
import com.nq.dto.app.FollowRecordDetailQueryDTO;
import com.nq.dto.app.FollowRecordQueryDTO;
import com.nq.enums.OrderTypeEnum;
import com.nq.enums.TypeEnum;
import com.nq.function.AmountConsumer;
import com.nq.manage.UserAmountChangeManage;
import com.nq.pojo.*;
import com.nq.pojo.Package;
import com.nq.service.*;
import com.nq.utils.DateUtils;
import com.nq.utils.PageUtil;
import com.nq.vo.admin.FollowAppendVO;
import com.nq.vo.admin.FollowVO;

import com.nq.vo.app.FollowDetailVO;
import com.nq.vo.app.FollowPositionVO;
import com.nq.vo.app.FollowRecordVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.text.SimpleDateFormat;

@Service
public class FollowServiceImpl extends ServiceImpl<FollowMapper, Follow> implements FollowService {

    @Resource
    private IUserService userService;
    @Resource
    private MentorService mentorService;
    @Resource
    private PackageService packageService;
    @Resource
    private FollowDetailService followDetailService;
    @Resource
    private UserAmountChangeManage userAmountChangeManage;
    @Resource
    private FollowAppendService followAppendService;

    @Override
    public PageInfo<FollowVO> pageList(Integer page, Integer size, Integer status,
                                       Integer addStatus, String keyword, String beginTime, String endTime) {

        List<Integer> userIdsToFilter = Collections.emptyList();
        if (StrUtil.isNotBlank(keyword)) {
            LambdaQueryWrapper<User> userWrapper = new LambdaQueryWrapper<>();
            userWrapper.like(User::getPhone, keyword)
                    .or().like(User::getRealName, keyword)
                    .or().like(User::getNickName, keyword);
            List<User> matchingUsers = userService.list(userWrapper);
            userIdsToFilter = matchingUsers.stream().map(User::getId).collect(Collectors.toList());
        }
        PageHelper.startPage(page, size);
        LambdaQueryWrapper<Follow> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(status != null, Follow::getStatus, status)
                .eq(addStatus != null, Follow::getIsAdd, addStatus)
                .ge(StrUtil.isNotBlank(beginTime), Follow::getCreateTime, beginTime)
                .le(StrUtil.isNotBlank(endTime), Follow::getCreateTime, endTime)
                .in(!userIdsToFilter.isEmpty(), Follow::getUserId, userIdsToFilter)
                .orderByDesc(Follow::getCreateTime);

        List<Follow> list = this.list(wrapper);
        List<FollowVO> voList = list.stream().map(this::convertToVO).collect(Collectors.toList());
        PageInfo<FollowVO> voPageInfo = PageUtil.buildPageDto(list, voList);
        return voPageInfo;
    }

    @Override
    public FollowVO getDetailById(Integer id) {
        Follow follow = this.getById(id);
        if (follow == null) {
            return null;
        }
        return convertToVO(follow);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(Integer id, Integer status) {
        Follow follow = this.getById(id);
        if (follow == null) {
            throw new RuntimeException("跟单申请不存在");
        }

        follow.setAuditTime(new Date());
        // 如果审核通过，更新状态为跟单中
        if (status == 2) {
            follow.setStatus(status);
            this.updateById(follow);
        }
        // 如果审核拒绝，更新状态为被驳回
        else if (status == 3) {
            //并且给他还原钱
            LambdaUpdateWrapper<Follow> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(Follow::getId, id);
            updateWrapper.set(Follow::getStatus, status);
            updateWrapper.set(Follow::getEndTime, null);

            AmountConsumer amountConsumer = (oldUser, newUser) -> {
                this.update(updateWrapper);
            };
            // 还原钱
            userAmountChangeManage.changeBalance(follow.getUserId(), follow.getAmount(), follow.getFollowNo(), OrderTypeEnum.INVESTMENT_ADVISOR, TypeEnum.ADVISOR_REFUND, "", "", amountConsumer);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditAdd(Integer id, Integer status) {
        Follow follow = this.getById(id);
        Integer isAdd = follow.getIsAdd();

        if(isAdd != 1){
            return;
        }
        follow.setIsAdd(0);
        LambdaQueryWrapper<FollowAppend> q = new LambdaQueryWrapper<>();
        q.eq(FollowAppend::getFollowId, id);
        q.last("limit 1");
        FollowAppend followAppend = followAppendService.getOne(q);
        // 如果审核通过，
        if (status == 2) {

            follow.setAddAmount(follow.getAddAmount().add(followAppend.getAmount()));
            followAppend.setStatus(status);
            followAppendService.updateById(followAppend);
            this.updateById(follow);
        }
        // 如果审核拒绝，更新状态为被驳回
        else if (status == 3) {
            //并且给他还原钱
            LambdaUpdateWrapper<Follow> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(Follow::getId, id);
            updateWrapper.set(Follow::getStatus, status);
            updateWrapper.set(Follow::getEndTime, null);
            AmountConsumer amountConsumer = (oldUser, newUser) -> {
                followAppend.setStatus(status);
                followAppendService.updateById(followAppend);
                this.update(updateWrapper);
            };
            // 还原钱
            userAmountChangeManage.changeBalance(follow.getUserId(), followAppend.getAmount(), follow.getFollowNo(), OrderTypeEnum.INVESTMENT_ADVISOR, TypeEnum.ADVISOR_REFUND, "", "", amountConsumer);
        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createFollow(Follow follow) {
        follow.setFollowNo(generateFollowNo());
        follow.setApplyTime(new Date());
        follow.setStatus(1);
        follow.setIsAdd(0);
        follow.setUsedDays(0);
        follow.setCreateTime(new Date());
        follow.setUpdateTime(new Date());
        // 设置今日日期
        follow.setToday(DateUtils.formatStart(new Date()));

        this.save(follow);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelFollow(User user, Integer id) {
        // 1. 查询跟单记录
        LambdaQueryWrapper<Follow> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Follow::getId, id)
                .eq(Follow::getUserId, user.getId());
        Follow follow = this.getOne(wrapper);

        if (follow == null) {
            throw new RuntimeException("跟单记录不存在");
        }

        // 2. 检查状态是否允许撤单
        if (follow.getStatus() != 1) { // 假设1是待审核状态
            throw new RuntimeException("当前状态不允许撤单");
        }
        AmountConsumer consumer = (oldUser, newUser) -> {
            // 3. 更新状态为已撤销
            follow.setStatus(4); // 假设4是已撤销状态
            follow.setUpdateTime(new Date());
            this.updateById(follow);
        };
        userAmountChangeManage.changeBalance(user.getId(), follow.getAmount(), follow.getFollowNo(), OrderTypeEnum.INVESTMENT_ADVISOR, TypeEnum.ADVISOR_REFUND, "", "", consumer);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void appendFollow(User user, FollowAppendDTO dto) {
        // 1. 查询跟单记录
        LambdaQueryWrapper<Follow> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Follow::getId, dto.getId())
                .eq(Follow::getUserId, user.getId());
        Follow follow = this.getOne(wrapper);

        if (follow == null) {
            throw new RuntimeException("跟单记录不存在");
        }
        //只有跟单中才可以追加
        if (follow.getStatus() != 2) { // 假设2是跟单中状态
            throw new RuntimeException("当前状态不允许追加");
        }

        // 2. 检查是否已经有追加记录
        if (follow.getIsAdd() == 1) {
            throw new RuntimeException("该跟单已有追加记录，不能重复追加");
        }
        // 4. 更新追加状态
        follow.setIsAdd(1);
        follow.setUpdateTime(new Date());
        AmountConsumer consumer = (oldUser, newUser) -> {
            follow.setUpdateTime(new Date());
            //同时网这个追加记录里放记录
            FollowAppend followAppend = new FollowAppend();
            followAppend.setFollowId(dto.getId());
            followAppend.setFollowNo(follow.getFollowNo());
            followAppend.setAmount(dto.getAmount());
            followAppend.setStatus(1);
            followAppend.setUserId(follow.getUserId());
            followAppendService.save(followAppend);
            this.updateById(follow);
        };
        userAmountChangeManage.changeBalance(user.getId(), follow.getAmount(), follow.getFollowNo(), OrderTypeEnum.INVESTMENT_ADVISOR, TypeEnum.ADVISOR_APPEND, "", "", consumer);
    }

    @Override
    public void update(Follow follow) {
        this.updateById(follow);
    }

    @Override
    public PageInfo<FollowRecordVO> queryFollowRecord(User user, FollowRecordQueryDTO dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        // 构建查询条件
        LambdaQueryWrapper<Follow> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Follow::getUserId, user.getId());

        // 如果状态不为空，添加状态条件
        if (StrUtil.isNotBlank(dto.getStatus())) {
            wrapper.eq(Follow::getStatus, Integer.parseInt(dto.getStatus()));
        }

        // 按创建时间倒序排序
        wrapper.orderByDesc(Follow::getCreateTime);

        // 查询数据
        List<Follow> follows = this.list(wrapper);

        // 转换为VO
        List<FollowRecordVO> voList = follows.stream().map(follow -> {
            FollowRecordVO vo = new FollowRecordVO();
            vo.setId(follow.getId());
            vo.setFollowNo(follow.getFollowNo());
            vo.setAmount(follow.getAmount());
            vo.setAddAmount(follow.getAddAmount());
            vo.setSalaryRate(follow.getSalaryRate());
            this.convertToFollowRecordVO(follow,vo);
            vo.setApplyTime(follow.getApplyTime());
            vo.setEndTime(follow.getEndTime());
            vo.setStatus(follow.getStatus());
            if(vo.getStatus()==2){
                vo.setIsAdd(1);
            }


            // 获取用户信息
            User investor = userService.getById(follow.getUserId());
            if (investor != null) {
                vo.setInvestorName(investor.getNickName());
            }

            // 获取套餐信息
            if (follow.getPackageId() != null) {
                Package pack = packageService.getById(follow.getPackageId());
                if (pack != null) {
                    vo.setPackageName(pack.getProductName());
                }else {
                    vo.setPackageName("普通跟单");
                }
            }else {
                vo.setPackageName("普通跟单");
            }

            return vo;
        }).collect(Collectors.toList());

        // 构建分页信息
        PageInfo<Follow> pageInfo = new PageInfo<>(follows);
        PageInfo<FollowRecordVO> voPageInfo = new PageInfo<>(voList);
        BeanUtils.copyProperties(pageInfo, voPageInfo, "list");
        voPageInfo.setList(voList);

        return voPageInfo;
    }
    public void  convertToFollowRecordVO(Follow follow,FollowRecordVO vo){
        vo.setSalaryRate(follow.getSalaryRate().multiply(BigDecimal.TEN).multiply(BigDecimal.TEN));
    }

    @Override
    public FollowDetailVO queryFollowRecordDetail(User user, FollowRecordDetailQueryDTO dto) {
        // 1. 查询基本信息
        LambdaQueryWrapper<Follow> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Follow::getId, dto.getId())
                .eq(Follow::getUserId, user.getId());
        Follow follow = this.getOne(wrapper);

        if (follow == null) {
            return null;
        }

        // 2. 组装VO对象
        FollowDetailVO vo = new FollowDetailVO();
        vo.setFollowNo(follow.getFollowNo());
        vo.setAmount(follow.getAmount());
        vo.setAddAmount(follow.getAddAmount() != null ? follow.getAddAmount() : BigDecimal.ZERO);
        LambdaQueryWrapper<FollowDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(FollowDetail::getFollowId, follow.getId())
                .orderByDesc(FollowDetail::getCreateTime);
        List<FollowDetail> all = followDetailService.list(detailWrapper);
        // 4. 使用PageHelper进行分页
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        // 3. 查询持仓信息


        List<FollowDetail> details = followDetailService.list(detailWrapper);

        // 5. 转换为VO列表
        List<FollowPositionVO> positionVOs = details.stream().map(detail -> {
            FollowPositionVO positionVO = new FollowPositionVO();
            positionVO.setStockName(detail.getStockName());
            positionVO.setStockCode(detail.getStockCode());
            positionVO.setBuyPrice(detail.getBuyPrice());
            positionVO.setBuyQuantity(detail.getBuyQuantity());
            positionVO.setBuyTime(detail.getBuyTime());
            positionVO.setSellPrice(detail.getSellPrice());
            positionVO.setSellTime(detail.getSellTime());
            //收益
            BigDecimal profit = detail.getProfit();
            //购买单价
            BigDecimal buyAmountBig = detail.getBuyAmount();
            //购买数量
            BigDecimal divide = profit.divide(buyAmountBig, 4, BigDecimal.ROUND_HALF_DOWN).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN);
            //收益比
            positionVO.setProfitRate(divide);
            positionVO.setProfit(profit);
            positionVO.setSalary(detail.getSalary());
            BigDecimal brokerage = positionVO.getSalary();

            // 计算净利润和总利润
            if (detail.getStatus()==2) {
                BigDecimal netProfit = profit.subtract(brokerage);
                positionVO.setNetProfit(netProfit);

            }

            return positionVO;
        }).collect(Collectors.toList());

        // 6. 创建分页对象
        PageInfo<FollowDetail> pageInfo = new PageInfo<>(details);
        PageInfo<FollowPositionVO> positionPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, positionPageInfo, "list");
        positionPageInfo.setList(positionVOs);

        // 7. 设置分页结果
        vo.setFollowPositionVO(positionPageInfo);

        // 8. 计算总收益率和总盈亏
        BigDecimal totalProfit = all.stream()
                .map(detail -> {
                    if (detail == null) {
                        return BigDecimal.ZERO;
                    }
                    BigDecimal profit = detail.getProfit() != null ? detail.getProfit() : BigDecimal.ZERO;
                    BigDecimal salary = detail.getSalary() != null ? detail.getSalary() : BigDecimal.ZERO;
                    return profit.subtract(salary);
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        vo.setTotalProfit(totalProfit);
        final BigDecimal[] totalProfitRate = {BigDecimal.ZERO};
        // 计算总收益率

        if (follow.getAmount().compareTo(BigDecimal.ZERO) > 0) {
            all.stream().forEach(e -> {
                BigDecimal profit = e.getProfit();
                BigDecimal salary = e.getSalary();
                BigDecimal netProfit = profit.subtract(salary);
                //购买单价
                BigDecimal buyAmountBig = e.getBuyAmount();
                //购买数量
                BigDecimal divide = netProfit.divide(buyAmountBig, 4, BigDecimal.ROUND_HALF_DOWN).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN);
                totalProfitRate[0] = totalProfitRate[0].add(divide);
            });
            vo.setTotalProfitRate(totalProfitRate[0]);
        }

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stopFollow(Integer id) {
        Follow follow = this.getById(id);
        if (follow == null) {
            throw new RuntimeException("跟单记录不存在");
        }

        // 检查状态是否允许中止，例如只允许在 "跟单中" (status=2) 的状态下中止
        if (follow.getStatus() != 2) {
            // 这里可以根据具体业务需求调整允许中止的状态
             throw new RuntimeException("当前状态不允许中止");
        }
        // 更新状态为已中止 (假设状态码 6 代表已中止)
        follow.setStatus(6);
        follow.setStopTime(new Date()); // 记录中止时间
        follow.setUpdateTime(new Date());
        //这个先不学 hyh
        // TODO: 根据业务需求，这里可能需要添加额外的逻辑
        // 1. 清算用户持仓 (调用 followDetailService 相关方法)
        // 2. 退还剩余资金 (调用 userAmountChangeManage 相关方法)
        // 3. 发送通知等

        this.updateById(follow);
    }

    @Override
    public FollowAppendVO queryAppendDetail(Integer id) {
        // 1. 查询跟单记录
        LambdaQueryWrapper<FollowAppend> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FollowAppend::getFollowId, id)
                .eq(FollowAppend::getStatus, 1);
        wrapper.last("limit 1");
        FollowAppend one = followAppendService.getOne(wrapper);
        FollowAppendVO vo = new FollowAppendVO();
        if(one!=null){
            vo.setAmount(one.getAmount());
            vo.setCreateTime(one.getCreateTime());
            return vo;
        }
        return vo;
    }

    @Override
    public Long countApplyFollowNumByDate(int type) {
        HashMap<String, Date> todayMap = DateUtils.changeTimeSection(type);
        Date startTime =  todayMap.get("startTime");
        Date endTime =   todayMap.get("endTime");
        LambdaQueryWrapper<Follow> q = new LambdaQueryWrapper<>();
        q.between(Follow::getApplyTime, startTime, endTime);
        return this.count(q);
    }

    @Override
    public BigDecimal countApplyFollowAmountByDate(int type) {
        HashMap<String, Date> todayMap = DateUtils.changeTimeSection(type);
        Date startTime =  todayMap.get("startTime");
        Date endTime =   todayMap.get("endTime");
        LambdaQueryWrapper<Follow> q = new LambdaQueryWrapper<>();
        q.between(Follow::getApplyTime, startTime, endTime);
        List<Follow> arr = this.list(q);
        BigDecimal amount = arr.stream().map(Follow::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal addMoney = arr.stream().map(Follow::getAddAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        return amount.add(addMoney);
    }

    @Override
    public Long countAppendFollowPendingCount() {
        LambdaQueryWrapper<Follow> q = new LambdaQueryWrapper<>();
        q.eq(Follow::getIsAdd, 1);
        return this.count(q);
    }

    @Override
    public Long getPendingAddCount() {
        LambdaQueryWrapper<Follow> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Follow::getIsAdd, 1);
        return this.count(wrapper);
    }

    /**
     * 将Follow实体转换为VO
     */
    private FollowVO convertToVO(Follow follow) {
        FollowVO vo = new FollowVO();
        BeanUtils.copyProperties(follow, vo);

        // 设置会员信息
        User user = userService.getById(follow.getUserId());
        if (user != null) {
            FollowVO.UserInfo userInfo = new FollowVO.UserInfo();
            userInfo.setUserAccount(user.getNickName()); // 使用昵称作为账号
            userInfo.setUserName(user.getRealName());
            userInfo.setUserPhone(user.getPhone());
            vo.setUserInfo(userInfo);
        }

        //还有套餐

        // 设置导师信息
        Mentor mentor = mentorService.getById(follow.getMentorId());
        if (mentor != null) {
            Integer userId = mentor.getUserId();
            User userMentor = userService.getById(userId);
            FollowVO.MentorInfo mentorInfo = new FollowVO.MentorInfo();
            //查询这个导师的 userid
             if(userMentor!=null){
                mentorInfo.setMentorAccount(userMentor.getNickName());
                 mentorInfo.setMentorPhone(userMentor.getPhone());
             }
            mentorInfo.setMentorName(mentor.getMentorName());
            vo.setMentorInfo(mentorInfo);
        }

        return vo;
    }

    /**
     * 生成跟单单号
     */
    private String generateFollowNo() {
        return "FD" + new Date();
    }

}