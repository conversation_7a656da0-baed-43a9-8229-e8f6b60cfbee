package com.nq.vo.app;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(description = "导师申请展示对象")
public class MentorApplyVO {

    @ApiModelProperty(value = "申请ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "用户ID", example = "1")
    private Integer userId;

    @ApiModelProperty(value = "用户手机号", example = "13800138000")
    private String userPhone;

    @ApiModelProperty(value = "姓名", example = "张三")
    private String name;

    @ApiModelProperty(value = "年龄", example = "35")
    private Integer age;

    @ApiModelProperty(value = "投资年限", example = "5")
    private Integer investYears;

    @ApiModelProperty(value = "公司名称", example = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "收益比例", example = "20.5")
    private BigDecimal salaryRate;

    @ApiModelProperty(value = "担保资金(元)", example = "100000")
    private BigDecimal guaranteeFund;

    @ApiModelProperty(value = "投资简介")
    private String introduction;

    @ApiModelProperty(value = "申请状态(0-待审核，1-已通过，2-已拒绝)", example = "0")
    private Integer status;

    @ApiModelProperty(value = "状态描述", example = "待审核")
    private String statusDesc;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @ApiModelProperty(value = "审核备注")
    private String auditRemark;
} 