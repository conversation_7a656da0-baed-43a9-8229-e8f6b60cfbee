package com.nq.vo.position;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
public class AgentPositionVO {
    private Integer id;


    private String positionSn;

    private Integer userId;

    private String nickName;

    private Integer agentId;

    private String stockName;

    private String stockCode;

    private String stockGid;

    private String stockSpell;

    private String buyOrderId;

    private Date buyOrderTime;

    private BigDecimal buyOrderPrice;

    private String sellOrderId;

    private Date sellOrderTime;

    private BigDecimal sellOrderPrice;

    private BigDecimal profitTargetPrice;

    private BigDecimal stopTargetPrice;

    private String orderDirection;

    private Integer orderNum;


    private BigDecimal orderTotalPrice;

    private BigDecimal orderFee;




    private BigDecimal profitAndLose;

    private BigDecimal allProfitAndLose;

    private BigDecimal now_price;

    private Integer isLock;

    private String lockMsg;

    private String stockPlate;


}
