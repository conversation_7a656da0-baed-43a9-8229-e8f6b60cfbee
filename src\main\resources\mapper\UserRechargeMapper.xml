<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nq.dao.UserRechargeMapper" >
  <resultMap id="BaseResultMap" type="com.nq.pojo.UserRecharge" >
    <id column="id" property="id" jdbcType="BIGINT"/>
    <result column="user_id" property="userId" jdbcType="INTEGER"/>
    <result column="nick_name" property="nickName" jdbcType="VARCHAR"/>
    <result column="order_sn" property="orderSn" jdbcType="VARCHAR"/>
    <result column="pay_sn" property="paySn" jdbcType="VARCHAR"/>
    <result column="pay_channel" property="payChannel" jdbcType="VARCHAR"/>
    <result column="pay_amt" property="payAmt" jdbcType="DECIMAL"/>
    <result column="order_status" property="orderStatus" jdbcType="INTEGER"/>
    <result column="order_desc" property="orderDesc" jdbcType="VARCHAR"/>
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
    <result column="pay_time" property="payTime" jdbcType="TIMESTAMP"/>
    <result column="pay_id" property="payId" jdbcType="INTEGER"/>
  </resultMap>
  <sql id="Base_Column_List" >
    id, user_id, nick_name, order_sn, pay_sn, pay_channel, pay_amt, order_status,
    order_desc, add_time, pay_time, pay_id
  </sql>


  <select id="checkInMoney" resultType="integer" parameterType="map">
    SELECT COUNT(*) FROM user_recharge
    WHERE user_id = #{userId}
    and order_status = #{status}
    and add_time > date_sub(now(), interval 1 hour)
  </select>


  <delete id="deleteByUserId" parameterType="integer" >
    DELETE FROM user_recharge WHERE user_id = #{userId}
  </delete>





  <!--累计充值金额-->
  <select id="CountChargeSumAmt" resultType="decimal" parameterType="integer">
    SELECT sum(pay_amt) FROM user_recharge WHERE order_status = #{chargeState}
  </select>

  <!--今日充值金额-->
  <select id="CountTotalRechargeAmountByTime" parameterType="integer" resultType="decimal">
    select sum(IFNULL(pay_amt,0)) pay_amt from user_recharge where order_status = #{chargeState} and TO_DAYS(pay_time) = TO_DAYS(NOW())
  </select>

</mapper>