package com.nq.controller.admin;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.Level;
import com.nq.service.LevelService;
import com.nq.vo.admin.LevelVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;

@Api(tags = "后台-等级管理")
@RestController
@RequestMapping("/admin/level")
public class AdminLevelController {

    @Resource
    private LevelService levelService;

    @ApiOperation("分页查询等级列表")
    @GetMapping("/list.do")
    public ServerResponse<PageInfo<LevelVO>> list(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页数量") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("等级名称") @RequestParam(required = false) String levelName
    ) {
        PageInfo<LevelVO> pageInfo = levelService.pageList(pageNum, pageSize, levelName, null);
        return ServerResponse.createBySuccess(pageInfo);
    }

    @ApiOperation("添加等级")
    @PostMapping("/add.do")
    public ServerResponse<String> add(@RequestBody Level level) {
        level.setCreateTime(new Date());
        level.setUpdateTime(new Date());
        // 如果没有设置排序值，默认为0
        if (level.getSort() == null) {
            level.setSort(0);
        }
        levelService.save(level);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation("修改等级信息")
    @PostMapping("/update.do")
    public ServerResponse<String> update(@RequestBody Level level) {
        level.setUpdateTime(new Date());
        // 如果没有设置排序值，默认为0
        if (level.getSort() == null) {
            level.setSort(0);
        }
        levelService.updateById(level);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation("删除等级")
    @GetMapping("/delete.do")
    public ServerResponse<String> delete(@RequestParam Integer id) {
        levelService.removeById(id);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation("获取等级详情")
    @GetMapping("/info.do")
    public ServerResponse<LevelVO> info(@RequestParam Integer id) {
        LevelVO levelVO = levelService.getDetailById(id);
        return ServerResponse.createBySuccess(levelVO);
    }
}
