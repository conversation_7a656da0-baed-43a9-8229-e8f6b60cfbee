package com.nq.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.pojo.SiteAdmin;
import com.nq.vo.admin.AdminStatisticVO;
import com.nq.vo.admin.SiteAdminVo;

import javax.servlet.http.HttpServletRequest;

public interface ISiteAdminService extends IService<SiteAdmin> {
    SiteAdminVo login(String paramString1, String paramString2, String paramString3, HttpServletRequest paramHttpServletRequest);

    PageInfo<SiteAdmin> listByAdmin(String paramString1, String paramString2, HttpServletRequest paramHttpServletRequest, int paramInt1, int paramInt2);

    void updateLock(Integer paramInteger);

    void add(SiteAdmin paramSiteAdmin);

    void update(SiteAdmin paramSiteAdmin);


    SiteAdmin getCurrentUser();

    AdminStatisticVO statisticCount();
}
