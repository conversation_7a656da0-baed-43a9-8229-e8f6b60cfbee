package com.nq.pojo;

import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;
import java.util.Date;

@Data
@ApiModel(description = "短信发送日志")
@AllArgsConstructor
@NoArgsConstructor
public class SiteSmsLog extends BaseEntity {
    @ApiModelProperty(value = "日志ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "接收手机号", example = "13800138000")
    private String smsPhone;

    @ApiModelProperty(value = "短信标题", example = "验证码")
    private String smsTitle;

    @ApiModelProperty(value = "短信内容", example = "您的验证码是123456，5分钟内有效")
    private String smsCnt;

    @ApiModelProperty(value = "短信模板", example = "SMS_123456")
    private String smsTemplate;

    @ApiModelProperty(value = "发送状态(1:成功,0:失败)", example = "1")
    private Integer smsStatus;

    @ApiModelProperty(value = "发送时间", example = "2024-01-01 12:00:00")
    private Date addTime;


}
