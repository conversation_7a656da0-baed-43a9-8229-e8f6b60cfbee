package com.nq.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.StockMarketsDay;
import com.nq.pojo.StockOption;
import com.nq.vo.stock.StockOptionListVO;

import javax.servlet.http.HttpServletRequest;

public interface IStockOptionService extends IService<StockOption> {

  PageInfo<StockOptionListVO> findMyStockOptions(String paramString, HttpServletRequest paramHttpServletRequest, int paramInt1, int paramInt2);
  
  Boolean isOption(Integer paramInteger, String paramString);

  String isMyOption(Integer paramInteger, String paramString);

}
