package com.nq.controller.agent;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.UserWithdraw;
import com.nq.service.IUserWithdrawService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;


@RestController
@Api(tags = {"代理后台-提现管理"})
@RequestMapping({"/agent/withdraw/"})
public class AgentWithdrawController {

    private static final Logger log = LoggerFactory.getLogger(AgentWithdrawController.class);


    @Resource
    private IUserWithdrawService iUserWithdrawService;

    @ApiOperation("分页查询提现列表")
    @PostMapping({ "list.do" })
    public ServerResponse<PageInfo<UserWithdraw>> list(
            @ApiParam("用户ID") @RequestParam(value = "userId", required = false) Integer userId,
            @ApiParam("订单号") @RequestParam(value = "orderSn", required = false) String orderSn,
            @ApiParam("昵称") @RequestParam(value = "nickName", required = false) String nickName,
            @ApiParam("提现状态") @RequestParam(value = "state", required = false) Integer state,
            @ApiParam("开始时间") @RequestParam(value = "beginTime", required = false) String beginTime,
            @ApiParam("结束时间") @RequestParam(value = "endTime", required = false) String endTime,
            @ApiParam("页码") @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam("每页数量") @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        PageInfo<UserWithdraw> userWithdrawPageInfo = this.iUserWithdrawService.listByAdmin(userId, orderSn, nickName,
                state, beginTime, endTime, pageNum, pageSize, true);
        return ServerResponse.createBySuccess(userWithdrawPageInfo);
    }

    @ApiOperation("导出提现记录")
    @PostMapping({ "export.do" })
    public void export(
            @ApiParam("用户ID") @RequestParam(value = "userId", required = false) Integer userId,
            @ApiParam("订单号") @RequestParam(value = "orderSn", required = false) String orderSn,
            @ApiParam("昵称") @RequestParam(value = "nickName", required = false) String nickName,
            @ApiParam("提现状态") @RequestParam(value = "state", required = false) Integer state,
            @ApiParam("开始时间") @RequestParam(value = "beginTime", required = false) String beginTime,
            @ApiParam("结束时间") @RequestParam(value = "endTime", required = false) String endTime,
            HttpServletResponse response) {
        List<UserWithdraw> userRechargeList = this.iUserWithdrawService.exportByAdmin(userId, orderSn, nickName, state,
                beginTime, endTime, true);
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("提现导出", "提现数据"),
                UserWithdraw.class, userRechargeList);
        try {
            ServletOutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            log.error("导出提现数据失败", e);
        }
    }

}
