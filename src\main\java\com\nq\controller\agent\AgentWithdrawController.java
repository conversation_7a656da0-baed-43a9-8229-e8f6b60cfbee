package com.nq.controller.agent;


import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;

import com.nq.pojo.UserWithdraw;
import com.nq.service.IUserWithdrawService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;

import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Controller;

import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;

import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping({"/agent/withdraw/"})
public class AgentWithdrawController {

    private static final Logger log = LoggerFactory.getLogger(AgentWithdrawController.class);




    //分页查询所有出金记录信息及模糊查询
    @RequestMapping({"list.do"})

    public ServerResponse<PageInfo<UserWithdraw>> list(@RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "8") int pageSize, @RequestParam(value = "realName", required = false) String realName, @RequestParam(value = "state", required = false) Integer state, @RequestParam(value = "agentId", required = false) Integer agentId, HttpServletRequest request) {
        return null;
    }
}
