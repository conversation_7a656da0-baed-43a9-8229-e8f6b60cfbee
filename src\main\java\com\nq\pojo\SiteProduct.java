package com.nq.pojo;

import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;

@Data
@ApiModel(description = "产品功能开关配置")
@AllArgsConstructor
@NoArgsConstructor
public class SiteProduct extends BaseEntity {
    @ApiModelProperty(value = "配置ID", example = "1")
    private Integer id;



    @ApiModelProperty(value = "实名认证开关(1:开启,0:关闭)", example = "true")
    private Boolean realNameDisplay;


    @ApiModelProperty(value = "节假日开关(1:开启,0:关闭)", example = "true")
    private Boolean holidayDisplay;

}
