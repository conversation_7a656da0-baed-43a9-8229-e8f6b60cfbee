package com.nq.pojo;

import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
@ApiModel(description = "代理商用户信息")
@AllArgsConstructor
@NoArgsConstructor
public class AgentUser  extends BaseEntity {
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "代理商用户名", example = "agent001")
    private String agentName;

    @ApiModelProperty(value = "代理商密码", example = "******")
    private String agentPwd;

    @ApiModelProperty(value = "绑定的用户id", example = "1001")
    private Integer userId;

    @ApiModelProperty(value = "绑定的用户昵称", example = "用户昵称")
    private String userNickName;

    @ApiModelProperty(value = "绑定的用户姓名", example = "用户姓名")
    private String userRealName;

    @ApiModelProperty(value = "代理商手机号", example = "13800138000")
    private String agentPhone;

    @ApiModelProperty(value = "添加时间", example = "2024-01-01 12:00:00")
    private Date addTime;

    @ApiModelProperty(value = "是否锁定：0-未锁定，1-已锁定", example = "0")
    private Integer isLock;

}
