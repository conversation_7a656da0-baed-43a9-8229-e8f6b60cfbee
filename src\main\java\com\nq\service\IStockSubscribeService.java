package com.nq.service;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.StockSubscribe;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletRequest;
import javax.xml.crypto.Data;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【stock_subscribe(新股)】的数据库操作Service
* @createDate 2022-10-24 23:27:27
*/
public interface IStockSubscribeService extends IService<StockSubscribe> {
    //pageNum, pageSize, name, code, zt, isLock, type, status
    PageInfo<StockSubscribe> list( int pageNum, int pageSize,String name,String code,Integer zt,Integer isLock,Integer type,Integer status);
    //pageNum, pageSize, name, code, zt, isLock, type
    PageInfo<StockSubscribe> listByAdmin( int pageNum, int pageSize,String name,String code,Integer zt,Integer isLock,Integer type);

    void add(StockSubscribe model);

    void update(StockSubscribe model);

    void del(Integer id);

    List<StockSubscribe> newStockQc();
}
