# 数据源配置
# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8090
  port: 8091
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.nq.pojo
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql
wallet:
  isHotWallet: false
# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api
  title: Stock Service API
  description: Stock Service API Documentation
  version: 1.0
  base-package: com.nq.controller
  contact:
    name: Your Name
    url: http://your-website.com
    email: <EMAIL>

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*
spring:
  application:
    name: @artifactId@
  profiles:
    active: local
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  task:
    scheduling:
      pool:
        size: 8 #配置Scheduled定时任务为多线程执行

## MyBatis-Plus配置

mybatis-plus:
  # 开启SQL语句打印
  global-config:
    db-config:
      # 全局默认主键类型
      id-type: auto
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    # 开启二级缓存
    cache-enabled: true

# sql打印与执行时间监控
decorator:
  datasource:
    p6spy:
      logging: slf4j
      log-format: "执行SQL %(executionTime)ms |  %(sqlSingleLine)"

# Sa-Token 配置
sa-token:
  # token名称 (同时也是cookie名称)
  # token-name: satoken
  # token有效期，单位s 默认30天, -1代表永不过期
  timeout: 2592000
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  active-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: false
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: false
  # 多账号体系配置
  multiple-login:
    # 是否开启多账号体系
    enable: true
    # 账号类型列表
    account-types:
      - user
      - admin
      - agent
