<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nq.dao.StockOptionMapper" >
  
  <resultMap id="BaseResultMap" type="com.nq.pojo.StockOption">
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="user_id" property="userId" jdbcType="INTEGER"/>
    <result column="stock_id" property="stockId" jdbcType="INTEGER"/>
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
    <result column="stock_code" property="stockCode" jdbcType="VARCHAR"/>
    <result column="stock_name" property="stockName" jdbcType="VARCHAR"/>
    <result column="stock_gid" property="stockGid" jdbcType="VARCHAR"/>
    <result column="is_lock" property="isLock" jdbcType="INTEGER"/>
  </resultMap>

  <sql id="Base_Column_List" >
    id, user_id, stock_id, add_time, stock_code, stock_name, stock_gid, is_lock
  </sql>



  <select id="findMyOptionIsExistByCode" resultMap="BaseResultMap" parameterType="map">
    SELECT
    <include refid="Base_Column_List"/>
    FROM stock_option
    WHERE user_id = #{uid} and stock_code = #{stockCode}
  </select>

  <select id="findMyOptionByKeywords" resultMap="BaseResultMap" parameterType="map">
    SELECT
    <include refid="Base_Column_List"/>
    FROM stock_option
    <where>
      user_id = #{uid}
      <if test="keyWords != null and keyWords != '' ">
        and stock_code like CONCAT('%','${keyWords}','%')
      </if>
    </where>
    order by stock_code asc
  </select>


  <select id="isOption" parameterType="map" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM stock_option
    WHERE user_id = #{uid} and stock_code = #{code}
  </select>


</mapper>