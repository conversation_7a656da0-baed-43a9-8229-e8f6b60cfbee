package com.nq.controller.app;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dto.app.TeamInfoDTO;
import com.nq.dto.app.TradeRecordQueryDTO;
import com.nq.dto.common.CommonPage;
import com.nq.pojo.User;
import com.nq.service.FollowTradeService;
import com.nq.service.IUserService;
import com.nq.service.LoadEarnService;
import com.nq.vo.user.LoadEarnVO;
import com.nq.vo.app.FollowIncomeVO;
import com.nq.vo.app.TeamInfoVO;
import com.nq.vo.app.TeamVO;
import com.nq.vo.app.TradeRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

//完事
@Api(tags = "APP-跟单接口")
@RestController
@RequestMapping("/api/follow")
public class AppFollowApiController {

    @Resource
    private IUserService iUserService;
    @Resource
    private FollowTradeService followTradeService;
    @Resource
    private LoadEarnService loadEarnService;

    @ApiOperation(value = "获取跟单收益信息 总收益 今日收益 跟单总数 今日跟单")
    @GetMapping("/getFollowIncome.do")
    public ServerResponse<FollowIncomeVO> getFollowIncome(HttpServletRequest request) {
        User user = iUserService.getCurrentUser();
        if (user == null) {
            return ServerResponse.createBySuccessMsg("用户未登录");
        }
        Integer id = user.getId();
        User userServiceById = iUserService.getById(id);
        FollowIncomeVO followIncomeVO = iUserService.getFollowIncome(userServiceById);
        return ServerResponse.createBySuccess(followIncomeVO);
    }
    @ApiOperation(value = "获取团队信息")
    @GetMapping("/getTeam.do")
    public ServerResponse<TeamVO> getTeam(HttpServletRequest request) {
        User user = iUserService.getCurrentUser();
        if (user == null) {
            return ServerResponse.createBySuccessMsg("用户未登录");
        }
        TeamVO teamVO = iUserService.getTeam(user);
        return ServerResponse.createBySuccess(teamVO);
    }
    @ApiOperation(value = "获取各种人数的具体信息  0团队总人数")
    @PostMapping("/getTeamInfo.do")
    public ServerResponse<PageInfo<TeamInfoVO>> getTeamInfo(@RequestBody @Valid TeamInfoDTO dto) {
        User user = iUserService.getCurrentUser();
        if (user == null) {
            return ServerResponse.createBySuccessMsg("用户未登录");
        }
        PageInfo<TeamInfoVO>  page = iUserService.getTeamInfo(user,dto);
        return ServerResponse.createBySuccess(page);
    }





    @ApiOperation(value = "获取操盘记录")
    @PostMapping("/getTradingRecords.do")
    public ServerResponse<TradeRecordVO> getTradingRecords(@RequestBody @Valid TradeRecordQueryDTO dto, HttpServletRequest request) {
        User user = iUserService.getCurrentUser();
        if (user == null) {
            return ServerResponse.createBySuccessMsg("用户未登录");
        }
        // 如果传入了经理人ID，则使用经理人ID查询，否则使用当前用户ID
        TradeRecordVO vo = followTradeService.getTradingRecords(dto);
        return ServerResponse.createBySuccess(vo);
    }


    @ApiOperation(value = "查询代理工资记录")
    @PostMapping("/getAgentSalaryList.do")
    public ServerResponse<PageInfo<LoadEarnVO>> getAgentSalaryList(HttpServletRequest request, @RequestBody @Valid CommonPage page) {
        User user = iUserService.getCurrentUser();
        if (user == null) {
            return ServerResponse.createBySuccessMsg("用户未登录");
        }
        Integer pageNum = page.getPageNum();
        Integer pageSize = page.getPageSize();
        PageInfo<LoadEarnVO> pageInfo  = loadEarnService.getAgentSalaryPage(user, pageNum,pageSize);
        return ServerResponse.createBySuccess(pageInfo);
    }
}

