package com.nq.service;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.Stock;
import com.nq.vo.stock.ChartCellVO;
import com.nq.vo.stock.MarketVOResult;
import com.nq.vo.stock.StockAdminListVO;
import com.nq.vo.stock.StockListVO;
import com.nq.vo.stock.k.echarts.EchartsDataVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface IStockService extends IService<Stock> {
    MarketVOResult getMarket();
    //pageNum, pageSize, keyWords, stockPlate, stockType
    PageInfo<StockListVO> getStock(int pageNum, int pageSize, String keyWords, String stockPlate, String stockType);
    //code, time, ma, size
    EchartsDataVO getMinK_Echarts(String code, Integer time, Integer ma, Integer size);

    EchartsDataVO getDateline(HttpServletResponse paramHttpServletResponse, String paramString);

    Map getSingleStock(String paramString);

    void z1();

    void z11();

    void z12();

    void z2();

    void z21();

    void z22();

    void z3();

    void z31();

    void z32();

    void z4();

    void z41();

    void z42();

    void h1();

    void h11();

    void h12();

    void h2();

    void h21();

    void h22();

    void h3();

    void h31();

    void h32();

    void bj1();
    EchartsDataVO getDayK_Echarts(String code);


    Stock findStockByName(String stockName);

    Stock findStockByCode(String stockCode);

    Stock findStockById(Integer stockId);
  //showState, lockState, code, name, stockPlate, stockType, pageNum, pageSize
    PageInfo<StockAdminListVO> listByAdmin(Integer showState, Integer lockState, String code, String name, String stockPlate, String stockType, int pageNum, int pageSize);

    void updateLock(Integer paramInteger);

    void updateShow(Integer paramInteger);

    void addStock(String paramString1, String paramString2, String paramString3, String paramString4, Integer paramInteger1, Integer paramInteger2);

    int CountStockNum();

    int CountShowNum(Integer num);

    int CountUnLockNum(Integer num);

    List<Stock> findStockList();

    BigDecimal selectRateByDaysAndStockCode(String code, int days);

    void updateStock(Stock model);

    void deleteByPrimaryKey(Integer id);

    List<Stock> stockDataBase();

    JSONArray lhb();

    JSONArray top(Integer content);

    JSONArray stop();


    /**
     * 获取同花顺涨跌幅榜
     *
     * @return
     */
    List<ChartCellVO> getZdfb();


    /**
     * @param pageNo
     * @param pageSize
     * @param sort     根据什么排序
     * @param asc      是否升序 0否 1是
     * @param node     排序的主板类型 科创板  创业板 a股  北交所等
     * @return
     */
    JSONArray getStockSort(Integer pageNo, Integer pageSize, String sort, Integer asc, String node);

    /**
     * 涨停板
     *
     * @return
     */
    JSONArray ztb();

    JSONArray getVipByCode(String code);
}
