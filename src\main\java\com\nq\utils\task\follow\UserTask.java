package com.nq.utils.task.follow;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nq.job.task.ITask;
import com.nq.pojo.User;
import com.nq.service.IUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.weaver.ast.Var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
//import org.springframework.scheduling.annotation.Scheduled;

@Slf4j
@Component("userTask")
public class UserTask implements ITask {
    //手写 嫌弃刷新下用户信息
    @Resource
    private IUserService userService;

    //    @Scheduled(cron = "0 0 0 * * ?")
    @Override
    public void run(String params) {
        List<User> list = userService.list(new LambdaQueryWrapper<User>().select(User::getId, User::getPhone, User::getPid));

        for (User user : list) {
            List<Integer> listPPJson = new ArrayList();
            List<Integer> longs = queryLower(listPPJson, user.getPid());

            if (CollectionUtil.isNotEmpty(longs)) {
                List<Integer> reverse = CollectionUtil.reverse(longs);
                String join = StringUtils.join(reverse, ",");
                // hzUserEntity.setLowerNum(Long.parseLong(longs.size() + ""))

                user.setPPath(join);
                userService.updateById(user);
            } else {
                user.setPPath("");
                userService.updateById(user);
            }
        }
        for (User user : list) {
            List<User> users = userService.queryLowerUser(user.getId());
            if (CollectionUtil.isNotEmpty(users)) {
                user.setLowerNum(users.size());
                userService.updateById(user);
            }
        }

    }


    private List<Integer> queryLower(List<Integer> listPPJson, Integer parentId) {
        User entities = listByParentId(parentId);
        if (entities != null) {
            listPPJson.add(entities.getId());
            queryLower(listPPJson, entities.getPid());
        }

        return listPPJson;
    }

    private User listByParentId(Integer parentId) {
        return userService.getOne(new LambdaQueryWrapper<User>().eq(User::getId, parentId).select(User::getId, User::getPhone, User::getPid));
    }
}