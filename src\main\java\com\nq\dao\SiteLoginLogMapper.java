package com.nq.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nq.pojo.SiteLoginLog;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
public interface SiteLoginLogMapper  extends BaseMapper<SiteLoginLog> {

  List<SiteLoginLog> loginList(@Param("userId") Integer paramInteger);
  
  int deleteByUserId(@Param("userId") Integer paramInteger);
}
