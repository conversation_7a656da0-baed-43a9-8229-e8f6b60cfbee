package com.nq.controller.app;

import com.nq.common.ServerResponse;
import com.nq.service.AppConfigService;
import com.nq.service.IStockService;
import com.nq.service.LevelService;
import com.nq.service.SitePopupService;
import com.nq.vo.admin.SitePopupVO;
import com.nq.vo.app.AppConfigVO;
import com.nq.vo.app.FollowLevelVO;
import com.nq.vo.stock.ChartCellVO;
import com.nq.vo.stock.k.echarts.EchartsDataVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.swing.*;
import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping({"/api/stock/"})
@Api(tags = "APP-首页接口")
public class AppIndexApiController {
    @Resource
    private IStockService iStockService;
    @Resource
    private SitePopupService sitePopupService;
    /**
     * 涨跌数
     *
     * @return
     */
    @PostMapping({"getZdfNumber.do"})
    @ApiOperation("获取涨跌数")
    public ServerResponse<Object> getZdfNumber() {
        List<ChartCellVO> data =  iStockService.getZdfb();;
        if (CollectionUtils.isEmpty(data)) {
            return ServerResponse.createByError();
        }
        ChartCellVO chartCellVO = data.get(0);
        ChartCellVO chartCellVO1 = data.get(3);
        ChartCellVO chartCellVO3 = data.get(2);
        ChartCellVO chartCellVO4 = data.get(5);
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("zhang", Integer.parseInt(chartCellVO.getYAxis()) + Integer.parseInt(chartCellVO1.getYAxis()));
        resultMap.put("die", Integer.parseInt(chartCellVO3.getYAxis()) + Integer.parseInt(chartCellVO4.getYAxis()));
        return ServerResponse.createBySuccess(resultMap);
    }
    //
    @PostMapping({"getMinK_Echarts.do"})
    @ApiOperation("获取k线图")
    public ServerResponse<EchartsDataVO> getMinK_Echarts(@RequestParam("code") String code, @RequestParam("time") Integer time, @RequestParam("ma") Integer ma, @RequestParam("size") Integer size) {
        EchartsDataVO minKEcharts = iStockService.getMinK_Echarts(code, time, ma, size);
        return ServerResponse.createBySuccess(minKEcharts);
    }
    //
    @GetMapping({"getPopup.do"})
    @ApiOperation("获取弹窗图")
    public ServerResponse<SitePopupVO> getPopup() {
        return sitePopupService.getPopup();
    }
}

