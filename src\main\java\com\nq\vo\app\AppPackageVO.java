package com.nq.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "套餐")
public class AppPackageVO {
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "产品名称", example = "基础套餐")
    private String productName;

    @ApiModelProperty(value = "跟单金额最小值", example = "1000.00")
    private BigDecimal minAmount;

    @ApiModelProperty(value = "跟单金额最大值", example = "100000.00")
    private BigDecimal maxAmount;

    @ApiModelProperty(value = "工资比例(%)", example = "0.10")
    private BigDecimal salaryRate;

}
