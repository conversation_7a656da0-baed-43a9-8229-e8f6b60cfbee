package com.nq;

import cn.dev33.satoken.SaManager;
import lombok.SneakyThrows;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@MapperScan(basePackages = {"com.nq.dao"})
@EnableScheduling
@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
@EnableTransactionManagement
@EnableAspectJAutoProxy(proxyTargetClass = true)
@EnableAsync(proxyTargetClass = true)
public class StockApplication {

    @SneakyThrows
    public static void main(String[] args) {
        System.out.println("Sa-Token 版本: " + SaManager.getConfig());
        System.out.println("Sa-Token 配置: " + SaManager.getConfig().toString());
        System.out.println("Sa-Token配置: " + SaManager.getConfig().toString());
        SpringApplication.run(StockApplication.class, args);
    }

}
