package com.nq.vo.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(description = "跟单申请VO")
public class FollowVO {
    
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;
    
    @ApiModelProperty(value = "跟单单号", example = "FD202403010001")
    private String followNo;
    
    @ApiModelProperty(value = "套餐ID", example = "1")
    private Integer packageId;
    
    @ApiModelProperty(value = "导师ID", example = "1")
    private Integer mentorId;
    
    @ApiModelProperty(value = "用户ID", example = "1")
    private Integer userId;

    @ApiModelProperty("跟单会员信息")
    private UserInfo userInfo;

    @ApiModelProperty("导师信息")
    private MentorInfo mentorInfo;
    
    @ApiModelProperty(value = "跟单金额", example = "30000.00")
    private BigDecimal amount;
    
    @ApiModelProperty(value = "追加金额", example = "0.00")
    private BigDecimal addAmount;
    
    @ApiModelProperty(value = "佣金比例", example = "40.00")
    private BigDecimal positionRate;
    
    @ApiModelProperty(value = "工资比例", example = "0.03")
    private BigDecimal salaryRate;
    
    @ApiModelProperty(value = "跟单最低", example = "1000.00")
    private BigDecimal minAmount;
    
    @ApiModelProperty(value = "跟单最高", example = "50000.00")
    private BigDecimal maxAmount;
    
    @ApiModelProperty(value = "申请时间")
    private Date applyTime;
    
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    
    @ApiModelProperty(value = "终止时间")
    private Date stopTime;
    
    @ApiModelProperty(value = "是否追加(0-否，1-是)", example = "0")
    private Integer isAdd;
    
    @ApiModelProperty(value = "今日日期", example = "2024-03-01")
    private Date today;
    
    @ApiModelProperty(value = "状态(1-待审核，2-跟单中，3-被驳回，4-已撤销，5-已结束，6-已中止)", example = "1")
    private Integer status;
    
    @ApiModelProperty(value = "审核时间")
    private Date auditTime;
    
    @ApiModelProperty(value = "套餐天数", example = "30")
    private Integer packageDays;
    
    @ApiModelProperty(value = "已用套餐天数", example = "0")
    private Integer usedDays;
    
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @Data
    @ApiModel(description = "跟单会员信息")
    public static class UserInfo {
        @ApiModelProperty(value = "用户账号", example = "aa0003")
        private String userAccount;
        
        @ApiModelProperty(value = "用户姓名", example = "张三")
        private String userName;
        
        @ApiModelProperty(value = "用户手机号", example = "***********")
        private String userPhone;
    }

    @Data
    @ApiModel(description = "导师信息")
    public static class MentorInfo {
        @ApiModelProperty(value = "导师账号", example = "mentor001")
        private String mentorAccount;
        
        @ApiModelProperty(value = "导师姓名", example = "王翔鹏")
        private String mentorName;
        
        @ApiModelProperty(value = "导师手机号", example = "***********")
        private String mentorPhone;
        
        @ApiModelProperty(value = "跟单金额最低", example = "5000.00")
        private BigDecimal minAmount;
        
        @ApiModelProperty(value = "跟单金额最高", example = "30000.00")
        private BigDecimal maxAmount;
    }
} 