package com.nq.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nq.dao.FollowAppendMapper;
import com.nq.pojo.FollowAppend;
import com.nq.service.FollowAppendService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 跟单追加申请表 服务实现类
 * </p>
 *
 * <AUTHOR> // TODO: Replace YourName with the actual author
 * @since 2024-07-26 // TODO: Adjust date if needed
 */
@Service
public class FollowAppendServiceImpl extends ServiceImpl<FollowAppendMapper, FollowAppend> implements FollowAppendService {


} 