package com.nq.pojo;

import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "股票自选信息")
public class StockOption extends BaseEntity {
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "用户ID", example = "1")
    private Integer userId;

    @ApiModelProperty(value = "股票ID", example = "1")
    private Integer stockId;

    @ApiModelProperty(value = "添加时间", example = "2024-01-01 12:00:00")
    private Date addTime;

    @ApiModelProperty(value = "股票代码", example = "000001")
    private String stockCode;

    @ApiModelProperty(value = "股票名称", example = "平安银行")
    private String stockName;

    @ApiModelProperty(value = "股票唯一标识", example = "sz000001")
    private String stockGid;

    @ApiModelProperty(value = "是否锁定(0:未锁定 1:已锁定)", example = "0")
    private Integer isLock;
}
