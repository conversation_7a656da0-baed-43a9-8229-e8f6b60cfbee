package com.nq.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 新闻资讯
 *
 * <AUTHOR> 2020-08-05
 */
@Data
@ApiModel(description = "新闻资讯")
@AllArgsConstructor
@NoArgsConstructor
public class SiteNews extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 新闻主键id
     */
    @ApiModelProperty(value = "新闻主键ID", example = "1")
    private Long id;

    /**
     * 新闻类型：1、财经要闻，2、经济数据，3、全球股市，4、7*24全球，5、商品资讯，6、上市公司，7、全球央行
     */
    @ApiModelProperty(value = "新闻类型(1:财经要闻,2:经济数据,3:全球股市,4:7*24全球,5:商品资讯,6:上市公司,7:全球央行)", example = "1")
    private Integer type;

    /**
     * 新闻标题
     */
    @ApiModelProperty(value = "新闻标题", example = "2024年全球经济展望")
    private String title;

    /**
     * 来源id
     */
    @ApiModelProperty(value = "来源ID", example = "source_123")
    private String sourceId;

    /**
     * 来源名称
     */
    @ApiModelProperty(value = "来源名称", example = "财经网")
    private String sourceName;

    /**
     * 浏览量
     */
    @ApiModelProperty(value = "浏览量", example = "1000")
    private Integer views;

    /**
     * 状态：1、启用，0、停用
     */
    @ApiModelProperty(value = "状态(1:启用,0:停用)", example = "1")
    private Integer status;

    /**
     * 显示时间
     */
    @ApiModelProperty(value = "显示时间", example = "2024-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date showTime;

    /**
     * 添加时间
     */
    @ApiModelProperty(value = "添加时间", example = "2024-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date addTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", example = "2024-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 图片地址
     */
    @ApiModelProperty(value = "图片地址", example = "http://example.com/news.jpg")
    private String imgurl;

    /**
     * 描述
     */
    @ApiModelProperty(value = "新闻描述", example = "本文分析了2024年全球经济的发展趋势...")
    private String description;

    /**
     * 新闻内容
     */
    @ApiModelProperty(value = "新闻内容", example = "2024年全球经济将面临诸多挑战...")
    private String content;
    /**
     * 新闻内容
     */
    @ApiModelProperty(value = "置顶 0不置顶 1zhi 置顶", example = "0...")
    private Integer isTop;


}
