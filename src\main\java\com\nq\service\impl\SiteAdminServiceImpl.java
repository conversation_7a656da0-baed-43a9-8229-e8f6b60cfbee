package com.nq.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nq.dao.SiteAdminMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.excepton.CustomException;
import com.nq.pojo.SiteAdmin;
import com.nq.pojo.SiteLoginLog;
import com.nq.service.*;
import com.nq.utils.BeanCopyUtil;
import com.nq.utils.PropertiesUtil;
import com.nq.utils.SymmetricCryptoUtil;

import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.nq.vo.admin.AdminStatisticVO;
import com.nq.vo.admin.SiteAdminVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.nq.common.satoken.StpAdminUtil;

@Service("iSiteAdminServiceImpl")
public  class SiteAdminServiceImpl extends ServiceImpl<SiteAdminMapper, SiteAdmin> implements ISiteAdminService {
    private static final Logger log = LoggerFactory.getLogger(SiteAdminServiceImpl.class);

    @Resource
    private SiteAdminMapper siteAdminMapper;
    @Resource
    private IUserService userService;
    @Resource
    private FollowService followService;
    @Resource
    private IUserWithdrawService withdrawService;
    @Resource
    private IUserRechargeService rechargeService;
    @Resource
    private ISiteLoginLogService iSiteLoginLogService;
    @Override
    public SiteAdminVo login(String adminName, String adminPwd, String verifyCode, HttpServletRequest request) {
        if (StringUtils.isBlank(adminName) || StringUtils.isBlank(adminPwd)) {
            throw new CustomException("参数不能为空");
        }
        adminPwd = SymmetricCryptoUtil.encryptPassword(adminPwd);
        SiteAdmin siteAdmin = this.siteAdminMapper.login(adminName, adminPwd);
        if (siteAdmin == null) {
            throw new CustomException("用户密码不正确");
        }
        if (siteAdmin.getIsLock() == 1) {
            throw new CustomException("登陆失败，您的账号已被锁定！");
        }
        StpAdminUtil.login(siteAdmin.getId());
        String tokenValue = StpAdminUtil.getTokenValue();
        SiteAdminVo siteAdminVo = BeanCopyUtil.copyProperties(siteAdmin, SiteAdminVo.class);
        siteAdminVo.setToken(tokenValue);
        return siteAdminVo;
    }

    @Override
    public PageInfo<SiteAdmin> listByAdmin(String adminName, String adminPhone, HttpServletRequest request, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        String superAdmin = PropertiesUtil.getProperty("admin.super.name");
        List<SiteAdmin> siteAdmins = this.siteAdminMapper.listByAdmin(adminName, adminPhone, superAdmin);
        PageInfo<SiteAdmin> pageInfo = new PageInfo<>(siteAdmins);
        return pageInfo;
    }



    @Override
    public void updateLock(Integer adminId) {
        SiteAdmin siteAdmin = this.siteAdminMapper.selectById(adminId);
        if (siteAdmin == null) {
            throw new CustomException("管理员不存在");
        }
        if (siteAdmin.getIsLock() == 0) {
            siteAdmin.setIsLock(1);
        } else {
            siteAdmin.setIsLock(0);
        }
        int updateCount = this.siteAdminMapper.updateById(siteAdmin);
        if (updateCount <= 0) {
            throw new CustomException("修改失败");
        }
    }

    @Override
    public void add(SiteAdmin siteAdmin) {
        if (StringUtils.isBlank(siteAdmin.getAdminName()) || StringUtils.isBlank(siteAdmin.getAdminPhone()) || StringUtils.isBlank(siteAdmin.getAdminPwd()) || siteAdmin.getIsLock() == null) {
            throw new CustomException("参数不能为空");
        }
        SiteAdmin siteAdmin1 = this.siteAdminMapper.findAdminByName(siteAdmin.getAdminName());
        if (siteAdmin1 != null) {
            throw new CustomException("管理名存在");
        }
        SiteAdmin siteAdmin2 = this.siteAdminMapper.findAdminByPhone(siteAdmin.getAdminPhone());
        if (siteAdmin2 != null) {
            throw new CustomException("手机号存在");
        }
        SiteAdmin dbadmin = new SiteAdmin();
        dbadmin.setAdminName(siteAdmin.getAdminName());
        dbadmin.setAdminPhone(siteAdmin.getAdminPhone());
        dbadmin.setAdminPwd(SymmetricCryptoUtil.encryptPassword(siteAdmin.getAdminPwd()));
        dbadmin.setIsLock(siteAdmin.getIsLock());
        dbadmin.setAddTime(new Date());
        int insertCount = this.siteAdminMapper.insert(dbadmin);
        if (insertCount <= 0) {
            throw new CustomException("添加失败");
        }
    }

    @Override
    public void update(SiteAdmin siteAdmin) {
        if (siteAdmin.getId() == null) {
            throw new CustomException("修改id不能为空");
        }
        siteAdmin.setAdminPwd(SymmetricCryptoUtil.encryptPassword(siteAdmin.getAdminPwd()));
        int updateCount = this.siteAdminMapper.updateById(siteAdmin);
        if (updateCount <= 0) {
            throw new CustomException("修改失败");
        }
    }



    @Override
    public SiteAdmin getCurrentUser() {
        Integer adminId = StpAdminUtil.getUserId();
        return this.siteAdminMapper.selectById(adminId);
    }

    @Override
    public AdminStatisticVO statisticCount() {
        AdminStatisticVO adminCountVO = new AdminStatisticVO();
        //总用户数 //0 全部 1 今日 2 昨日 3 近七日 4 近一个月
        adminCountVO.setTotalUserCount(userService.countNewUserByDate(0));
        //昨日新增用户数
        adminCountVO.setTodayNewUserCount(userService.countNewUserByDate(1));
        adminCountVO.setYesterdayNewUserCount(userService.countNewUserByDate(2));
        adminCountVO.setLast7DaysNewUserCount(userService.countNewUserByDate(3));
        adminCountVO.setMonthNewUserCount(userService.countNewUserByDate(4));
        adminCountVO.setTodayApplyFollowCount(followService.countApplyFollowNumByDate(1));
        adminCountVO.setTodayApplyFollowAmount(followService.countApplyFollowAmountByDate(1));
        adminCountVO.setYesterdayApplyFollowCount(followService.countApplyFollowNumByDate(2));
        adminCountVO.setYesterdayApplyFollowAmount(followService.countApplyFollowAmountByDate(2));
        //这个总提现金额吧用户身上的提现总金额加一起吧 只算通过的
        adminCountVO.setTotalWithdrawAmount(userService.countWithdrawAmount());
        adminCountVO.setTodayWithdrawAmount(withdrawService.countWithdrawAmountByDate(1));
        adminCountVO.setTodayWithdrawCount(withdrawService.countWithdrawNumByDate(1));
        adminCountVO.setTotalChargeAmount(userService.countChargeAmount());
        adminCountVO.setTodayChargeAmount(rechargeService.countChargeAmountByDate(1));
        adminCountVO.setTodayChargeCount(rechargeService.countChargeNumByDate(1));
        adminCountVO.setTodayFirstChargeCount(userService.countFirstChargeNumByDate(1));
        adminCountVO.setTodayFirstChargeAmount(rechargeService.countFirstChargeAmountByDate(1));
        adminCountVO.setTodayActiveCount(iSiteLoginLogService.countActiveUserByDate(1));
        adminCountVO.setYesterdayActiveCount(iSiteLoginLogService.countActiveUserByDate(2));
        adminCountVO.setAppendFollowPendingCount(followService.countAppendFollowPendingCount());
        adminCountVO.setChargePendingCount(rechargeService.countChargePendingCount());
        adminCountVO.setWithdrawPendingCount(withdrawService.countWithdrawPendingCount());
        adminCountVO.setRealNamePendingCount(userService.countRealNamePendingCount());
        return adminCountVO;
    }
}
