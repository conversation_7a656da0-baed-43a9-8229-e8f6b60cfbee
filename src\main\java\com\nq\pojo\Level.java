package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("level")
@ApiModel(description = "等级配置")
public class Level extends BaseEntity {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "等级名称", example = "团队长v1")
    private String levelName;

    @ApiModelProperty(value = "等级标识", example = "partner_11")
    private String levelCode;

    @ApiModelProperty(value = "有效跟单人数最小值", example = "1")
    private Integer minPeople;

    @ApiModelProperty(value = "有效跟单人数最大值", example = "2")
    private Integer maxPeople;

    @ApiModelProperty(value = "跟单金额最小值", example = "1.00")
    private BigDecimal minAmount;

    @ApiModelProperty(value = "跟单金额最大值", example = "2.00")
    private BigDecimal maxAmount;

    @ApiModelProperty(value = "仓位比例(%)", example = "0.1")
    private BigDecimal positionRate;

    @ApiModelProperty(value = "工资比例(%)", example = "0")
    private BigDecimal salaryRate;

    @ApiModelProperty(value = "等级图标")
    private String levelIcon;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")

    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "排序", example = "0")
    private Integer sort;
}