package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @TableName user_pendingorder
 */
@TableName(value = "user_pending_order")
@Data
@ApiModel(description = "用户挂单信息")
@AllArgsConstructor
@NoArgsConstructor
public class UserPendingOrder extends BaseEntity implements Serializable {
    @TableId(type = IdType.AUTO, value = "id")
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "用户ID", example = "1")
    private Integer userId;

    @ApiModelProperty(value = "真实姓名", example = "张三")
    private String nickName;

    @ApiModelProperty(value = "订单号", example = "ORDER202401010001")
    private String orderNo;

    @ApiModelProperty(value = "股票GID", example = "SH600519")
    private String stockGid;

    @ApiModelProperty(value = "股票代码", example = "600519")
    private String stockCode;

    @ApiModelProperty(value = "股票名称", example = "贵州茅台")
    private String stockName;

    @ApiModelProperty(value = "数量", example = "100")
    private Integer buyNum;

    @ApiModelProperty(value = "类型：0买，1卖", example = "0")
    private Integer buyType;

    @ApiModelProperty(value = "目标价格", example = "1000.00")
    private BigDecimal targetPrice;

    @ApiModelProperty(value = "添加时间", example = "2024-01-01 09:30:00")
    private Date addTime;

    @ApiModelProperty(value = "补仓ID", example = "1")
    private Integer positionId;

    @ApiModelProperty(value = "状态：0已挂单，1交易成功，2交易失败，3取消", example = "0")
    private Integer status;

    private static final long serialVersionUID = 1L;
}