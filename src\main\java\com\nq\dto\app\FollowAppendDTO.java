package com.nq.dto.app;

import com.nq.dto.common.CommonPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "根据跟单记录获取跟进记录详情")
public class FollowAppendDTO {
    
    @ApiModelProperty(value = "跟单订单号", example = "1")
    private Integer id;
    @ApiModelProperty(value = "金额", example = "200")
    private BigDecimal amount;
} 