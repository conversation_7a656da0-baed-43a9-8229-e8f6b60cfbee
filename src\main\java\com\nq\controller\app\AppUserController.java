package com.nq.controller.app;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.nq.common.ServerResponse;
import com.nq.pojo.StockSubscribe;
import com.nq.pojo.UserStockSubscribe;
import com.nq.service.*;
import com.nq.vo.app.AppConfigVO;
import com.nq.vo.user.UserAgentInfoVO;
import com.nq.vo.user.UserInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.http.util.TextUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping({"/user/"})
@Api(tags = "APP-用户操作接口")
public class AppUserController {
    private static final Logger log = LoggerFactory.getLogger(AppUserController.class);
    @Resource
    private IUserService iUserService;
    @Resource
    private IUserPositionService iUserPositionService;
    @Resource
    private IFileUploadService iFileUploadService;
    @Resource
    private IUserStockSubscribeService iUserStockSubscribeService;
    @Resource
    private IStockSubscribeService iStockSubscribeService;
    @Resource
    private AppConfigService appConfigService;

    @ApiOperation(value = "添加自选股", notes = "将股票添加到用户的自选股列表")
    @PostMapping({"addOption.do"})
    public ServerResponse<String> addOption(@ApiParam(value = "股票代码", required = true) @RequestParam("code") String code ) {
        this.iUserService.addOption(code);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation(value = "删除自选股", notes = "从用户的自选股列表中删除股票")
    @PostMapping({"delOption.do"})
    public ServerResponse<String> delOption(@ApiParam(value = "股票代码", required = true) @RequestParam("code") String code) {
        this.iUserService.delOption(code);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation(value = "检查是否自选股", notes = "检查股票是否在用户的自选股列表中")
    @PostMapping({"isOption.do"})
    public ServerResponse<Boolean> isOption(@ApiParam(value = "股票代码", required = true) @RequestParam("code") String code) {
        Boolean option = this.iUserService.isOption(code);
        return ServerResponse.createBySuccess(option);
    }

    @ApiOperation(value = "买入股票", notes = "用户下单买入股票")
    @PostMapping({"buy.do"})
    public ServerResponse<String> buy(@ApiParam(value = "股票ID", required = true) @RequestParam("stockId") Integer stockId,
                                      @ApiParam(value = "买入数量", required = true) @RequestParam("buyNum") Integer buyNum,
                                      @ApiParam(value = "买入价格", required = false) @RequestParam(value = "buyPrice", required = false) BigDecimal buyPrice,
                                      @ApiParam(value = "买入类型0 市价 1 现价", required = true) @RequestParam("orderType") Integer orderType
    ) throws Exception {
        this.iUserPositionService.buy(stockId, buyNum, buyPrice, orderType);
        return ServerResponse.createBySuccess();
    }


    @ApiOperation(value = "股票补仓", notes = "用户对现有持仓进行补仓操作")
    @PostMapping({"addPosition.do"})
    public ServerResponse<String> addPosition(@ApiParam(value = "持仓编号", required = true) @RequestParam("positionSn") String positionSn,
                                              @ApiParam(value = "补仓数量", required = true) @RequestParam("buyNum") Integer buyNum, @ApiParam(value = "补仓价格", required = false)
                                              @RequestParam(value = "buyPrice", required = false) BigDecimal buyPrice,
                                              @ApiParam(value = "买入类型0 市价 1 现价", required = true) @RequestParam("buyType") Integer buyType) throws Exception {
        this.iUserPositionService.addPosition(positionSn, buyNum, buyPrice, buyType);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation(value = "平仓操作", notes = "用户平仓操作")
    @PostMapping({"sell.do"})
    public ServerResponse<String> sell(@ApiParam(value = "持仓编号", required = true) @RequestParam("positionSn") String positionSn, @ApiParam(value = "可用持仓编号列表", required = false) @RequestParam("availablePositionSn") String availablePositionSn) {
        try {
            if (!TextUtils.isBlank(availablePositionSn)) {
                String[] list = availablePositionSn.split(",");
                for (String position : list) {
                    this.iUserPositionService.sell(position, 1);
                }
            } else {
                this.iUserPositionService.sell(positionSn, 1);
            }
        } catch (Exception e) {
            log.error("用户平仓操作 = {}", e);
        }
        return ServerResponse.createBySuccess();
    }


    @ApiOperation(value = "获取用户信息", notes = "查询当前登录用户的信息")
    @PostMapping({"getUserInfo.do"})
    public ServerResponse<UserInfoVO> getUserInfo() {
        UserInfoVO userInfo = this.iUserService.getUserInfo();
        AppConfigVO customerLink = appConfigService.queryConfig("customer_link");
        userInfo.setCustomerLink(customerLink.getConfigValue());
        return ServerResponse.createBySuccess(userInfo);
    }

    @ApiOperation(value = "修改登录密码", notes = "修改用户的登录密码")
    @GetMapping({"updatePwd.do"})
    public ServerResponse<String> updatePwd(@ApiParam(value = "旧密码", required = true) String oldPwd, @ApiParam(value = "新密码", required = true) String newPwd ) {
        this.iUserService.updatePwd(oldPwd, newPwd);
        return ServerResponse.createBySuccess();
    }
    @ApiOperation(value = "设置提现密码", notes = "设置提现密码")
    @PostMapping({"setWithPwd.do"})
    public ServerResponse<String> setWithPwd( @ApiParam(value = "提现密码", required = true) @RequestParam("pwd")  String pwd) {
        this.iUserService.setWithPwd(pwd);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation(value = "修改提现密码", notes = "修改提现密码")
    @PostMapping({"updateWithPwd.do"})
    public ServerResponse<String> updateWithPwd( @ApiParam(value = "旧密码", required = true)  @RequestParam("oldPwd") String oldPwd, @ApiParam(value = "新密码", required = true) @RequestParam("newPwd") String newPwd) {
        this.iUserService.updateWithPwd(oldPwd,newPwd);
        return ServerResponse.createBySuccess();
    }




    @ApiOperation(value = "实名认证", notes = "用户提交实名认证信息")
    @PostMapping({"auth.do"})
    public ServerResponse<String> auth(@ApiParam(value = "真实姓名", required = true) String realName, @ApiParam(value = "身份证号", required = true) String idCard, @ApiParam(value = "身份证正面照片", required = true) String img1key, @ApiParam(value = "身份证反面照片", required = true) String img2key, @ApiParam(value = "手持身份证照片", required = true) String img3key) {
        this.iUserService.auth(realName, idCard, img1key, img2key, img3key);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation(value = "上传图片", notes = "上传用户认证所需的图片")
    @PostMapping({"upload.do"})
    public ServerResponse<HashMap> upload(@ApiParam(value = "上传的文件", required = true) @RequestParam(value = "upload_file", required = false) MultipartFile file) {
        String fileName = this.iFileUploadService.upload(file);
        HashMap<String, String> fileMap = Maps.newHashMap();
        fileMap.put("uri", this.iFileUploadService.generUri(fileName));
        fileMap.put("url", this.iFileUploadService.generUrl(fileName));
        return ServerResponse.createBySuccess(fileMap);
    }

    @ApiOperation(value = "获取新股列表", notes = "分页查询新股申购列表")
    @PostMapping({"newStockList.do"})
    public ServerResponse<PageInfo<StockSubscribe>> list(@ApiParam(value = "页码", defaultValue = "1") @RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @ApiParam(value = "每页数量", defaultValue = "10") @RequestParam(value = "pageSize", defaultValue = "10") int pageSize, @ApiParam(value = "股票名称", required = false) @RequestParam(value = "name", required = false) String name, @ApiParam(value = "股票代码", required = false) @RequestParam(value = "code", required = false) String code, @ApiParam(value = "状态", required = false) @RequestParam(value = "zt", required = false) Integer zt, @ApiParam(value = "是否锁定", required = false) @RequestParam(value = "isLock", required = false) Integer isLock, @ApiParam(value = "类型", required = false) @RequestParam(value = "type", required = false) Integer type,  @ApiParam(value = "申购类型", required = false) @RequestParam(value = "status", required = false) Integer status) {
        PageInfo<StockSubscribe> pageInfo = this.iStockSubscribeService.list(pageNum, pageSize, name, code, zt, isLock, type, status);
        return ServerResponse.createBySuccess(pageInfo);
    }

    @ApiOperation(value = "添加新股申购", notes = "用户添加新股申购记录")
    @PostMapping({"add.do"})
    public ServerResponse<String> add(@ApiParam(value = "新股申购信息", required = true) UserStockSubscribe model) throws Exception {
        this.iUserStockSubscribeService.insert(model);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation(value = "获取用户新股申购记录", notes = "查询用户的申购记录")
    @PostMapping({"getOneSubscribeByUserId.do"})
    public ServerResponse<List<UserStockSubscribe>> getOneSubscribeByUserId(@ApiParam(value = "类型", required = false) @RequestParam(value = "type", required = false) String type) {
        List<UserStockSubscribe> dtos = this.iUserStockSubscribeService.getOneSubscribeByUserId(type);
        return ServerResponse.createBySuccess(dtos);
    }

    @ApiOperation(value = "获取中签记录", notes = "查询用户的新股中签记录")
    @PostMapping({"getzqjl.do"})
    public ServerResponse<List<UserStockSubscribe>> getOneSubscribeByUserIdAndType() {
        List<UserStockSubscribe> getzqjkl = this.iUserStockSubscribeService.getzqjkl();
        return ServerResponse.createBySuccess(getzqjkl);
    }

    @ApiOperation(value = "提交申购金额", notes = "用户提交新股申购金额")
    @PostMapping({"submitSubscribe.do"})
    public ServerResponse<String> userSubmit(@ApiParam(value = "申购ID", required = true) @RequestParam("id") Integer id) {
        this.iUserStockSubscribeService.userSubmit(id);
        return ServerResponse.createBySuccess();
    }


    @ApiOperation(value = "获取新股抢筹列表", notes = "查询可抢筹的新股列表")
    @PostMapping({"newStockQc.do"})
    public ServerResponse<List<StockSubscribe>> newStockQc() {
        List<StockSubscribe> stockSubscribes = this.iStockSubscribeService.newStockQc();
        return ServerResponse.createBySuccess(stockSubscribes);
    }

    @ApiOperation(value = "新股抢筹下单", notes = "用户进行新股抢筹下单")
    @PostMapping({"buyNewStockQc.do"})
    public ServerResponse<String> buyNewStockQc(@ApiParam(value = "股票代码", required = true) @RequestParam("code") String code, @ApiParam(value = "数量", required = true) @RequestParam("num") Integer num) {
        this.iUserStockSubscribeService.buyNewStockQc(code, num);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation(value = "VIP抢筹下单", notes = "用户进行VIP抢筹下单")
    @PostMapping({"buyVipQc.do"})
    public ServerResponse<String> buyVipQc(@ApiParam(value = "股票代码", required = true) @RequestParam("stockCode") String stockCode, @ApiParam(value = "买入数量", required = true) @RequestParam("buyNum") Integer buyNum, @ApiParam(value = "买入类型", required = true) @RequestParam("buyType") Integer buyType, @ApiParam(value = "杠杆倍数", required = true) @RequestParam("lever") Integer lever, @ApiParam(value = "止盈价格", required = false) @RequestParam(value = "profitTarget", required = false) BigDecimal profitTarget, @ApiParam(value = "止损价格", required = false) @RequestParam(value = "stopTarget", required = false) BigDecimal stopTarget) throws Exception {
        this.iUserPositionService.buyVipQc(stockCode, buyNum, buyType, lever, profitTarget, stopTarget);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation(value = "查询累类返佣和累计线下人数", notes = "查询累类返佣和累计线下人数")
    @PostMapping({"getUserAgentInfoVO.do"})
    public ServerResponse<UserAgentInfoVO> getUserAgentInfoVO() throws Exception {
        UserAgentInfoVO vo = iUserService.getUserAgentInfoVO();
        return ServerResponse.createBySuccess(vo);
    }

    @ApiOperation(value = "总资产转沪深账户", notes = "将总资产转入沪深账户")
    @PostMapping("/transferToEnable")
    public ServerResponse<String> transferToEnable(
            @ApiParam(value = "转账金额", required = true) @RequestParam("amount") BigDecimal amount) {
        this.iUserService.transferToEnable(amount);
        return ServerResponse.createBySuccess("转账成功");
    }

    @ApiOperation(value = "沪深账户转总资产", notes = "将沪深账户资金转回总资产")
    @PostMapping("/transferToTotal")
    public ServerResponse<String> transferToTotal(
            @ApiParam(value = "转账金额", required = true) @RequestParam("amount") BigDecimal amount) {
        this.iUserService.transferToTotal(amount);
        return ServerResponse.createBySuccess("转账成功");
    }

}
