package com.nq.vo.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "用户银行卡信息视图对象")
public class UserBankInfoVO {

    @ApiModelProperty(value = "银行卡ID", example = "1")
    private Integer id;
    @ApiModelProperty(value = "真实姓名", example = "张三")
    private String realName;

    @ApiModelProperty(value = "银行名称", example = "中国银行")
    private String bankName;

    @ApiModelProperty(value = "银行卡号", example = "6222021234567890123")
    private String bankNo;

    @ApiModelProperty(value = "开户行地址", example = "北京市海淀区中关村支行")
    private String bankAddress;

}

