package com.nq.controller.admin;

import com.github.pagehelper.PageInfo;
import com.nq.annotation.RepeatSubmit;
import com.nq.common.ServerResponse;
import com.nq.pojo.FollowTrade;
import com.nq.pojo.Stock;
import com.nq.service.FollowTradeService;
import com.nq.vo.admin.FollowTradeVO;
import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/admin/follow/trade")
@Api(tags = "后台-跟单交易管理")
public class AdminFollowTradeController {

    @Resource
    private FollowTradeService followTradeService;

    @GetMapping("/list.do")
    @ApiOperation("获取跟单交易列表")
    public ServerResponse<PageInfo<FollowTradeVO>> getFollowTradeList(
            @ApiParam(value = "页码", required = true, example = "1") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页数量", required = true, example = "10") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam(value = "导师ID", example = "1") @RequestParam(required = false) Integer mentorId,
            @ApiParam(value = "股票代码", example = "000001") @RequestParam(required = false) String stockCode,
            @ApiParam(value = "状态(1-持仓中，2-已清仓)", example = "1") @RequestParam(required = false) Integer status) {
        PageInfo<FollowTradeVO> pageInfo = followTradeService.getFollowTradeList(pageNum, pageSize, mentorId, stockCode,
                status);
        return ServerResponse.createBySuccess(pageInfo);
    }

    @PostMapping("/detail.do")
    @ApiOperation("获取跟单交易详情")
    public ServerResponse<FollowTradeVO> getFollowTradeDetail(@RequestBody FollowTrade followTrade) {
        FollowTradeVO vo = followTradeService.getFollowTradeById(followTrade.getId());
        return ServerResponse.createBySuccess(vo);
    }

    @PostMapping("/create.do")
    @ApiOperation("创建跟单交易")
    @RepeatSubmit
    public ServerResponse<Boolean> createFollowTrade(@RequestBody FollowTrade followTrade) {
        boolean result = followTradeService.createFollowTrade(followTrade);
        return ServerResponse.createBySuccess(result);
    }

    @PostMapping("/update.do")
    @ApiOperation("更新跟单交易")
    public ServerResponse<Boolean> updateFollowTrade(@RequestBody FollowTrade followTrade) {
        boolean result = followTradeService.updateFollowTrade(followTrade);
        return ServerResponse.createBySuccess(result);
    }

    @GetMapping("/delete.do")
    @ApiOperation("删除跟单交易")
    public ServerResponse<Boolean> deleteFollowTrade(@RequestParam(defaultValue = "0") Integer id) {
        boolean result = followTradeService.deleteFollowTrade(id);
        return ServerResponse.createBySuccess(result);
    }

    @PostMapping("/publish.do")
    @ApiOperation("发布跟单交易")
    public ServerResponse<Boolean> publishFollowTrade(@RequestBody FollowTrade followTrade) {
        boolean result = followTradeService.publishFollowTrade(followTrade.getId());
        return ServerResponse.createBySuccess(result);
    }

    @PostMapping("/close.do")
    @ApiOperation("清仓跟单交易")
    public ServerResponse<Boolean> closeFollowTrade(@RequestBody FollowTrade followTrade) {
        System.out.println("接收到清仓请求: " + followTrade);

        // 确保 stockCode 字段不会被错误设置
        Integer id = followTrade.getId();
        if (id != null && followTrade.getStockCode() != null && followTrade.getStockCode().equals(id.toString())) {
            followTrade.setStockCode(null); // 如果 stockCode 等于 id，则置为 null，让服务层从数据库获取
        }

        // 获取原始数据，确保 stockCode 字段正确
        if (id != null) {
            FollowTrade originalTrade = followTradeService.getById(id);
            if (originalTrade != null) {
                // 如果 stockCode 为空或等于 id，则使用原始数据中的 stockCode
                if (followTrade.getStockCode() == null || followTrade.getStockCode().equals(id.toString())) {
                    followTrade.setStockCode(originalTrade.getStockCode());
                    followTrade.setStockName(originalTrade.getStockName());
                }
            }
        }

        boolean result = followTradeService.closeFollowTrade(followTrade.getId(), followTrade.getSellPrice());
        return ServerResponse.createBySuccess(result);
    }

    @GetMapping("/mentor-holding-stocks.do")
    @ApiOperation("获取导师持仓中的股票列表")
    public ServerResponse<List<String>> getMentorHoldingStocks(
            @ApiParam(value = "导师ID", required = true, example = "1") @RequestParam Integer mentorId) {
        List<String> stockList = followTradeService.getMentorHoldingStocks(mentorId);
        return ServerResponse.createBySuccess(stockList);
    }

    @GetMapping("/search-stocks.do")
    @ApiOperation("根据关键字查询股票")
    public ServerResponse<List<Stock>> searchStocks(
            @ApiParam(value = "关键字（股票代码或股票名称）", required = true, example = "平安") @RequestParam String keyword,
            @ApiParam(value = "限制返回数量", example = "10") @RequestParam(required = false, defaultValue = "10") Integer limit) {
        List<Stock> stockList = followTradeService.searchStocks(keyword, limit);
        return ServerResponse.createBySuccess(stockList);
    }
}