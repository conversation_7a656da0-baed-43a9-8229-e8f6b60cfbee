package com.nq.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nq.pojo.UserPosition;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
public interface UserPositionMapper extends BaseMapper<UserPosition> {

  
  UserPosition findPositionBySn(String positionSn);

  int CountPositionNum(@Param("state") Integer state);
  
  BigDecimal CountPositionProfitAndLose();
  
  BigDecimal CountPositionAllProfitAndLose();
  
  int deleteByUserId(@Param("userId") Integer userId);
  




}
