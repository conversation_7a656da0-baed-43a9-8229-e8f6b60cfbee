package com.nq.controller.admin;


import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.nq.common.ServerResponse;
import com.nq.pojo.SiteAdmin;
import com.nq.service.IFileUploadService;
import com.nq.service.ISiteAdminService;
import java.util.HashMap;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


@RestController
@RequestMapping({"/admin/"})
@Api(tags = "后台-管理员管理")
public class AdminController {

    private static final Logger log = LoggerFactory.getLogger(AdminController.class);


    @Resource
    private ISiteAdminService iSiteAdminService;


    @Resource
    private IFileUploadService iFileUploadService;

    @ApiOperation("分页查询管理设置 所有管理列表数据及模糊查询")
    @PostMapping({"list.do"})
    public ServerResponse<PageInfo<SiteAdmin>> list(@RequestParam(value = "adminName", required = false) String adminName, @RequestParam(value = "adminPhone", required = false) String adminPhone, @RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "10") int pageSize, HttpServletRequest request) {
        PageInfo<SiteAdmin> siteAdminPageInfo = this.iSiteAdminService.listByAdmin(adminName, adminPhone, request, pageNum, pageSize);
        return ServerResponse.createBySuccess(siteAdminPageInfo);
    }

    @ApiOperation("修改管理员状态 锁定管理员/解锁管理员")
    @PostMapping({"updateLock.do"})
    public ServerResponse<String> updateLock(Integer adminId) {
        this.iSiteAdminService.updateLock(adminId);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation("管理设置 添加管理员")
    @PostMapping({"add.do"})
    public ServerResponse<String> add(SiteAdmin siteAdmin) {
        this.iSiteAdminService.add(siteAdmin);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation("管理设置 修改管理员密码")
    @PostMapping({"update.do"})
    public ServerResponse<String> update(SiteAdmin siteAdmin) {
        this.iSiteAdminService.update(siteAdmin);
        return ServerResponse.createBySuccess();
    }



    @ApiOperation("处理图片上传")
    @PostMapping({"upload.do"})
    public ServerResponse upload(HttpSession session, @RequestParam(value = "upload_file", required = false) MultipartFile file, HttpServletRequest request) {
        String fileName = this.iFileUploadService.upload(file);
        HashMap<String, String> fileMap = Maps.newHashMap();
        fileMap.put("uri", this.iFileUploadService.generUri(fileName));
        fileMap.put("url", this.iFileUploadService.generUrl(fileName));
        return ServerResponse.createBySuccess(fileMap);
    }
    
}
