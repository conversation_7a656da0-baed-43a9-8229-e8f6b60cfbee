package com.nq.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nq.constant.OrderConstant;
import com.nq.dao.*;
import com.nq.enums.OrderTypeEnum;
import com.nq.enums.ResultCode;
import com.nq.enums.TypeEnum;
import com.nq.excepton.CustomException;
import com.nq.function.AmountConsumer;
import com.nq.manage.UserAmountChangeManage;
import com.nq.pojo.*;
import com.nq.service.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.nq.common.ServerResponse;
import com.nq.utils.*;
import com.nq.utils.stock.BuyAndSellUtils;
import com.nq.utils.stock.GeneratePosition;
import com.nq.utils.stock.sina.SinaStockApi;
import com.nq.vo.position.PositionProfitVO;
import com.nq.vo.position.UserPositionVO;
import com.nq.vo.stock.StockListVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;


import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

@Service("iUserPositionService")
public class UserPositionServiceImpl extends ServiceImpl<UserPositionMapper, UserPosition> implements IUserPositionService {
    private static final Logger log = LoggerFactory.getLogger(UserPositionServiceImpl.class);
    @Resource
    private UserPositionMapper userPositionMapper;
    @Resource
    private IUserService iUserService;
    @Resource
    private ISiteSettingService iSiteSettingService;

    @Resource
    private IStockService iStockService;
    @Resource
    private UserMapper userMapper;

    @Resource
    private StockMapper stockMapper;

    @Resource
    private ISiteProductService iSiteProductService;
    @Resource
    private UserStockSubscribeMapper userStockSubscribeMapper;
    @Resource
    private StockSubscribeMapper stockSubscribeMapper;
    @Resource
    private SiteMessageMapper siteMessageMapper;
    @Resource
    private UserAmountChangeManage userAmountChangeManage;
    @Resource
    private UserPendingOrderService userPendingOrderService;
    @Resource
    private RedisLockUtil redisLockUtil;

    @Transactional
    public void buy(Integer stockId, Integer buyNum, BigDecimal buyPrice, Integer orderType) throws Exception {
        doBuy(stockId, buyNum, buyPrice, orderType, null);
    }


    @Override
    @Transactional
    public void addPosition(String positionSn, Integer buyNum, BigDecimal buyPrice, Integer orderType) throws Exception {
        UserPosition position = this.userPositionMapper.findPositionBySn(positionSn);
        if (position == null) {
            throw new CustomException("补仓失败，原持仓单不存在");
        }
        Stock stock = this.iStockService.findStockByCode(position.getStockCode());
        doBuy(stock.getId(), buyNum, buyPrice, orderType, position.getId());
    }

    private void doBuy(Integer stockId, Integer buyNum, BigDecimal buyPrice, Integer orderType, Integer positionId) throws Exception {
        // 判断周末不能买
        Date today = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(today);
        /*实名认证开关开启*/
        SiteProduct siteProduct = iSiteProductService.getProductSetting();
        User user = this.iUserService.getCurrentUser();
        if (siteProduct.getRealNameDisplay() && (StringUtils.isBlank(user.getRealName()) || StringUtils.isBlank(user.getIdCard()))) {
            throw new CustomException(ResultCode.REAL_NAME_NOT_AUTHENTICATION.getCode(), "下单失败，请先实名认证");
        }

        BigDecimal user_enable_amt = user.getEnableAmt();

        if (siteProduct.getRealNameDisplay() && user.getIsLock() == 1) {
            throw new CustomException("下单失败，账户已被锁定");
        }
        SiteSetting siteSetting = this.iSiteSettingService.getSiteSetting();
        if (siteSetting == null) {
            throw new CustomException("下单失败，系统设置错误");
        }
        log.debug("获取到系统设置: {}", siteSetting);
        Stock stock = this.iStockService.findStockById(stockId);
        if (stock == null) {
            throw new CustomException("下单失败，股票代码错误");
        }
        log.info("查询到股票信息: stockCode={}, stockName={}, stockType={}", stock.getStockCode(), stock.getStockName(), stock.getStockType());

        String am_begin = siteSetting.getTransAmBegin();
        String am_end = siteSetting.getTransAmEnd();
        String pm_begin = siteSetting.getTransPmBegin();
        String pm_end = siteSetting.getTransPmEnd();
        boolean am_flag = BuyAndSellUtils.isTransTime(am_begin, am_end);
        boolean pm_flag = BuyAndSellUtils.isTransTime(pm_begin, pm_end);
        log.info("是否在上午交易时间 = {} 是否在下午交易时间 = {}", am_flag, pm_flag);
        //hyh
        if (!am_flag && !pm_flag) {
            throw new CustomException("下单失败，不在交易时段内");
        }
        if (siteProduct.getHolidayDisplay()) {
            throw new CustomException("周末或节假日不能交易!");
        }
        //这个挂单去掉吧 直接购买就好了 然后取挂单
        if (stock.getIsLock() != 0) {
            log.warn("股票 {} 已被锁定，不能交易", stock.getStockCode());
            throw new CustomException("下单失败，当前股票不能交易");
        }
        BigDecimal now_price;
        StockListVO stockListVO = SinaStockApi.assembleStockListVO(SinaStockApi.getSinaStock(stock.getStockGid()));
        //股票类型 现价 数据源的处理
        //如果有买入价格，以用户的买入价格为准
        if (buyPrice != null) {
            now_price = buyPrice;
        } else {
            now_price = stockListVO.getNowPrice();
        }
        log.info("当前股票={}的价格={}", stock.getStockCode(), now_price);
        if (now_price.compareTo(new BigDecimal("0")) == 0) {
            throw new CustomException("报价0，请稍后再试");
        }
        double stock_crease = stockListVO.getHcrate().doubleValue();
        BigDecimal maxRisePercent = new BigDecimal("0");
        if (stock.getStockPlate() != null) {
            maxRisePercent = new BigDecimal("0.2");
            log.info("【科创股票】");
        } else {
            maxRisePercent = new BigDecimal("0.1");
            log.info("【普通A股】");
        }
        if (stockListVO.getName().startsWith("ST") || stockListVO.getName().endsWith("退")) {
            throw new CustomException("ST和已退市的股票不能入仓");
        }
        BigDecimal zsPrice = new BigDecimal(stockListVO.getPreclose_px());
        BigDecimal ztPrice = zsPrice.multiply(maxRisePercent).add(zsPrice).setScale(2, 4);
        BigDecimal chaPrice = ztPrice.subtract(zsPrice);
        BigDecimal ztRate = chaPrice.multiply(new BigDecimal("100")).divide(zsPrice, 2, 4);
        log.info("当前涨跌幅 = {} % , 涨停幅度 = {} %", Double.valueOf(stock_crease), ztRate);

        if (stock.getStockPlate() == null || StringUtils.isEmpty(stock.getStockPlate())) {
            int crease = siteSetting.getCreaseMaxPercent().intValue();
            log.info("当前股票={}的最大涨幅={}", stock.getStockCode(), crease);
            if (stock_crease > 0.0D && stock_crease >= crease) {
                throw new CustomException("下单失败，股票当前涨幅:" + stock_crease + ",大于最大涨幅:" + crease);
            }
            if (stock_crease < 0.0D && -stock_crease > crease) {
                throw new CustomException("下单失败，股票当前跌幅:" + stock_crease + ",大于最大跌幅:" + crease);

            }
        } else if ("创业".equals(stock.getStockPlate())) {
            int maxcrease = siteSetting.getCyCreaseMaxPercent().intValue();
            log.info("当前创业股={}的最大涨幅={}", stock.getStockCode(), maxcrease);
            if (stock_crease > 0.0D && stock_crease >= maxcrease) {
                throw new CustomException("下单失败，创业股当前涨幅:" + stock_crease + ",大于最大涨幅:" + maxcrease);
            }
            if (stock_crease < 0.0D && -stock_crease > maxcrease) {
                throw new CustomException("下单失败，创业股当前跌幅:" + stock_crease + ",大于最大跌幅:" + maxcrease);
            }
        } else {
            int maxcrease = siteSetting.getKcCreaseMaxPercent().intValue();
            log.info("当前科创股={}的最大涨幅={}", stock.getStockCode(), maxcrease);
            if (stock_crease > 0.0D && stock_crease >= maxcrease) {
                throw new CustomException("下单失败，科创股当前涨幅:" + stock_crease + ",大于最大涨幅:" + maxcrease);
            }
            if (stock_crease < 0.0D && -stock_crease > maxcrease) {
                throw new CustomException("下单失败，科创股当前跌幅:" + stock_crease + ",大于最大跌幅:" + maxcrease);
            }
        }
        int actualBuyNum = buyNum;
        // 实际支付金额
        BigDecimal orderPrice = now_price.multiply(new BigDecimal(actualBuyNum));
        BigDecimal buy_amt_autual = now_price.multiply(new BigDecimal(buyNum));
        int compareUserAmtInt = user_enable_amt.compareTo(buy_amt_autual);
        if (compareUserAmtInt == -1) {
            throw new CustomException("下单失败，余额不足");
        }
        //看看是是市价还是现价 买入类型0 市价 1 限价
        //市价走原来的逻辑
        if (orderType == 0) {
            //添加账变
            String orderNo = SnowIdUtil.getId(OrderConstant.userPositionOrder);
            AmountConsumer amountConsumer = (oldUser, newUser) -> {
                UserPosition userPosition = new UserPosition();
                userPosition.setPositionSn(orderNo);
                userPosition.setUserId(user.getId());
                userPosition.setNickName(user.getRealName());
                userPosition.setStockCode(stock.getStockCode());
                userPosition.setStockName(stock.getStockName());
                userPosition.setStockGid(stock.getStockGid());
                userPosition.setStockSpell(stock.getStockSpell());
                userPosition.setBuyOrderId(GeneratePosition.getPositionId());
                userPosition.setBuyOrderTime(new Date());
                userPosition.setBuyOrderPrice(now_price);
                userPosition.setOrderDirection("买涨");
                userPosition.setOrderNum(actualBuyNum);
                userPosition.setStockPlate(stock.getStockPlate());
                userPosition.setIsLock(0);
                userPosition.setPositionAppendId(positionId);
                userPosition.setOrderTotalPrice(buy_amt_autual);
                BigDecimal buy_fee_amt = orderPrice.multiply(siteSetting.getBuyFee()).setScale(2, 4);
                userPosition.setOrderFee(buy_fee_amt);
                BigDecimal profit_and_lose = new BigDecimal("0");
                userPosition.setProfitAndLose(profit_and_lose);
                BigDecimal all_profit_and_lose = profit_and_lose.subtract(buy_fee_amt);
                userPosition.setAllProfitAndLose(all_profit_and_lose);
                this.userPositionMapper.insert(userPosition);
            };
            userAmountChangeManage.changeBalance(user.getId(), buy_amt_autual, orderNo, OrderTypeEnum.STOCK, TypeEnum.STOCK_BUY, "", "", amountConsumer);
        } else if (orderType == 1) {
            String orderNo = SnowIdUtil.getId(OrderConstant.userPendingOrder);
            AmountConsumer amountConsumer = (oldUser, newUser) -> {
                UserPendingOrder userPendingOrder = new UserPendingOrder();
                userPendingOrder.setUserId(user.getId());
                userPendingOrder.setNickName(user.getRealName());
                userPendingOrder.setOrderNo(orderNo);
                userPendingOrder.setStockCode(stock.getStockCode());
                userPendingOrder.setStockName(stock.getStockName());
                userPendingOrder.setStockGid(stock.getStockGid());
                userPendingOrder.setBuyNum(buyNum);
                userPendingOrder.setTargetPrice(now_price);
                //类型0买，1卖
                userPendingOrder.setBuyType(0);
                userPendingOrder.setAddTime(new Date());
                userPendingOrder.setStatus(0);
                userPendingOrderService.save(userPendingOrder);
            };
            userAmountChangeManage.changeBalance(user.getId(), buy_amt_autual, orderNo, OrderTypeEnum.STOCK, TypeEnum.BUY_PING_STOCK, "", "", amountConsumer);
            //用户下单站内信
            SiteMessage siteMessage = new SiteMessage();
            siteMessage.setUserId(user.getId());
            siteMessage.setMessageType(SiteMessage.MSG_TYPE_ADMIN);
            siteMessage.setStatus(1);
            siteMessage.setUserName(user.getRealName());
            siteMessage.setAddTime(new Date());
            siteMessage.setTypeName("用户委托股票交易");
            siteMessage.setContent("用户【" + user.getRealName() + "】下单");
            this.siteMessageMapper.insert(siteMessage);
            log.info("【委托股票教】成功");
        }
    }

    public void sell(String positionSn, int doType) throws Exception {
        log.info("开始执行平仓操作 - 订单号={}, 操作类型={}", positionSn, doType);

        SiteSetting siteSetting = this.iSiteSettingService.getSiteSetting();
        if (siteSetting == null) {
            log.error("平仓出错，网站设置表不存在");
            throw new CustomException("下单失败，系统设置错误");
        }
        SiteProduct siteProduct = iSiteProductService.getProductSetting();
        UserPosition userPosition = this.userPositionMapper.findPositionBySn(positionSn);
        if (doType != 0) {
            String am_begin = siteSetting.getTransAmBegin();
            String am_end = siteSetting.getTransAmEnd();
            String pm_begin = siteSetting.getTransPmBegin();
            String pm_end = siteSetting.getTransPmEnd();
            boolean am_flag = BuyAndSellUtils.isTransTime(am_begin, am_end);
            boolean pm_flag = BuyAndSellUtils.isTransTime(pm_begin, pm_end);
            log.info("A股是否在上午交易时间 = {} 是否在下午交易时间 = {}", Boolean.valueOf(am_flag), Boolean.valueOf(pm_flag));
            if (!am_flag && !pm_flag) {
                throw new Exception("平仓失败，不在交易时段内");
            }
        }
        if (siteProduct.getHolidayDisplay()) {
            throw new CustomException("周末或节假日不能交易！");
        }
        if (userPosition == null) {
            throw new CustomException("平仓失败，订单不存在");
        }
        User user = this.userMapper.selectById(userPosition.getUserId());
        if (user == null) {
            throw new CustomException("平仓失败，用户不存在");
        }
        /*实名认证开关开启*/
        if (siteProduct.getRealNameDisplay() && user.getIsLock() == 1) {
            log.info("用户已被锁定，平仓失败");
            throw new CustomException("平仓失败，用户已被锁定");
        }
        if (StrUtil.isNotEmpty(userPosition.getSellOrderId())) {
            throw new CustomException("平仓失败，此订单已平仓");
        }
        if (1 == userPosition.getIsLock()) {
            throw new CustomException("平仓失败 " + userPosition.getLockMsg());
        }

        if (doType == 1) {
            int cantSellTime = siteSetting.getCantSellTimes();
            log.info("不能平仓时间={}", cantSellTime);
            if (cantSellTime == -1) {
                if (DateTimeUtil.isSameDay(userPosition.getBuyOrderTime(), DateTimeUtil.getCurrentDate())) {
                    throw new CustomException("隔日才能卖出");
                }
            } else {
                if (!DateTimeUtil.isCanSell(userPosition.getBuyOrderTime(), cantSellTime)) {
                    throw new CustomException(cantSellTime + "分钟内不能平仓");
                }
            }
        }
        BigDecimal now_price;
        //股票卖出的 价格 数据源
        StockListVO stockListVO = SinaStockApi.assembleStockListVO(SinaStockApi.getSinaStock(userPosition.getStockGid()));
        now_price = stockListVO.getNowPrice();
        if (now_price.compareTo(new BigDecimal("0")) != 1) {
            log.error("股票 = {} 收到报价 = {}", userPosition.getStockName(), now_price);
            throw new CustomException("报价0，平仓失败，请稍后再试");
        }

        double stock_crease = stockListVO.getHcrate().doubleValue();
        BigDecimal zsPrice = new BigDecimal(stockListVO.getPreclose_px());
        BigDecimal ztPrice = zsPrice.multiply(new BigDecimal("0.1")).add(zsPrice);
        ztPrice = ztPrice.setScale(2, 4);
        BigDecimal chaPrice = ztPrice.subtract(zsPrice);
        BigDecimal ztRate = chaPrice.multiply(new BigDecimal("100")).divide(zsPrice, 2, 4);
        ztRate = ztRate.negate();
        log.info("股票当前涨跌幅 = {} 跌停幅度 = {}", Double.valueOf(stock_crease), ztRate);
        if ((new BigDecimal(String.valueOf(stock_crease))).compareTo(ztRate) == 0 && "买涨".equals(userPosition.getOrderDirection())) {
            throw new CustomException("当前股票已跌停不能卖出");
        }
        Integer buy_num = userPosition.getOrderNum();
        BigDecimal all_buy_amt = userPosition.getBuyOrderPrice().multiply(new BigDecimal(buy_num));
        BigDecimal all_sell_amt = now_price.multiply(new BigDecimal(buy_num));
        BigDecimal profitLoss = new BigDecimal("0");
        if ("买涨".equals(userPosition.getOrderDirection())) {
            profitLoss = all_sell_amt.subtract(all_buy_amt);
        } else {
            profitLoss = all_buy_amt.subtract(all_sell_amt);
        }
        BigDecimal buy_fee_amt = userPosition.getOrderFee();
        log.info("买入手续费 = {}", buy_fee_amt);
        BigDecimal sell_fee_amt = all_sell_amt.multiply(siteSetting.getSellFee()).setScale(2, RoundingMode.HALF_UP);
        log.info("卖出手续费 = {}", sell_fee_amt);
        //总手续费= 买入手续费+卖出手续费+印花税+递延费+点差费
        userPosition.setSellOrderId(GeneratePosition.getPositionId());
        userPosition.setSellOrderPrice(now_price);
        userPosition.setSellOrderTime(new Date());
        BigDecimal order_fee_all = buy_fee_amt.add(sell_fee_amt);
        userPosition.setOrderFee(order_fee_all);
        userPosition.setProfitAndLose(profitLoss);
        BigDecimal all_profit = profitLoss.subtract(order_fee_all);
        userPosition.setAllProfitAndLose(all_profit);
        //变化金额
        //修改用户可用+总盈亏+买入总金额
        AmountConsumer amountConsumer = (oldUser, newUser) -> {
            this.userPositionMapper.updateById(userPosition);

        };
        userAmountChangeManage.changeBalance(user.getId(), all_profit, userPosition.getPositionSn(), OrderTypeEnum.STOCK, TypeEnum.STOCK_SELL, "", "", amountConsumer);
    }


    public void lock(Integer positionId, Integer state, String lockMsg) {
        if (positionId == null || state == null) {
            throw new CustomException("参数不能为空");
        }

        UserPosition position = this.userPositionMapper.selectById(positionId);
        if (position == null) {
            throw new CustomException("持仓不存在");
        }

        if (StrUtil.isNotEmpty(position.getSellOrderId()) && !"apply".equals(position.getSellOrderId()) && !"reject".equals(position.getSellOrderId()) && !"append".equals(position.getSellOrderId())) {
            throw new CustomException("平仓单不能锁仓");
        }

        if (state == 1 && StringUtils.isBlank(lockMsg)) {
            throw new CustomException("锁仓提示信息必填");
        }
        if (state == 1) {
            position.setIsLock(1);
            position.setLockMsg(lockMsg);
        } else {
            position.setIsLock(0);
        }
        int updateCount = this.userPositionMapper.updateById(position);
        if (updateCount <= 0) {
            throw new CustomException("操作失败");
        }
    }

    public void del(Integer positionId) {
        if (positionId == null) {
            throw new CustomException("参数不能为空");
        }
        UserPosition position = this.userPositionMapper.selectById(positionId);
        if (position == null) {
            ServerResponse.createByErrorMsg("该持仓不存在");
        }
        int updateCount = this.userPositionMapper.deleteById(positionId);
        if (updateCount <= 0) {
            throw new CustomException("删除失败");
        }
    }

    @Override
    public PageInfo<UserPositionVO> findPositionByCodeByState(Integer userId, String stockCode, String stockSpell, Integer
            state, int pageNum, int pageSize) {
        //持仓 合并的
        if (state == 0) {
            return getHoldingPositions(userId, stockCode, stockSpell, state, pageNum, pageSize);
        }
        //平仓
        if (state == 1) {
            return findPCPosition(userId, stockCode, stockSpell, state, pageNum, pageSize);
        }
        //挂单
        if (state == 2) {
            PageInfo<UserPendingOrder> myPendingOrder = userPendingOrderService.findMyPendingOrder(userId, stockCode, pageNum, pageSize);
            List<UserPendingOrder> arr = myPendingOrder.getList();
            List<UserPositionVO> resultArr = CollectionUtil.newArrayList();

            for (UserPendingOrder userPendingOrder : arr) {
                UserPositionVO userPositionVO = new UserPositionVO();
                userPositionVO.setId(userPendingOrder.getId());
                userPositionVO.setOrderNum(userPendingOrder.getBuyNum());
                userPositionVO.setUserId(userPendingOrder.getUserId());
                userPositionVO.setPositionSn(userPendingOrder.getOrderNo());
                userPositionVO.setBuyOrderPrice(userPendingOrder.getTargetPrice());
                userPositionVO.setStockCode(userPendingOrder.getStockCode());
                userPositionVO.setStockName(userPendingOrder.getStockName());
                userPositionVO.setNow_price(userPendingOrder.getTargetPrice());
                userPositionVO.setBuyType(userPendingOrder.getBuyType());
                userPositionVO.setCreateTime(userPendingOrder.getAddTime());
                userPositionVO.setNickName(userPendingOrder.getNickName());
                resultArr.add(userPositionVO);
            }
            return new PageInfo<>(resultArr);
        }

        return null;
    }

    /**
     * 持仓 合并
     *
     * @param stockSpell
     * @param state
     * @param pageNum
     * @param pageSize
     * @return
     */
    private PageInfo<UserPositionVO> getHoldingPositions(Integer userId, String stockCode, String stockSpell, Integer state, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        // Get all positions including sold ones for the user
        List<UserPosition> userPositions = this.findPositionByCode(userId, stockCode, stockSpell, state);
        // First, create a map of all positions by their ID
        List<UserPositionVO> userPositionVOS = new ArrayList<>();
        // Process each group
        for (UserPosition position : userPositions) {
            // Calculate totals
            BigDecimal totalBuyAmount = BigDecimal.ZERO;
            int totalQuantity = 0;
            long availableQuantity = 0;
            List<UserPosition> positionSnList = new ArrayList<>();
            BigDecimal totalOrderPrice = BigDecimal.ZERO;
            // Calculate total quantity and total buy amount
            totalBuyAmount = totalBuyAmount.add(position.getBuyOrderPrice().multiply(new BigDecimal(position.getOrderNum())));
            totalQuantity += position.getOrderNum();
            totalOrderPrice = totalOrderPrice.add(position.getOrderTotalPrice());
            SiteSetting siteSetting = this.iSiteSettingService.getSiteSetting();
            int cantSellTime = siteSetting.getCantSellTimes();
            if (cantSellTime == -1) {
                if (!DateTimeUtil.isSameDay(position.getBuyOrderTime(), DateTimeUtil.getCurrentDate())) {
                    availableQuantity += position.getOrderNum();
                    positionSnList.add(position);
                    log.debug("持仓{}可卖出(隔日规则)", position.getPositionSn());
                }
            } else {
                if (DateTimeUtil.isCanSell(position.getBuyOrderTime(), cantSellTime)) {
                    availableQuantity += position.getOrderNum();
                    positionSnList.add(position);
                    log.debug("持仓{}可卖出(分钟数规则)", position.getPositionSn());
                }
            }
            // Use the root position as template for the VO
            UserPositionVO userPositionVO = assembleUserPositionVO(position);
            if (totalQuantity == 0) {
                userPositionVO.setBuyOrderPrice(totalBuyAmount);
            } else {
                userPositionVO.setBuyOrderPrice(totalBuyAmount.divide(new BigDecimal(totalQuantity), 2, RoundingMode.HALF_UP));
            }
            // Get current stock price
            StockListVO stockListVO = SinaStockApi.assembleStockListVO(SinaStockApi.getSinaStock(position.getStockGid()));
            BigDecimal currentPrice = stockListVO.getNowPrice();
            // Calculate total sell amount at current price
            BigDecimal totalSellAmount = currentPrice.multiply(new BigDecimal(totalQuantity));
            // Calculate profit/loss
            BigDecimal profitLoss;
            if ("买涨".equals(position.getOrderDirection())) {
                profitLoss = totalSellAmount.subtract(totalBuyAmount);
            } else {
                profitLoss = totalBuyAmount.subtract(totalSellAmount);
            }
            log.info("盈亏计算 - 总买入金额={}, 总卖出金额={}, 盈亏={}", totalBuyAmount, totalSellAmount, profitLoss);
            // Calculate total fees
            BigDecimal totalFees = position.getOrderFee();
            BigDecimal totalProfit = profitLoss.subtract(totalFees);
            userPositionVO.setAvailablePositionSn(positionSnList);
            userPositionVO.setOrderNum(totalQuantity);
            userPositionVO.setOrderFee(totalOrderPrice.multiply(siteSetting.getBuyFee()).setScale(2, RoundingMode.HALF_UP));
            userPositionVO.setAvailableNum(availableQuantity);
            userPositionVO.setProfitAndLose(profitLoss);
            userPositionVO.setAllProfitAndLose(totalProfit);
            userPositionVO.setOrderTotalPrice(totalOrderPrice);
            userPositionVO.setIsLock(position.getIsLock());
            userPositionVOS.add(userPositionVO);
        }
        return PageUtil.buildPageDto(userPositions, userPositionVOS);
    }



    public PageInfo<UserPositionVO> findPCPosition(Integer userId, String stockCode, String
            stockSpell, Integer state, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<UserPosition> userPositions = this.findPositionByCode(userId, stockCode, stockSpell, state);
        SiteSetting siteSetting = this.iSiteSettingService.getSiteSetting();
        BigDecimal sellFee = siteSetting.getSellFee();
        BigDecimal buyFee = siteSetting.getBuyFee();
        List<UserPositionVO> userPositionVOS = Lists.newArrayList();
        if (!userPositions.isEmpty()) {
            for (UserPosition position : userPositions) {
                UserPositionVO userPositionVO = assembleUserPositionVO(position);
                BigDecimal orderFee = userPositionVO.getSellOrderPrice().multiply(new BigDecimal(userPositionVO.getOrderNum())).multiply(sellFee).setScale(2, RoundingMode.HALF_UP);
                userPositionVO.setOrderFee(orderFee);
                BigDecimal buyOrderFee = userPositionVO.getBuyOrderPrice().multiply(new BigDecimal(userPositionVO.getOrderNum())).multiply(buyFee).setScale(2, RoundingMode.HALF_UP);
                userPositionVO.setBuyOrderFee(buyOrderFee);
                userPositionVO.setIsLock(position.getIsLock());
                userPositionVOS.add(userPositionVO);
            }
        }
        PageInfo<UserPositionVO> pageInfo = new PageInfo<UserPositionVO>(userPositionVOS);
        pageInfo.setList(userPositionVOS);
        return pageInfo;
    }


    public List<UserPosition> findPositionByCode(Integer uid, String stockCode, String stockSpell, Integer state) {
        LambdaQueryWrapper<UserPosition> wrapper = new LambdaQueryWrapper<>();
        if (uid != null) {
            wrapper.eq(UserPosition::getUserId, uid);
        }
        if (state != null) {
            if (state == 0) {
                wrapper.isNull(UserPosition::getSellOrderId);
            } else if (state == 1) {
                wrapper.isNotNull(UserPosition::getSellOrderId);
            }
        }
        if (StringUtils.isNotBlank(stockCode)) {
            wrapper.like(UserPosition::getStockCode, stockCode);
        }
        if (StringUtils.isNotBlank(stockSpell)) {
            wrapper.like(UserPosition::getStockSpell, stockSpell);
        }
        wrapper.orderByDesc(UserPosition::getId);
        return this.list(wrapper); // this 继承自 ServiceImpl
    }

    private String findRootPositionId(UserPosition position, Map<String, UserPosition> positionsById) {
        // Build a chain map to track relationships: position -> its parent
        Map<String, String> chainMap = new HashMap<>();
        for (UserPosition pos : positionsById.values()) {
            if (pos.getPositionAppendId() != null) {
                chainMap.put(pos.getId().toString(), pos.getPositionAppendId().toString());
            }
        }

        // Find all positions in the same chain as current position
        Set<String> chainPositions = new HashSet<>();
        String currentId = position.getId().toString();
        chainPositions.add(currentId);

        // Add all parents to chain
        String parentId = chainMap.get(currentId);
        while (parentId != null) {
            chainPositions.add(parentId);
            parentId = chainMap.get(parentId);
        }

        // Add all siblings (positions with same parent)
        for (Map.Entry<String, String> entry : chainMap.entrySet()) {
            if (chainPositions.contains(entry.getValue())) {
                chainPositions.add(entry.getKey());
            }
        }

        // Find the earliest unsold position in the chain
        List<UserPosition> chainPositionsList = chainPositions.stream().map(positionsById::get).filter(Objects::nonNull).sorted((p1, p2) -> p1.getBuyOrderTime().compareTo(p2.getBuyOrderTime())).collect(Collectors.toList());

        // Find first unsold position
        for (UserPosition pos : chainPositionsList) {
            if (pos.getSellOrderId() == null) {
                return pos.getId().toString();
            }
        }

        // If all positions are sold, return the earliest position's ID
        return chainPositionsList.get(0).getId().toString();
    }


    //state, userId, positionSn, beginTime, endTime, request, pageNum, pageSize


    public int CountPositionNum(Integer state) {
        return this.userPositionMapper.CountPositionNum(state);
    }

    public BigDecimal CountPositionProfitAndLose() {
        return this.userPositionMapper.CountPositionProfitAndLose();
    }

    public BigDecimal CountPositionAllProfitAndLose() {
        return this.userPositionMapper.CountPositionAllProfitAndLose();
    }

    @Override
    public void create(Integer userId, String stockCode, BigDecimal now_price, Integer
            actualBuyNum, Integer positionId, String buyTime) {
        User user = this.userMapper.selectById(userId);
        if (user == null) {
            throw new CustomException("用户不存在");
        }

        SiteProduct siteProduct = iSiteProductService.getProductSetting();
        if (siteProduct.getRealNameDisplay() && user.getIsLock() == 1) {
            throw new CustomException("下单失败，账户已被锁定");
        }
        Stock stock = this.iStockService.findStockByCode(stockCode);
        if (stock == null) {
            throw new CustomException("股票不存在");
        }

        SiteSetting siteSetting = this.iSiteSettingService.getSiteSetting();
        if (siteSetting == null) {
            throw new CustomException("下单失败，系统设置错误");
        }
        UserPosition userPosition = new UserPosition();
        String orderNo = SnowIdUtil.getId(OrderConstant.userPositionOrder);
        BigDecimal buy_amt_autual = now_price.multiply(new BigDecimal(actualBuyNum));
        userPosition.setUserId(user.getId());
        userPosition.setPositionSn(orderNo);
        userPosition.setNickName(user.getNickName());
        userPosition.setStockCode(stock.getStockCode());
        userPosition.setStockName(stock.getStockName());
        userPosition.setStockGid(stock.getStockGid());
        userPosition.setStockSpell(stock.getStockSpell());
        userPosition.setBuyOrderId(GeneratePosition.getPositionId());
        userPosition.setBuyOrderTime(StrUtil.isEmpty(buyTime) ? new Date() : DateTimeUtil.strToDate(buyTime));
        userPosition.setBuyOrderPrice(now_price);
        userPosition.setOrderDirection("买涨");
        userPosition.setOrderNum(actualBuyNum);
        userPosition.setStockPlate(stock.getStockPlate());
        userPosition.setIsLock(0);
        userPosition.setPositionAppendId(positionId);
        userPosition.setOrderTotalPrice(buy_amt_autual);
        userPosition.setBuyOrderPrice(now_price);
        BigDecimal orderPrice = now_price.multiply(new BigDecimal(actualBuyNum));
        BigDecimal buy_fee_amt = orderPrice.multiply(siteSetting.getBuyFee()).setScale(2, 4);
        userPosition.setOrderFee(buy_fee_amt);
        BigDecimal profit_and_lose = new BigDecimal("0");
        userPosition.setProfitAndLose(profit_and_lose);
        BigDecimal all_profit_and_lose = profit_and_lose.subtract(buy_fee_amt);
        userPosition.setAllProfitAndLose(all_profit_and_lose);
        this.userPositionMapper.insert(userPosition);
    }


    public int deleteByUserId(Integer userId) {
        return this.userPositionMapper.deleteByUserId(userId);
    }


    private UserPositionVO assembleUserPositionVO(UserPosition position) {
        UserPositionVO userPositionVO = new UserPositionVO();

        userPositionVO.setId(position.getId());
        userPositionVO.setPositionSn(position.getPositionSn());
        userPositionVO.setUserId(position.getUserId());
        userPositionVO.setNickName(position.getNickName());
        userPositionVO.setStockName(position.getStockName());
        userPositionVO.setStockCode(position.getStockCode());
        userPositionVO.setStockGid(position.getStockGid());
        userPositionVO.setStockSpell(position.getStockSpell());
        userPositionVO.setBuyOrderId(position.getBuyOrderId());
        userPositionVO.setBuyOrderTime(position.getBuyOrderTime());
        userPositionVO.setBuyOrderPrice(position.getBuyOrderPrice());
        userPositionVO.setSellOrderId(position.getSellOrderId());
        userPositionVO.setSellOrderTime(position.getSellOrderTime());
        userPositionVO.setSellOrderPrice(position.getSellOrderPrice());
        userPositionVO.setProfitTargetPrice(position.getProfitTargetPrice());
        userPositionVO.setStopTargetPrice(position.getStopTargetPrice());
        userPositionVO.setOrderDirection(position.getOrderDirection());
        userPositionVO.setOrderNum(position.getOrderNum());
        userPositionVO.setOrderTotalPrice(position.getOrderTotalPrice());
        userPositionVO.setOrderFee(position.getOrderFee());
        userPositionVO.setStockPlate(position.getStockPlate());
        PositionProfitVO positionProfitVO = getPositionProfitVO(position);
        userPositionVO.setProfitAndLose(positionProfitVO.getProfitAndLose());
        userPositionVO.setAllProfitAndLose(positionProfitVO.getAllProfitAndLose());
        userPositionVO.setNow_price(positionProfitVO.getNowPrice());


        return userPositionVO;
    }

    public PositionProfitVO getPositionProfitVO(UserPosition position) {
        BigDecimal profitAndLose = new BigDecimal("0");
        BigDecimal allProfitAndLose = new BigDecimal("0");
        BigDecimal nowPrice = BigDecimal.ZERO;

        if (StrUtil.isNotEmpty(position.getSellOrderId())) {
            BigDecimal subPrice = position.getSellOrderPrice().subtract(position.getBuyOrderPrice());
            profitAndLose = subPrice.multiply(new BigDecimal(position.getOrderNum()));
            if ("买跌".equals(position.getOrderDirection())) {
                profitAndLose = profitAndLose.negate();
            }


            allProfitAndLose = profitAndLose.subtract(position.getOrderFee());
        } else {
            StockListVO stockListVO = new StockListVO();
            stockListVO = SinaStockApi.assembleStockListVO(SinaStockApi.getSinaStock(position.getStockGid()));
            nowPrice = stockListVO.getNowPrice();
            if (nowPrice == null) {
                nowPrice = BigDecimal.ZERO;
            }
            BigDecimal subPrice = (nowPrice).subtract(position.getBuyOrderPrice());
            profitAndLose = subPrice.multiply(new BigDecimal(position.getOrderNum()));
            if ("买跌".equals(position.getOrderDirection())) {
                profitAndLose = profitAndLose.negate();
            }

            //总盈亏= 浮动盈亏 – 手续费 – 印花税 – 留仓费 – 点差费
            allProfitAndLose = profitAndLose.subtract(position.getOrderFee());
        }
        PositionProfitVO positionProfitVO = new PositionProfitVO();
        positionProfitVO.setProfitAndLose(profitAndLose);
        positionProfitVO.setAllProfitAndLose(allProfitAndLose);
        positionProfitVO.setNowPrice(nowPrice);

        return positionProfitVO;
    }


    @Override
    public void revocation(Integer id) {

        User user = this.iUserService.getCurrentUser();
        if (user == null) {
            throw new CustomException("用户未登录");
        }
        redisLockUtil.tryLock(StrUtil.format(RedisLockKey.UserPendingOrderLock, id), 10, TimeUnit.SECONDS, () -> {
            UserPendingOrder userPendingOrder = this.userPendingOrderService.getById(id);
            String orderNo = userPendingOrder.getOrderNo();
            if (userPendingOrder.getUserId() != user.getId()) {
                throw new CustomException("该订单不是您的，无法撤销");
            }
            Integer status = userPendingOrder.getStatus();
            if (status == 0 || status == 2) {
                BigDecimal orderTotalPrice = new BigDecimal(userPendingOrder.getBuyNum()).multiply(userPendingOrder.getTargetPrice());
                //撤销然后给返回钱
                AmountConsumer amountConsumer = (oldUser, newUser) -> {
                    this.userPositionMapper.deleteById(id);
                };
                userAmountChangeManage.changeBalance(user.getId(), orderTotalPrice, orderNo, OrderTypeEnum.STOCK, TypeEnum.RETURN_PING_STOCK, "", "", amountConsumer);
            }
            return null;
        });
    }

    /**
     * @Description: 新股转持仓
     * @Param:
     * @return:
     * @Author: tf
     * @Date: 2022/10/26
     */
    @Override
    public void newStockToPosition(Integer id) {
        UserStockSubscribe userStockSubscribe = userStockSubscribeMapper.load(id);
        if (userStockSubscribe == null) {
            throw new CustomException("无该申购记录");
        }
        StockSubscribe stockSubscribe = stockSubscribeMapper.selectOne(new QueryWrapper<StockSubscribe>().eq("code", userStockSubscribe.getNewCode()));
        if (userStockSubscribe.getApplyNumber() == null) {
            throw new CustomException("中签数量为空，转持仓失败");
        }
        Stock stock = stockMapper.selectOne(new QueryWrapper<Stock>().eq("stock_code", userStockSubscribe.getNewCode()));
        if (stock == null) {
            throw new CustomException("该股不存在，每天8点30分更新，如果时间已过并且此新股确保上市，请手动添加");
        }

        if (userStockSubscribe.getStatus() == 4 || userStockSubscribe.getStatus() == 3) {

            String sinaStock = SinaStockApi.getSinaStock(stock.getStockGid());
            String[] arrayOfString = sinaStock.split(",");
//                 if (arrayOfString.length < 10){
//                 return ServerResponse.createByErrorMsg("数据源无该新股数据，转持仓失败");
//                 }
            String orderNo = SnowIdUtil.getId(OrderConstant.userPositionOrder);
            UserPosition userPosition = new UserPosition();
            userPosition.setPositionSn(orderNo);
            userPosition.setUserId(userStockSubscribe.getUserId());
            userPosition.setNickName(userStockSubscribe.getRealName());
            userPosition.setStockCode(userStockSubscribe.getNewCode());
            userPosition.setStockName(userStockSubscribe.getNewName());
            userPosition.setStockGid(stock.getStockGid());

            userPosition.setBuyOrderId(GeneratePosition.getPositionId());
            userPosition.setBuyOrderTime(new Date());
            userPosition.setBuyOrderPrice(userStockSubscribe.getBuyPrice());
            userPosition.setOrderDirection("买涨");
            userPosition.setOrderNum(userStockSubscribe.getApplyNumber());


            userPosition.setIsLock(0);
            //递延费特殊处理
            //            BigDecimal stayFee = userPosition.getOrderTotalPrice().multiply(siteSetting.getStayFee());
            BigDecimal stayFee = new BigDecimal(0);
            BigDecimal allStayFee = stayFee.multiply(new BigDecimal(1));
            userPosition.setOrderTotalPrice(userStockSubscribe.getBond());

            //            BigDecimal buy_fee_amt = buy_amt.multiply(siteSetting.getBuyFee()).setScale(2, 4);
            BigDecimal buy_fee_amt = new BigDecimal(0);
            log.info("用户购买手续费（配资后总资金 * 百分比） = {}", buy_fee_amt);
            userPosition.setOrderFee(buy_fee_amt);


            //            BigDecimal buy_yhs_amt = buy_amt.multiply(siteSetting.getDutyFee()).setScale(2, 4);
            BigDecimal buy_yhs_amt = new BigDecimal(0);
            log.info("用户购买印花税（配资后总资金 * 百分比） = {}", buy_yhs_amt);

            //            SiteSpread siteSpread = iSiteSpreadService.findSpreadRateOne(new BigDecimal(stock_crease), buy_amt, stock.getStockCode(), now_price);
            //            BigDecimal spread_rate_amt = new BigDecimal("0");
            //            if(siteSpread != null){
            //                spread_rate_amt = buy_amt.multiply(siteSpread.getSpreadRate()).setScale(2, 4);
            //                log.info("用户购买点差费（配资后总资金 * 百分比{}） = {}", siteSpread.getSpreadRate(), spread_rate_amt);
            //            } else{
            //                log.info("用户购买点差费（配资后总资金 * 百分比{}） = {}", "设置异常", spread_rate_amt);
            //            }
            BigDecimal spread_rate_amt = new BigDecimal(0);


            BigDecimal profit_and_lose = new BigDecimal("0");
            userPosition.setProfitAndLose(profit_and_lose);


            BigDecimal all_profit_and_lose = profit_and_lose.subtract(buy_fee_amt).subtract(buy_yhs_amt).subtract(spread_rate_amt);
            userPosition.setAllProfitAndLose(all_profit_and_lose);

            int ret = 0;
            ret = this.userPositionMapper.insert(userPosition);

            if (ret > 0) {
                userStockSubscribe.setStatus(5);
                userStockSubscribeMapper.updateById(userStockSubscribe);
                if (userStockSubscribe.getType() == 1 || userStockSubscribe.getType() == 2) {
                    User user = userMapper.selectById(userStockSubscribe.getUserId());
                    ret = userMapper.updateById(user);
                }
                if (ret > 0) {
                    return;
                } else {
                    throw new CustomException("新股转持仓失败");
                }
            } else {
                throw new CustomException("新股转持仓失败");
            }
        }
        throw new CustomException("新股转持仓失败");
    }


    /**
     * vip抢筹
     *
     * @param stockCode
     * @param buyNum
     * @param buyType
     * @param lever
     * @param profitTarget
     * @param stopTarget
     * @return
     */
    @Transactional(isolation = Isolation.SERIALIZABLE)
    @Override
    public void buyVipQc(String stockCode, Integer buyNum, Integer buyType, Integer lever, BigDecimal
            profitTarget, BigDecimal stopTarget) throws Exception {

        /*实名认证开关开启*/
        SiteProduct siteProduct = iSiteProductService.getProductSetting();
        User user = this.iUserService.getCurrentUser();

        if (siteProduct.getRealNameDisplay() && (StringUtils.isBlank(user.getRealName()) || StringUtils.isBlank(user.getIdCard()))) {
            throw new CustomException("下单失败，请先实名认证");
        }

        BigDecimal user_enable_amt = user.getEnableAmt();

        log.info("用户 {} 下单，股票id = {} ，数量 = {} , 方向 = {} , 杠杆 = {}", new Object[]{user.getId(), stockCode, buyNum, buyType, lever});
        if (siteProduct.getRealNameDisplay() && user.getIsLock() == 1) {
            throw new CustomException("下单失败，账户已被锁定");
        }


        SiteSetting siteSetting = this.iSiteSettingService.getSiteSetting();
        if (siteSetting == null) {
            throw new CustomException("下单失败，系统设置错误");
        }
        if (siteSetting.getVipQcMaxAmt().compareTo(user_enable_amt) > 0) {
            throw new CustomException("下单失败，可用余额小于" + siteSetting.getVipQcMaxAmt());
        }
        Stock stock = this.iStockService.findStockByCode(stockCode);
        String am_begin = siteSetting.getTransAmBegin();
        String am_end = siteSetting.getTransAmEnd();
        String pm_begin = siteSetting.getTransPmBegin();
        String pm_end = siteSetting.getTransPmEnd();
        boolean am_flag = BuyAndSellUtils.isTransTime(am_begin, am_end);
        boolean pm_flag = BuyAndSellUtils.isTransTime(pm_begin, pm_end);
        log.info("是否在上午交易时间 = {} 是否在下午交易时间 = {}", am_flag, pm_flag);

        if (!am_flag && !pm_flag) {
            throw new CustomException("下单失败，不在交易时段内");
        }
        if (siteProduct.getHolidayDisplay()) {
            throw new CustomException("周末或节假日不能交易！");
        }
        if (stock.getIsLock() != 0) {
            throw new CustomException("下单失败，当前股票不能交易");
        }

        StockListVO stockListVO = new StockListVO();
        BigDecimal now_price = stockListVO.getNowPrice();
        if (now_price.compareTo(new BigDecimal("0")) == 0) {
            throw new CustomException("报价0，请稍后再试");
        }

        if (stockListVO.getName().startsWith("ST") || stockListVO.getName().endsWith("退")) {
            throw new CustomException("ST和已退市的股票不能入仓");
        }

        BigDecimal zsPrice = new BigDecimal(stockListVO.getPreclose_px());
        double stock_crease = stockListVO.getHcrate().doubleValue();

        if ("主板".equals(stock.getStockPlate())) {
            int maxcrease = siteSetting.getCreaseMaxPercent().intValue();
            if (stock_crease > 0.0D && stock_crease >= maxcrease) {
                throw new CustomException("下单失败，股票当前涨幅:" + stock_crease + ",大于最大涨幅:" + maxcrease);
            }

            if (stock_crease < 0.0D && -stock_crease > maxcrease) {
                throw new CustomException("下单失败，股票当前跌幅:" + stock_crease + ",大于最大跌幅:" + maxcrease);
            }
        } else if ("创业".equals(stock.getStockPlate())) {
            int maxcrease = siteSetting.getCyCreaseMaxPercent().intValue();
            if (stock_crease > 0.0D && stock_crease >= maxcrease) {
                throw new CustomException("下单失败，创业股当前涨幅:" + stock_crease + ",大于最大涨幅:" + maxcrease);
            }

            if (stock_crease < 0.0D && -stock_crease > maxcrease) {
                throw new CustomException("下单失败，创业股当前跌幅:" + stock_crease + ",大于最大跌幅:" + maxcrease);
            }
        } else {
            int maxcrease = siteSetting.getKcCreaseMaxPercent().intValue();
            if (stock_crease > 0.0D && stock_crease >= maxcrease) {
                throw new CustomException("下单失败，科创股当前涨幅:" + stock_crease + ",大于最大涨幅:" + maxcrease);
            }

            if (stock_crease < 0.0D && -stock_crease > maxcrease) {
                throw new CustomException("下单失败，科创股当前跌幅:" + stock_crease + ",大于最大跌幅:" + maxcrease);
            }
        }



        BigDecimal buy_amt_autual = now_price.multiply(new BigDecimal(buyNum));
        int compareUserAmtInt = user_enable_amt.compareTo(buy_amt_autual);
        if (compareUserAmtInt == -1) {
            throw new CustomException("下单失败，可用金额小于" + buy_amt_autual + "元");
        }
        UserPosition userPosition = new UserPosition();
        if (profitTarget != null && profitTarget.compareTo(new BigDecimal("0")) > 0) {
            userPosition.setProfitTargetPrice(profitTarget);
        }
        if (stopTarget != null && stopTarget.compareTo(new BigDecimal("0")) > 0) {
            userPosition.setStopTargetPrice(stopTarget);
        }
        String orderNo = SnowIdUtil.getId(OrderConstant.userPositionOrder);
        userPosition.setPositionSn(orderNo);
        userPosition.setUserId(user.getId());
        userPosition.setNickName(user.getRealName());
        userPosition.setStockCode(stock.getStockCode());
        userPosition.setStockName(stock.getStockName());
        userPosition.setStockGid(stock.getStockGid());
        userPosition.setStockSpell(stock.getStockSpell());
        userPosition.setBuyOrderId(GeneratePosition.getPositionId());
        userPosition.setBuyOrderTime(new Date());
        userPosition.setBuyOrderPrice(now_price);
        userPosition.setOrderDirection((buyType == 0) ? "买涨" : "买跌");
        userPosition.setOrderNum(buyNum);
        userPosition.setStockPlate(stock.getStockPlate());
        userPosition.setIsLock(0);
        userPosition.setOrderTotalPrice(buy_amt_autual);
        BigDecimal buy_fee_amt = buy_amt_autual.multiply(siteSetting.getBuyFee()).setScale(2, 4);
        userPosition.setOrderFee(buy_fee_amt);
        BigDecimal profit_and_lose = new BigDecimal("0");
        userPosition.setProfitAndLose(profit_and_lose);
        BigDecimal all_profit_and_lose = profit_and_lose.subtract(buy_fee_amt);
        userPosition.setAllProfitAndLose(all_profit_and_lose);
        int insertPositionCount = 0;
        this.userPositionMapper.insert(userPosition);
        insertPositionCount = userPosition.getId();
        if (insertPositionCount > 0) {
            //修改用户可用余额= 当前余额-下单金额-买入手续费-印花税-点差费
            //修改用户可用余额= 当前余额-下单总金额
            BigDecimal reckon_enable = user_enable_amt.subtract(buy_amt_autual);
            user.setEnableAmt(reckon_enable);
            int updateUserCount = this.userMapper.updateById(user);
            if (updateUserCount > 0) {
                log.info("【用户交易下单】修改用户金额成功");
            } else {
                log.error("用户交易下单】修改用户金额出错");
                throw new CustomException("用户交易下单】修改用户金额出错");
            }
            //核算代理收入-入仓手续费
            log.info("【用户交易下单】保存持仓记录成功");
        } else {
            log.error("用户交易下单】保存持仓记录出错");
            throw new CustomException("用户交易下单】保存持仓记录出错");
        }


        SiteMessage siteMessage = new SiteMessage();
        siteMessage.setUserId(user.getId());
        siteMessage.setMessageType(SiteMessage.MSG_TYPE_ADMIN);
        siteMessage.setStatus(1);
        siteMessage.setUserName(user.getRealName());
        siteMessage.setAddTime(new Date());
        siteMessage.setTypeName("VIP抢筹下单");
        siteMessage.setContent("用户【" + user.getRealName() + "】发起VIP抢筹下单");
        this.siteMessageMapper.insert(siteMessage);


        return;
    }


}