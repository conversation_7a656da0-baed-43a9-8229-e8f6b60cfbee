package com.nq.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nq.dao.RealTimeMapper;
import com.nq.pojo.RealTime;
import com.nq.service.RealTimeService;
import com.nq.excepton.CustomException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class RealTimeServiceImpl extends ServiceImpl<RealTimeMapper, RealTime> implements RealTimeService {

    @Resource
    private RealTimeMapper realTimeMapper;
    @Override
    public void deleteRealTime() {
        int result = this.realTimeMapper.deleteStockCode();
        if (result <= 0) {
            throw new CustomException("删除失败");
        }
    }
    @Override
    public void deleteFuturesRealTime() {
        int result = this.realTimeMapper.deleteStockFuturesCode();
        if (result <= 0) {
            throw new CustomException("删除失败");
        }
    }
    @Override
    public Map<String, Object> findStock(String stockCode) {
        List<RealTime> stock;
        if (stockCode.startsWith("6")) {
            stock = this.realTimeMapper.findStock("sh" + stockCode);
        } else if (stockCode.startsWith("3") || stockCode.startsWith("0") || stockCode.startsWith("2")) {
            stock = this.realTimeMapper.findStock("sz" + stockCode);
        } else if (stockCode.startsWith("4") || stockCode.startsWith("8")) {
            stock = this.realTimeMapper.findStock("bj" + stockCode);
        } else {
            stock = this.realTimeMapper.findStock(stockCode);
        }

        if (stock == null || stock.isEmpty()) {
            throw new CustomException("未找到相关股票数据");
        }

        Map<String, Object> map = new HashMap<>();
        List<Double> price = new ArrayList<>();
        List<Double> averagePrice = new ArrayList<>();
        List<Double> rates = new ArrayList<>();
        List<String> time = new ArrayList<>();
        List<Integer> volumes = new ArrayList<>();
        List<Integer> amounts = new ArrayList<>();
        map.put("stockCode", stockCode);
        map.put("size", stock.size());
        for (RealTime realTime : stock) {
            price.add(realTime.getPrice());
            // averagePrice.add(realTime.getAveragePrice()); // 如需平均价请取消注释
            rates.add(realTime.getRates());
            time.add(realTime.getTime());
            volumes.add(realTime.getVolumes());
            amounts.add(realTime.getAmounts());
        }
        map.put("time", time);
        map.put("volumes", volumes);
        map.put("price", price);
        map.put("averagePrice", averagePrice);
        map.put("rates", rates);
        map.put("amounts", amounts);
        return map;
    }
}
