package com.nq.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.nq.dao.MerchantPayMapper;
import com.nq.dto.common.CommonPage;
import com.nq.pojo.MerchantPay;
import com.nq.service.MerchantPayService;
import com.nq.utils.PageUtil;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MerchantPayServiceImpl extends ServiceImpl<MerchantPayMapper, MerchantPay> implements MerchantPayService{

    @Override
    public PageInfo<MerchantPay> listByAdmin(MerchantPay merchantPay, CommonPage commonPage) {
        Integer pageNum = commonPage.getPageNum();
        Integer pageSize = commonPage.getPageSize();
        PageUtil.startPage(pageNum, pageSize);
        LambdaQueryWrapper<MerchantPay> q = new LambdaQueryWrapper<>();
        this.buildQuery(q, merchantPay);
        List<MerchantPay> merchantPays = this.list(q);
        return new PageInfo<>(merchantPays);

    }
    private void buildQuery(LambdaQueryWrapper<MerchantPay> q, MerchantPay merchantPay) {
        // 添加名称查询条件
        q.like(StrUtil.isNotEmpty(merchantPay.getName()), MerchantPay::getName, merchantPay.getName());

        // 添加排序条件
        q.orderByAsc(MerchantPay::getSort);
    }

}
