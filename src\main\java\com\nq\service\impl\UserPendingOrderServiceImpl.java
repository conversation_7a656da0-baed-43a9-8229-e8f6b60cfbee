package com.nq.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.dao.StockMapper;
import com.nq.dao.UserMapper;
import com.nq.dao.UserPendingOrderMapper;
import com.nq.pojo.Stock;
import com.nq.pojo.User;
import com.nq.pojo.UserPendingOrder;
import com.nq.service.IUserPositionService;
import com.nq.service.IUserService;
import com.nq.service.UserPendingOrderService;
import com.nq.utils.RedisLockKey;
import com.nq.utils.RedisLockUtil;
import com.nq.utils.stock.sina.SinaStockApi;
import com.nq.vo.stock.StockListVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class UserPendingOrderServiceImpl extends ServiceImpl<UserPendingOrderMapper, UserPendingOrder>
        implements UserPendingOrderService {
    @Resource
    private UserPendingOrderMapper userPendingOrderMapper;
    @Resource
    private StockMapper stockMapper;
    @Resource
    private UserMapper userMapper;
    @Resource
    private RedisLockUtil redisLockUtil;
    @Resource
    private IUserPositionService iUserPositionService;
    @Resource
    private IUserService iUserService;


    @Override
    public void orderTask() {
        List<UserPendingOrder> UserPendingOrders = userPendingOrderMapper.selectList(new QueryWrapper<UserPendingOrder>().eq("status", 0));
        log.info("当前有挂单的用户数量 为 {}", UserPendingOrders.size());

        for (UserPendingOrder pendingOrder : UserPendingOrders) {
            Integer userId = pendingOrder.getUserId();
            User user = this.userMapper.selectById(userId);
            if (user == null) {
                continue;
            }
            List<UserPendingOrder> UserPendingOrderList = userPendingOrderMapper.selectList(new QueryWrapper<UserPendingOrder>().eq("user_id", userId).eq("status", 0));
            if (UserPendingOrderList == null || UserPendingOrderList.isEmpty()) {
                continue;
            }

            log.info("用户id = {} 姓名 = {} 已挂单数： {}", userId, user.getRealName(), UserPendingOrders.size());
            BigDecimal nowPrice;
            String code;
            StockListVO stockListVO;
            for (UserPendingOrder UserPendingOrder : UserPendingOrderList) {
                Stock stock = stockMapper.findStockByCode(UserPendingOrder.getStockCode());
                stockListVO = SinaStockApi.assembleStockListVO(SinaStockApi.getSinaStock(stock.getStockGid()));
                nowPrice = stockListVO.getNowPrice();
                if (nowPrice == null) {
                    nowPrice = BigDecimal.ZERO;
                }
                code = stock.getStockCode();
                Integer id = UserPendingOrder.getId();
                BigDecimal finalNowPrice = nowPrice;
                String finalCode = code;
                if (isValidOrder(UserPendingOrder)) {
                    if (UserPendingOrder.getBuyType() == 0&&finalNowPrice.compareTo(BigDecimal.ZERO)>0) {
                        if (UserPendingOrder.getTargetPrice().compareTo(finalNowPrice) >= 0) {
                            redisLockUtil.tryLock(StrUtil.format(RedisLockKey.UserPendingOrderLock, id), 10, TimeUnit.SECONDS, () -> {
                                try {
                                    this.iUserPositionService.create(UserPendingOrder.getUserId(), finalCode, UserPendingOrder.getTargetPrice(),
                                            UserPendingOrder.getBuyNum(), UserPendingOrder.getPositionId(), null);
                                    UserPendingOrder.setStatus(1);
                                    this.userPendingOrderMapper.updateById(UserPendingOrder);
                                } catch (Exception e) {
                                    log.error("股票挂单任务失败...", e);
                                    UserPendingOrder.setStatus(2);
                                    this.userPendingOrderMapper.updateById(UserPendingOrder);
                                }
                                return null;
                            });

                        }
                    }
                }
            }
        }
    }

    @Override
    public PageInfo<UserPendingOrder> findMyPendingOrder(Integer userId, String stockCode, int pageNum, int pageSize, Boolean isAgent) {
        PageHelper.startPage(pageNum, pageSize);
        LambdaQueryWrapper<UserPendingOrder> wrapper = new LambdaQueryWrapper<>();
        if (stockCode != null && !stockCode.isEmpty()) {
            wrapper.eq(UserPendingOrder::getStockCode, stockCode);
        }
        wrapper.eq(userId!=null,UserPendingOrder::getUserId, userId);
        wrapper.in(UserPendingOrder::getStatus, 0, 2);
        if (isAgent){
            wrapper.in(UserPendingOrder::getUserId, iUserService.getUserIdByAgent());
        }
        List<UserPendingOrder> UserPendingOrders = userPendingOrderMapper.selectList(wrapper);
        return new PageInfo<>(UserPendingOrders);
    }

    private boolean isValidOrder(UserPendingOrder order) {
        return order.getUserId() != null && order.getStockCode() != null &&
                order.getBuyNum() != null && order.getBuyType() != null;
    }


}


