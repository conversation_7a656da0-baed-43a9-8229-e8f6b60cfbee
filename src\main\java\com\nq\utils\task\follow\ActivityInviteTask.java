package com.nq.utils.task.follow;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nq.constant.OrderConstant;
import com.nq.enums.OrderTypeEnum;
import com.nq.enums.TypeEnum;
import com.nq.function.AmountConsumer;
import com.nq.job.task.ITask;
import com.nq.manage.UserAmountChangeManage;
import com.nq.pojo.Activity;
import com.nq.pojo.ActivityReceive;
import com.nq.pojo.User;
import com.nq.service.ActivityReceiveService;
import com.nq.service.ActivityService;
import com.nq.service.impl.UserServiceImpl;
import com.nq.utils.SnowIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component("activityInviteTask")
public class ActivityInviteTask implements ITask {

    //手写 嫌弃刷新下用户信息
    @Resource
    private ActivityService activityService;
    @Resource
    private UserServiceImpl userService;
    @Resource
    private ActivityReceiveService activityReceiveService;
    @Resource
    private UserAmountChangeManage userAmountChangeManage;


    //    @Scheduled(cron = "0 0 12 * * ?")
    @Override
    public void run(String params) {
        log.info("开始执行日常邀请任务"); // 日志记录
        //查询昨日新注册的用户, 然后批量处理,查出上级邀请了多少多少人, 然后遍历上级,给每个上级分发奖励

        //查询日常邀请任务
        List<Activity> activities = activityService.list(new LambdaQueryWrapper<Activity>().eq(Activity::getType, 2).eq(Activity::getStatus, 1));
        List<Activity> sortedLists = activities.stream()
                .sorted(Comparator.comparing(Activity::getTarget).reversed()) // 倒序
                .collect(Collectors.toList()); // 转回 List
        //获取最小的
        DateTime yesterday = DateUtil.yesterday();
        DateTime beginTime = DateUtil.beginOfDay(yesterday);
        DateTime endTime = DateUtil.endOfDay(yesterday);
        List<User> users = userService.list(new LambdaQueryWrapper<User>().ge(User::getFirstFollowStopTime, beginTime)
                .le(User::getFirstFollowStopTime, endTime));

        //获取新注册用户的上级,并且分组,
        Map<Integer, Long> countMap = users.stream()
                .collect(Collectors.groupingBy(User::getPid, Collectors.counting()));


        for (Map.Entry<Integer, Long> entry : countMap.entrySet()) {
            Integer key = entry.getKey();
            Long value = entry.getValue();
            User user = userService.getById(key);
            //查询邀请人数对应的活动
            Activity activity = getActivity(sortedLists, value);
            try {
                if (activity != null) {
                    //添加活动领取记录,修改用户最大等级
                    ActivityReceive activityReceive = new ActivityReceive();
                    String orderNo = SnowIdUtil.getId(OrderConstant.ActivityReceiveCode);
                    activityReceive.setOrderSn(orderNo);
                    activityReceive.setUserId(user.getId());
                    activityReceive.setNickName(user.getNickName());
                    activityReceive.setActivityName(activity.getName());
                    activityReceive.setActivityId(activity.getActivityId());
                    activityReceive.setAmount(activity.getAward());

                    //修改充值订单
                    AmountConsumer amountConsumer = (oldUser, newUser) -> {
                        activityReceiveService.save(activityReceive);
                        userService.lambdaUpdate().set(User::getMaxLevel, user.getLevel()).eq(User::getId, user.getId()).update();
                    };

                    userAmountChangeManage.changeBalance(user.getId(), activity.getAward(), orderNo, OrderTypeEnum.BONUS, TypeEnum.BONUS, "", "", amountConsumer);
                } else {
                    log.warn("日常邀请任务-邀请人数:{} 活动不存在", value);
                }

            } catch (Exception e) {
                log.error("日常邀请任务-用户:{}, 邀请奖励发放失败", user.getId());
            }


        }
    }

    private Activity getActivity(List<Activity> sortedLists, Long value) {
        Activity activity1 = null;
        for (Activity activity : sortedLists) {
            if (value >= activity.getTarget()) {
                activity1 = activity;
                break;
            }
        }
        return activity1;
    }
}

