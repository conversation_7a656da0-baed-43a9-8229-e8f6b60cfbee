package com.nq.controller.app;

import com.alibaba.fastjson2.JSONArray;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.service.IStockService;
import com.nq.vo.stock.MarketVOResult;
import com.nq.vo.stock.StockDzVo;
import com.nq.vo.stock.StockListVO;
import com.nq.vo.stock.k.echarts.EchartsDataVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping({"/api/stock/"})
@Api(tags = "APP-股票接口")
public class AppStockApiController {

    @Resource
    private IStockService iStockService;


    //查询 股票指数、大盘指数信息
    @ApiOperation("查询大盘指数信息")
    @GetMapping({"getMarket.do"})
    public ServerResponse<MarketVOResult> getMarket() {
        MarketVOResult marketVOResult = iStockService.getMarket();
        return ServerResponse.createBySuccess(marketVOResult);
    }

    //查询官网PC端交易 所有股票信息及指定股票信息
    @ApiOperation("指定股票信息")
    @GetMapping({"getStock.do"})
    public ServerResponse<PageInfo<StockListVO>> getStock(@RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "10") int pageSize, @RequestParam(value = "stockPlate", required = false) String stockPlate, @RequestParam(value = "stockType", required = false) String stockType, @RequestParam(value = "keyWords", required = false) String keyWords) {
        PageInfo<StockListVO> stock = iStockService.getStock(pageNum, pageSize, keyWords, stockPlate, stockType);
        return ServerResponse.createBySuccess(stock);
    }

    //通过股票代码查询股票信息
    @ApiOperation("查询单个股票信息")
    @PostMapping({"getSingleStock.do"})
    public ServerResponse<Map> getSingleStock(@RequestParam("code") String code) {
        Map map = iStockService.getSingleStock(code);
        return ServerResponse.createBySuccess(map);
    }


    /*查询股票日线*/
    @ApiOperation("查询单个股票日线")
    @PostMapping({"getDayK.do"})
    public ServerResponse<EchartsDataVO> getDayK(@RequestParam("code") String code) {
        EchartsDataVO vo = iStockService.getDayK_Echarts(code);
        return ServerResponse.createBySuccess(vo);
    }


    /**
     * 龙虎榜
     */
    @ApiOperation("龙虎榜")
    @GetMapping({"getlhb.do"})
    public ServerResponse<JSONArray> getlhb() {
        JSONArray lhb = iStockService.lhb();
        return ServerResponse.createBySuccess(lhb);
    }

    /**
     * 十大成交股
     */
    @ApiOperation("十大成交股")
    @GetMapping({"getTop.do"})
    public ServerResponse<JSONArray> getTop(@RequestParam(value = "content") Integer content) {
        JSONArray top = iStockService.top(content);
        return ServerResponse.createBySuccess(top);
    }

    /**
     * 每日停牌
     */
    @ApiOperation("每日停牌")
    @GetMapping({"getStop.do"})
    public ServerResponse<JSONArray> getStop() {
        JSONArray stop = iStockService.stop();
        return ServerResponse.createBySuccess(stop);
    }


    /**
     * 获取股票根据 涨跌幅 成交额 价格排序
     *
     * @return sort  ("根据什么排序   changepercent 涨跌幅  pricechange 涨跌额  volume 成交量 amount 成交额")
     * asc ("是否升序 0否 1是")
     * node ("排序的主板类型 hs_bjs 北交所  cyb 创业板  kcb 科创板   hs_a 沪深a股")
     */
    @ApiOperation("获取股票根据 涨跌幅 成交额 价格排序")
    @PostMapping({"getStockSort.do"})
    public ServerResponse<JSONArray> getStockMarketZDFB(Integer pageNo, Integer pageSize, String sort, Integer asc, String node) {
        JSONArray stockSort = iStockService.getStockSort(pageNo, pageSize, sort, asc, node);
        return ServerResponse.createBySuccess(stockSort);
    }

    /**
     * 涨停板
     */
    @ApiOperation("涨停板")
    @PostMapping({"getztb.do"})
    public ServerResponse<JSONArray> getztb() {
        JSONArray ztb = iStockService.ztb();
        return ServerResponse.createBySuccess(ztb);
    }


    /**
     * vip 抢筹列表
     */
    @ApiOperation("vip 抢筹列表")
    @PostMapping({"getVipList.do"})
    public ServerResponse<JSONArray> getVipList() {
        JSONArray ztb = iStockService.ztb();
        return ServerResponse.createBySuccess(ztb);
    }

    /**
     * vip 抢筹根据股票代码查询
     *
     * @param code
     * @return
     */
    @ApiOperation("vip 抢筹根据股票代码查询")
    @PostMapping({"getVipByCode.do"})
    public ServerResponse<JSONArray> getVipByCode(String code) {
        JSONArray vipByCode = iStockService.getVipByCode(code);
        return ServerResponse.createBySuccess(vipByCode);
    }


}