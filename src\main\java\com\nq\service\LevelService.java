package com.nq.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.pojo.Level;
import com.nq.pojo.User;
import com.nq.vo.admin.LevelVO;
import com.nq.vo.app.AppDayPackageVO;
import com.nq.vo.app.AppMorePackageVO;
import com.nq.vo.app.AppPackageVO;
import com.nq.vo.app.FollowLevelVO;

import java.util.List;

public interface LevelService extends IService<Level> {

    /**
     * 分页查询等级列表
     * @param page 页码
     * @param size 每页大小
     * @param levelName 等级名称
     * @param sortField 排序字段
     * @return 分页结果
     */
    PageInfo<LevelVO> pageList(Integer page, Integer size, String levelName, String sortField);

    /**
     * 获取等级详情
     */
    LevelVO getDetailById(Integer id);
    /**
     * 跟单等级
     */
    List<FollowLevelVO> queryTradeZD();
    /**
     * 查询单日跟单
     */
    AppDayPackageVO queryDayPackage(User user,Integer mentorId);
    /**
     *  查询单日跟单其他详情
     */
    List<AppDayPackageVO> queryDayPackageInfo(Integer mentorId);

}