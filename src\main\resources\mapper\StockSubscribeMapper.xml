<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nq.dao.StockSubscribeMapper">

    <resultMap id="BaseResultMap" type="com.nq.pojo.StockSubscribe">
            <id property="newlistId" column="newlist_id" jdbcType="INTEGER"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="code" column="code" jdbcType="VARCHAR"/>
            <result property="price" column="price" jdbcType="VARCHAR"/>
            <result property="pe" column="pe" jdbcType="VARCHAR"/>
            <result property="orderNumber" column="order_number" jdbcType="BIGINT"/>
            <result property="zt" column="zt" jdbcType="TINYINT"/>
            <result property="isLock" column="isLock" jdbcType="TINYINT"/>
            <result property="subscribeTime" column="subscribe_time" jdbcType="TIMESTAMP"/>
            <result property="subscriptionTime" column="subscription_time" jdbcType="TIMESTAMP"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        newlist_id,name,code,
        price,pe,order_number,zt,is_lock,
        subscribe_time,subscription_time,type
    </sql>

</mapper>
