package com.nq.service.impl;

import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nq.dao.SiteSmsLogMapper;
import com.nq.excepton.CustomException;
import com.nq.pojo.SiteSmsLog;
import com.nq.service.ISmsService;
import com.nq.utils.DateTimeUtil;
import com.nq.utils.redis.RedisShardedPoolUtils;
import com.nq.utils.sms.ali.AliyunSms;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

@Service("iSmsService")
public class SmsServiceImpl extends ServiceImpl<SiteSmsLogMapper, SiteSmsLog> implements ISmsService {
    private static final Logger log = LoggerFactory.getLogger(SmsServiceImpl.class);

    @Resource
    private SiteSmsLogMapper siteSmsLogMapper;

    public void sendAliyunSMS(String phoneNum, String ali_template) {
        if (StringUtils.isBlank(phoneNum)) {
            throw new CustomException("发送失败，手机号不能为空");
        }

        String yzmCode = RandomStringUtils.randomNumeric(4);
        log.info("验证码：{}", yzmCode);

        SendSmsResponse response = null;
        try {
            response = AliyunSms.sendSms(phoneNum, "鸿鹄", ali_template, yzmCode);
        } catch (Exception e) {
            log.error("发送短信异常：{}", e);
            throw new CustomException("短信发送异常");
        }

        log.debug("短信接口返回数据: Code={}, Message={}, RequestId={}, BizId={}", 
            response.getCode(), response.getMessage(), response.getRequestId(), response.getBizId());

        if (response.getCode() != null && response.getCode().equals("OK")) {
            String keys = "AliyunSmsCode:" + phoneNum;
            RedisShardedPoolUtils.setEx(keys, yzmCode, 5400);
            
            SiteSmsLog siteSmsLog = new SiteSmsLog();
            siteSmsLog.setSmsPhone(phoneNum);
            siteSmsLog.setSmsTitle("注册验证码");
            siteSmsLog.setSmsCnt(yzmCode);
            siteSmsLog.setSmsTemplate(ali_template);
            siteSmsLog.setSmsStatus(0);
            siteSmsLog.setAddTime(DateTimeUtil.getCurrentDate());
            this.siteSmsLogMapper.insert(siteSmsLog);
            return;
        }
        throw new CustomException("短信发送失败，请重试");
    }
}
