<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nq.dao.ScheduleJobLogDao">

	<!-- 根据任务id查询日志 -->
	<select id="getLogsByJobId" resultType="com.nq.job.entity.ScheduleJobLogEntity">
		select * from schedule_job_log where job_id = #{jobId} order by create_time desc
	</select>

	<!-- 批量删除 -->
	<delete id="deleteBatch">
		delete from schedule_job_log where log_id in
		<foreach item="logId" collection="array" open="(" separator="," close=")">
			#{logId}
		</foreach>
	</delete>

	<!-- 根据任务id删除日志 -->
	<delete id="deleteByJobId">
		delete from schedule_job_log where job_id = #{jobId}
	</delete>

</mapper>
