
spring:
  # redis 配置
  redis:
    # 地址
    host: master.im-test.uq7o35.apse1.cache.amazonaws.com
    # 端口，默认为6379
    port: 22647
    # 数据库索引
    database: 0
    # 密码
    password: Nt#2?bE1i!8pjv^Mh*4m
    # 连接超时时间
    timeout: 10s
    ssl: true
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 99
        # 连接池的最大数据库连接数
        max-active: 99
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
#    url: jdbc:mysql://*************:3306/stock?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
#    username: stock
#    password: 3veqrrZ4r2hDmvkt
    url: ******************************************************************************************************************************************************************************************
    username: baichuan
    password: AdFFk=CPy-TX#G8Lm78
    druid:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: Greysparrow
        login-password: 123456
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: false

minio:
  endpoint: http://54.255.59.180:9000
  domain: https://im-minio.imtest88.com
  accessKey: minioadmin
  secretKey: minioadmin
  bucketName: box-im
