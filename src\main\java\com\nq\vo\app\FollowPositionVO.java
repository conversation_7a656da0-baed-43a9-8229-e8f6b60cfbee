package com.nq.vo.app;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(description = "投顾标的持仓信息VO")
public class FollowPositionVO {
    @ApiModelProperty(value = "股票名称", example = "塞力医疗")
    private String stockName;

    @ApiModelProperty(value = "股票代码", example = "603716")
    private String stockCode;

    @ApiModelProperty(value = "买入价格", example = "9.63")
    private BigDecimal buyPrice;

    @ApiModelProperty(value = "买入数量", example = "41")
    private Integer buyQuantity;

    @ApiModelProperty(value = "买入时间", example = "2025-04-29 11:16:12")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date buyTime;

    @ApiModelProperty(value = "卖出价格", example = "0.00")
    private BigDecimal sellPrice;

    @ApiModelProperty(value = "盈亏", example = "0.00")
    private BigDecimal profit;

    @ApiModelProperty(value = "收益比", example = "0")
    private BigDecimal profitRate;
    @ApiModelProperty(value = "卖出时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sellTime;
    @ApiModelProperty(value = "净盈亏", example = "0.00")
    private BigDecimal netProfit;
    @ApiModelProperty(value = "工资", example = "0.00")
    private BigDecimal salary;
} 