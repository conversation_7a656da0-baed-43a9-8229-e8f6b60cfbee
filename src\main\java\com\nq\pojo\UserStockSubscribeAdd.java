package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户增发预售
 */
@ApiModel(description = "用户增发预售")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "user_stock_subscribe_add")
public class UserStockSubscribeAdd {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id")
    private Integer id;

    /**
     * 订单id
     */
    @TableField(value = "order_no")
    @ApiModelProperty(value = "订单id")
    private String orderNo;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    @ApiModelProperty(value = "用户id")
    private Integer userId;

    /**
     * 用户真实姓名
     */
    @TableField(value = "real_name")
    @ApiModelProperty(value = "用户真实姓名")
    private String realName;

    /**
     * 用户手机号
     */
    @TableField(value = "phone")
    @ApiModelProperty(value = "用户手机号")
    private String phone;

    /**
     * 股票代码
     */
    @TableField(value = "code")
    @ApiModelProperty(value = "股票代码")
    private String code;

    /**
     * 股票名称
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value = "股票名称")
    private String name;

    /**
     * 类型
     */
    @TableField(value = "stock_type")
    @ApiModelProperty(value = "类型")
    private String stockType;

    /**
     * 发行价格
     */
    @TableField(value = "price")
    @ApiModelProperty(value = "发行价格")
    private BigDecimal price;

    /**
     * 购买数量
     */
    @TableField(value = "buy_nums")
    @ApiModelProperty(value = "购买数量")
    private Integer buyNums;

    /**
     * 状态：1、已购买 2 可交易
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value = "状态：1、已购买 2 可交易")
    private Integer status;

    /**
     * 购买时间
     */
    @TableField(value = "buy_time")
    @ApiModelProperty(value = "购买时间")
    private Date buyTime;

    /**
     * 上市时间
     */
    @TableField(value = "list_time")
    @ApiModelProperty(value = "上市时间")
    private Date listTime;

    /**
     * 备注
     */
    @TableField(value = "remarks")
    @ApiModelProperty(value = "备注")
    private String remarks;
}