package com.nq.dao;

import com.nq.pojo.ConvertBondApply;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【convert_bond_apply(用户转债申请表)】的数据库操作Mapper
* @createDate 2022-12-05 16:33:44
* @Entity com.nq.pojo.ConvertBondApply
*/
@Mapper
public interface ConvertBondApplyMapper extends BaseMapper<ConvertBondApply> {

}




