<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nq.dao.UserPositionMapper">
    <resultMap id="BaseResultMap" type="com.nq.pojo.UserPosition">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="position_sn" property="positionSn" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="nick_name" property="nickName" jdbcType="VARCHAR"/>
        <result column="stock_name" property="stockName" jdbcType="VARCHAR"/>
        <result column="stock_code" property="stockCode" jdbcType="VARCHAR"/>
        <result column="stock_gid" property="stockGid" jdbcType="VARCHAR"/>
        <result column="stock_spell" property="stockSpell" jdbcType="VARCHAR"/>
        <result column="buy_order_id" property="buyOrderId" jdbcType="VARCHAR"/>
        <result column="buy_order_time" property="buyOrderTime" jdbcType="TIMESTAMP"/>
        <result column="buy_order_price" property="buyOrderPrice" jdbcType="DECIMAL"/>
        <result column="sell_order_id" property="sellOrderId" jdbcType="VARCHAR"/>
        <result column="sell_order_time" property="sellOrderTime" jdbcType="TIMESTAMP"/>
        <result column="sell_order_price" property="sellOrderPrice" jdbcType="DECIMAL"/>
        <result column="profit_target_price" property="profitTargetPrice" jdbcType="DECIMAL"/>
        <result column="stop_target_price" property="stopTargetPrice" jdbcType="DECIMAL"/>
        <result column="order_direction" property="orderDirection" jdbcType="VARCHAR"/>
        <result column="order_num" property="orderNum" jdbcType="INTEGER"/>
        <result column="order_total_price" property="orderTotalPrice" jdbcType="DECIMAL"/>
        <result column="order_fee" property="orderFee" jdbcType="DECIMAL"/>
        <result column="profit_and_lose" property="profitAndLose" jdbcType="DECIMAL"/>
        <result column="all_profit_and_lose" property="allProfitAndLose" jdbcType="DECIMAL"/>
        <result column="is_lock" property="isLock" jdbcType="INTEGER"/>
        <result column="lock_msg" property="lockMsg" jdbcType="VARCHAR"/>
        <result column="stock_plate" property="stockPlate" jdbcType="VARCHAR"/>
        <result column="position_append_id" property="positionAppendId" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , position_sn, user_id, nick_name, stock_name, stock_code,
    stock_gid, stock_spell, buy_order_id, buy_order_time, buy_order_price, sell_order_id, 
    sell_order_time, sell_order_price, profit_target_price, stop_target_price, order_direction, 
    order_num, order_total_price, order_fee, profit_and_lose, all_profit_and_lose,
    is_lock, lock_msg, stock_plate, position_append_id
    </sql>


    <select id="findPositionBySn" resultMap="BaseResultMap" parameterType="string">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_position
        WHERE position_sn = #{positionSn}
    </select>


    <select id="CountPositionNum" parameterType="map" resultType="integer">
        SELECT COUNT(id) FROM user_position
        <where>
            <if test="state == 1">
                sell_order_id is null
            </if>
            <if test="state == 2">
                and sell_order_id is not null
            </if>
        </where>
    </select>


    <select id="CountPositionProfitAndLose" resultType="decimal" parameterType="integer">
        SELECT sum(profit_and_lose)
        FROM user_position
        WHERE sell_order_id is not null
    </select>

    <select id="CountPositionAllProfitAndLose" resultType="decimal" parameterType="integer">
        SELECT sum(all_profit_and_lose)
        FROM user_position
        WHERE sell_order_id is not null
    </select>


    <delete id="deleteByUserId" parameterType="integer">
        DELETE
        FROM user_position
        WHERE user_id = #{userId}
    </delete>





</mapper>

