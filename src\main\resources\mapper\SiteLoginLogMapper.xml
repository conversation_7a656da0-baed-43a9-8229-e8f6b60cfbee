<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nq.dao.SiteLoginLogMapper" >
  <resultMap id="BaseResultMap" type="com.nq.pojo.SiteLoginLog" >
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="user_id" property="userId" jdbcType="INTEGER"/>
    <result column="login_ip" property="loginIp" jdbcType="VARCHAR"/>
    <result column="login_address" property="loginAddress" jdbcType="VARCHAR"/>
  </resultMap>
  <sql id="Base_Column_List" >
    id, user_id, user_name, login_ip, login_address, add_time
  </sql>

  <select id="loginList" parameterType="integer" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM site_login_log
    <where>
      <if test="userId != null">
        and user_id = #{userId}
      </if>
    </where>
    order by id desc
  </select>
  <delete id="deleteByUserId" parameterType="integer">
    DELETE FROM site_login_log WHERE user_id = #{userId}
  </delete>




</mapper>

