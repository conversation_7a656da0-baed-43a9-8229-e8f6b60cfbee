FROM amazoncorretto:latest

MAINTAINER axing

# 创建目录
RUN mkdir -p /ft/tmpdir
RUN mkdir -p /home/<USER>

EXPOSE 8091


WORKDIR /ft
COPY ./target/stock-1.0.1.jar ./

RUN yum -y install shadow-utils
RUN groupadd -r appgroup && useradd -r -g appgroup appuser && chown -R appuser:appgroup /home/<USER>/ft && chmod -R 777 /home/<USER>/ft/stock-1.0.1.jar

CMD java -Djava.io.tmpdir=/ft/tmpdir -jar -XX:-DisableAttachMechanism -Dfile.encoding=UTF-8 -Duser.timezone=GMT+08 stock-1.0.1.jar
