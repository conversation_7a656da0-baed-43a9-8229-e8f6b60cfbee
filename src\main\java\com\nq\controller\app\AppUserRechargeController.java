package com.nq.controller.app;


import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dto.app.MerchantPayDTO;
import com.nq.pojo.MerchantPay;
import com.nq.pojo.UserRecharge;
import com.nq.service.IUserRechargeService;
import com.nq.service.MerchantPayService;
import com.nq.utils.BeanCopyUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


@RestController
@RequestMapping({"/user/recharge/"})
@Api(tags =  "APP-用户充值接口")
public class AppUserRechargeController {
    private static final Logger log = LoggerFactory.getLogger(AppUserRechargeController.class);

    @Resource
    private IUserRechargeService iUserRechargeService;

    @Resource
    private MerchantPayService merchantPayService;


    //分页查询所有充值记录
    @ApiOperation(value = "充值商户列表", notes = "充值商户列表")
    @PostMapping({"rechargelist.do"})
    public ServerResponse<List<MerchantPayDTO>>  list() {
        //查询所有能用的充值列表
        List<MerchantPay> list = merchantPayService.lambdaQuery().eq(MerchantPay::getStatus, 1).orderByAsc(MerchantPay::getSort).list();
        List<MerchantPayDTO> merchantPayDTOS = BeanCopyUtil.copyToList(list, MerchantPayDTO.class);
        return ServerResponse.createBySuccess(merchantPayDTOS);
    }

    //分页查询所有充值记录
    @ApiOperation(value = "分页查询所有充值记录", notes = "分页查询所有充值记录")
    @PostMapping({"list.do"})
    public ServerResponse<PageInfo<UserRecharge>>  list(@RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "10") int pageSize, @RequestParam(value = "payChannel", required = false) String payChannel, @RequestParam(value = "orderStatus", required = false) String orderStatus) {
        PageInfo<UserRecharge> userChargeList = iUserRechargeService.findUserChargeList(payChannel, orderStatus, pageNum, pageSize);
        return ServerResponse.createBySuccess(userChargeList);
    }

    //账户线下充值转账 创建充值订单
    @ApiOperation("创建充值订单")
    @PostMapping({"inMoney.do"})
    public ServerResponse<String>  inMoney(String amt, Integer payId) {
         return ServerResponse.createBySuccess(iUserRechargeService.inMoney(amt, payId));
    }
}

