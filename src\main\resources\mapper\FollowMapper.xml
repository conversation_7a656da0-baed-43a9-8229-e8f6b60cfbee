<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nq.dao.FollowMapper">

    <resultMap id="BaseResultMap" type="com.nq.pojo.Follow">
        <id column="id" property="id"/>
        <result column="follow_no" property="followNo"/>
        <result column="package_id" property="packageId"/>
        <result column="mentor_id" property="mentorId"/>
        <result column="user_id" property="userId"/>
        <result column="apply_time" property="applyTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="stop_time" property="stopTime" jdbcType="TIMESTAMP"/>
        <result column="amount" property="amount"/>
        <result column="position_rate" property="positionRate"/>
        <result column="salary_rate" property="salaryRate"/>
        <result column="min_amount" property="minAmount"/>
        <result column="max_amount" property="maxAmount"/>
        <result column="is_add" property="isAdd"/>
        <result column="add_amount" property="addAmount"/>
        <result column="today" property="today"/>
        <result column="status" property="status"/>
        <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP"/>
        <result column="package_days" property="packageDays"/>
        <result column="used_days" property="usedDays"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, follow_no, package_id, mentor_id, user_id, apply_time, end_time, stop_time,
        amount, position_rate, salary_rate, min_amount, max_amount, is_add, add_amount,
        today,
        status, audit_time, package_days, used_days, create_time, update_time
    </sql>
    <!-- 自定义查询方法可以在这里添加 -->
</mapper>