package com.nq.vo.stock;


import lombok.Data;

/**
 * 涨跌幅分别基础柱状单元
 */
@Data
public class ChartCellVO {

    private String xAxis;
    private String yAxis;
    //是否涨跌 -1跌 0平 1涨
    private Integer type;

    public ChartCellVO(String yAxis, String xAxis ) {
        this.xAxis = xAxis;
        this.yAxis = yAxis;
        String substring = xAxis.substring(xAxis.length() - 1, xAxis.length());
        if(substring.equals("涨")){
            type=1;
        }else if(substring.equals("平")){
            type=0;
        }else {
            type=-1;
        }

    }


}
