package com.nq.common.interceptor;

import cn.dev33.satoken.exception.NotLoginException;
import com.google.common.collect.Maps;
import com.nq.utils.redis.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.Map;

import com.nq.common.satoken.StpAdminUtil;

@Component
public class ApiAdminAuthorityInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object handler) throws Exception {
        if (HttpMethod.OPTIONS.toString().equals(httpServletRequest.getMethod())) {
            return true;
        }

        try {
            // 使用 Sa-Token 验证管理员权限，指定账号类型为 admin
            StpAdminUtil.checkLogin();
            return true;
        } catch (NotLoginException e) {
            httpServletResponse.setCharacterEncoding("UTF-8");
            httpServletResponse.setContentType("application/json;charset=UTF-8");
            PrintWriter writer = httpServletResponse.getWriter();
            Map<String, Object> map = Maps.newHashMap();
            map.put("success", Boolean.FALSE);
            map.put("status",-1);
            map.put("msg", "请先登录，无权限访问管理员");
            writer.print(JsonUtil.obj2String(map));
            writer.flush();
            writer.close();
            return false;
        }
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object handler, ModelAndView modelAndView) throws Exception {
    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object handler, Exception e) throws Exception {
    }
}
