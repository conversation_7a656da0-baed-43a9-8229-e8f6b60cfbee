package com.nq.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "团队用户展示对象")
public class TeamInfoVO {
    
    @ApiModelProperty(value = "用户名", example = "100")
    private String userName;
    
    @ApiModelProperty(value = "手机号", example = "1888888")
    private String phone;

    @ApiModelProperty(value = "注册时间", example = "3")
    private Date createTime;
} 