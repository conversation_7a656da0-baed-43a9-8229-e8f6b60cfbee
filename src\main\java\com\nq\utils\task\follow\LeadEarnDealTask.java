package com.nq.utils.task.follow;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nq.constant.OrderConstant;
import com.nq.enums.OrderTypeEnum;
import com.nq.enums.TypeEnum;
import com.nq.function.AmountConsumer;
import com.nq.job.task.ITask;
import com.nq.manage.UserAmountChangeManage;
import com.nq.pojo.*;
import com.nq.service.*;
import com.nq.utils.SnowIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component("leadEarnDealTask")
public class LeadEarnDealTask implements ITask {

    @Resource
    private FollowDetailService followDetailService;
    @Resource
    private UserAmountChangeManage userAmountChangeManage;
    @Resource
    private LevelService levelService;
    @Resource
    private IUserService userService;
    @Resource
    private LoadEarnService loadEarnService;

    // 周末晚上11点半 执行一次
    //@Scheduled(cron = "30 23 ? * 7")
    @Override
    public void run(String params) {
        log.info("执行领导者分佣金额"); // 日志记录
        // 查询所有工资比例大于0的等级
        List<Level> levelList = levelService.list(
                new LambdaQueryWrapper<Level>()
                        .gt(Level::getSalaryRate, BigDecimal.ZERO)
        );
        log.info("工资比例大于0的等级数量: {}", levelList.size());
        // 获取所有工资比例大于0的等级ID
        List<Integer> levelIdList = levelList.stream()
                .map(Level::getId)
                .collect(Collectors.toList());
        if (levelIdList.isEmpty()) {
            log.info("没有工资比例大于0的等级，无需分佣。");
            return;
        }
        // 查询所有level属于这些等级的用户
        List<User> leaderList = userService.list(
                new LambdaQueryWrapper<User>()
                        .in(User::getLevel, levelIdList)
        );
        log.info("需要分佣的队长用户数量: {}", leaderList.size());
        // 后续分佣逻辑...
        for (User user : leaderList) {
            try {
                List<User> userList = userService.queryAllLowerEfficientTask(user.getId());
                List<Integer> lowerUserIds = userList.stream()
                        .map(User::getId)
                        .collect(Collectors.toList());
                if (lowerUserIds.isEmpty()) {
                    log.info("用户 {} 没有有效下级，无需分佣。", user.getId());
                    continue;
                }
                Date[] week = com.nq.utils.DateUtils.getWeekStartAndEnd(0);
                List<FollowDetail> followDetails = followDetailService.list(
                        new LambdaQueryWrapper<FollowDetail>()
                                .in(FollowDetail::getUserId, lowerUserIds)
                                .eq(FollowDetail::getStatus, 2)
                                .between(FollowDetail::getCreateTime, week[0], week[1])
                );
                log.info("用户 {} 下级本周已清仓跟单详情数量: {}", user.getId(), followDetails.size());
                BigDecimal totalFollowEarnAmount = BigDecimal.ZERO;
                for (FollowDetail followDetail : followDetails) {
                    totalFollowEarnAmount = totalFollowEarnAmount.add(followDetail.getProfit().subtract(followDetail.getSalary()));
                }
                log.info("用户 {} 本周已清仓跟单详情总金额: {}", user.getId(), totalFollowEarnAmount);

                Level level = levelService.getById(user.getLevel());
                BigDecimal amount = totalFollowEarnAmount.multiply(level.getSalaryRate());
                log.info("用户 {} 本周已清仓跟单详情分佣金额: {}", user.getId(), amount);

                LoadEarn loadEarn = new LoadEarn();
                String orderNo = SnowIdUtil.getId(OrderConstant.loadEarn);
                loadEarn.setOrderNo(orderNo);
                loadEarn.setUserId(user.getId());
                loadEarn.setUserName(user.getNickName());
                loadEarn.setRealName(user.getRealName());
                loadEarn.setLevel(user.getLevel());
                loadEarn.setSalaryRate(level.getSalaryRate());
                loadEarn.setTotalFollowEarnAmount(totalFollowEarnAmount);
                loadEarn.setStartTime(week[0]);
                loadEarn.setEndTime(week[1]);
                loadEarn.setAmount(amount);
                AmountConsumer amountConsumer = (oldUser, newUser) -> {
                    loadEarnService.save(loadEarn);
                    userService.addLeadEarnAmount(user.getId(), amount);
                };
                userAmountChangeManage.changeBalance(user.getId(), amount, orderNo, OrderTypeEnum.TEAM_SALARY, TypeEnum.TEAM_SALARY, "", "", amountConsumer);
            } catch (Exception e) {
                log.error("队长分佣处理异常，userId={}，异常信息：{}", user.getId(), e.getMessage(), e);
                // 跳过本次，继续下一个
            }
        }
    }


}