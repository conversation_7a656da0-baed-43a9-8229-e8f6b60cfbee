<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nq.dao.StockMarketsDayMapper" >
 
  <resultMap id="BaseResultMap" type="com.nq.pojo.StockMarketsDay">
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="stock_id" property="stockId" jdbcType="INTEGER"/>
    <result column="stock_name" property="stockName" jdbcType="VARCHAR"/>
    <result column="stock_code" property="stockCode" jdbcType="VARCHAR"/>
    <result column="stock_gid" property="stockGid" jdbcType="VARCHAR"/>
    <result column="ymd" property="ymd" jdbcType="VARCHAR"/>
    <result column="hms" property="hms" jdbcType="VARCHAR"/>
    <result column="now_price" property="nowPrice" jdbcType="DECIMAL"/>
    <result column="crease_rate" property="creaseRate" jdbcType="DECIMAL"/>
    <result column="open_px" property="openPx" jdbcType="VARCHAR"/>
    <result column="close_px" property="closePx" jdbcType="VARCHAR"/>
    <result column="business_balance" property="businessBalance" jdbcType="VARCHAR"/>
    <result column="business_amount" property="businessAmount" jdbcType="VARCHAR"/>
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
    <result column="add_time_str" property="addTimeStr" jdbcType="VARCHAR"/>
  </resultMap>
  <sql id="Base_Column_List" >
    id, stock_id, stock_name, stock_code, stock_gid, ymd, hms, now_price, crease_rate, 
    open_px, close_px, business_balance, business_amount, add_time, add_time_str
  </sql>
  <select id="selectRateByDaysAndStockCode" parameterType="map" resultType="decimal">
    select sum(a.crease_rate) FROM (
    select crease_rate
    FROM stock_markets_day
    WHERE stock_id = #{stockId}
    order by id DESC
    limit 0,#{days}) as a
  </select>



</mapper>




