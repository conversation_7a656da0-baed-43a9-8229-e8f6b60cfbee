package com.nq.controller;

import com.nq.common.ServerResponse;
import com.nq.service.IStockService;
import com.nq.service.RealTimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@RestController
@RequestMapping({"/api/realTime/"})
public class RealTimeController {

    @Resource
    private RealTimeService realTimeService;

    @Resource
    private IStockService stockService;

    @RequestMapping({"findStock.do"})
    public ServerResponse<Map> findStock(@RequestParam(value = "stockCode", required = false) String stockCode) {
        Map stock = this.realTimeService.findStock(stockCode);
        return ServerResponse.createBySuccess(stock);
    }

}
