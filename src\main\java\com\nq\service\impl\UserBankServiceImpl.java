package com.nq.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nq.common.ServerResponse;
import com.nq.dao.UserBankMapper;
import com.nq.excepton.CustomException;
import com.nq.pojo.User;
import com.nq.pojo.UserBank;
import com.nq.service.IUserBankService;
import com.nq.service.IUserService;
import com.nq.vo.user.UserBankInfoVO;
import java.util.Date;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Service;

@Service("iUserBankService")
public class UserBankServiceImpl extends ServiceImpl<UserBankMapper, UserBank> implements IUserBankService {
    @Resource
    private UserBankMapper userBankMapper;

    @Resource
    private IUserService iUserService;

    public UserBank findUserBankByUserId(Integer userId) {
        return this.userBankMapper.findUserBankByUserId(userId);
    }

    public void addBank(UserBank bank, HttpServletRequest request) {
        User user = this.iUserService.getCurrentUser();
        if (user == null) {
            throw new CustomException("請先登錄");
        }
        UserBank dbBank = this.userBankMapper.findUserBankByUserId(user.getId());
        if (dbBank != null) {
            throw new CustomException("银行信息已存在，不要重复添加");
        }
        UserBank userBank = new UserBank();
        userBank.setUserId(user.getId());
        userBank.setBankName(bank.getBankName());
        if (bank.getBankNo().length() < 16) {
            throw new CustomException("银行卡号大于16位");
        }
        userBank.setBankNo(bank.getBankNo());
        userBank.setBankAddress(bank.getBankAddress());
        userBank.setBankImg(bank.getBankImg());
        userBank.setBankPhone(bank.getBankPhone());
        userBank.setAddTime(new Date());
        int insertCount = this.userBankMapper.insert(userBank);
        if (insertCount <= 0) {
            throw new CustomException("添加银行卡失败");
        }
    }

    public void updateBank(UserBank bank, HttpServletRequest request) {
        User user = this.iUserService.getCurrentUser();
        UserBank dbBank = this.userBankMapper.findUserBankByUserId(user.getId());
        if (dbBank == null) {
            throw new CustomException("修改失败，找不到银行");
        }
        dbBank.setBankName(bank.getBankName());
        dbBank.setBankNo(bank.getBankNo());
        dbBank.setBankAddress(bank.getBankAddress());
        dbBank.setBankImg(bank.getBankImg());
        dbBank.setBankPhone(bank.getBankPhone());
        int updateCount = this.userBankMapper.updateById(dbBank);
        if (updateCount <= 0) {
            throw new CustomException("修改银行卡失败");
        }
    }

    public UserBankInfoVO getBankInfo(HttpServletRequest request) {
        User user = this.iUserService.getCurrentUser();
        UserBank dbBank = this.userBankMapper.findUserBankByUserId(user.getId());
        if (dbBank == null) {
            throw new CustomException("未添加银行信息");
        }
        UserBankInfoVO userBankInfoVO = new UserBankInfoVO();
        userBankInfoVO.setId(dbBank.getId());
        userBankInfoVO.setRealName(user.getRealName());
        userBankInfoVO.setBankName(dbBank.getBankName());
        userBankInfoVO.setBankAddress(dbBank.getBankAddress());
        userBankInfoVO.setBankNo(dbBank.getBankNo());
        return userBankInfoVO;
    }

    public void updateBankByAdmin(UserBank formData) {
        Integer userId = formData.getUserId();
        if (userId == null) {
            throw new CustomException("缺少参数");
        }
        UserBank existData = this.userBankMapper.findUserBankByUserId(userId);
        int updateCount;
        if (existData == null) {
            UserBank userBank = new UserBank();
            userBank.setUserId(formData.getUserId());
            userBank.setBankName(formData.getBankName());
            userBank.setBankNo(formData.getBankNo());
            userBank.setBankAddress(formData.getBankAddress());
            userBank.setBankImg(formData.getBankImg());
            userBank.setBankPhone(formData.getBankPhone());
            userBank.setAddTime(new Date());
            updateCount = this.userBankMapper.insert(userBank);
        } else {
            formData.setId(existData.getId());
            updateCount = this.userBankMapper.updateById(formData);
        }
        if (updateCount <= 0) {
            throw new CustomException("修改失败");
        }
    }

    public UserBank getBank(Integer userId) {
        return this.userBankMapper.findUserBankByUserId(userId);
    }
}
