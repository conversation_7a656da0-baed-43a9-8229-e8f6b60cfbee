package com.nq.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.ConvertBond;
import com.nq.service.ConvertBondService;
import com.nq.dao.ConvertBondMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【convert_bond(转债债券表)】的数据库操作Service实现
* @createDate 2022-12-05 16:33:20
*/
@Service
public class ConvertBondServiceImpl extends ServiceImpl<ConvertBondMapper, ConvertBond>
    implements ConvertBondService{
    @Resource
    private ConvertBondMapper convertBondMapper;

    @Override
    public ServerResponse<PageInfo<ConvertBond>> listByPage(Integer page, Integer size) {
        PageHelper.startPage(page,size);
        List<ConvertBond> convertBonds = this.convertBondMapper.selectList(null);
        PageInfo<ConvertBond>  pageInfo = new PageInfo<ConvertBond>(convertBonds);
        return ServerResponse.createBySuccess(pageInfo);
    }
}




