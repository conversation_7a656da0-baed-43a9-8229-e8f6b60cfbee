package com.nq.pojo;

import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;
import java.beans.ConstructorProperties;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(description = "股票日行情数据")
@AllArgsConstructor
@NoArgsConstructor
public class StockMarketsDay extends BaseEntity {

    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "股票ID", example = "1")
    private Integer stockId;

    @ApiModelProperty(value = "股票名称", example = "平安银行")
    private String stockName;

    @ApiModelProperty(value = "股票代码", example = "000001")
    private String stockCode;

    @ApiModelProperty(value = "股票唯一标识", example = "sz000001")
    private String stockGid;

    @ApiModelProperty(value = "交易日期", example = "2024-01-01")
    private String ymd;

    @ApiModelProperty(value = "交易时间", example = "15:00:00")
    private String hms;

    @ApiModelProperty(value = "当前价格", example = "10.5")
    private BigDecimal nowPrice;

    @ApiModelProperty(value = "涨跌幅", example = "0.05")
    private BigDecimal creaseRate;

    @ApiModelProperty(value = "开盘价", example = "10.0")
    private String openPx;

    @ApiModelProperty(value = "收盘价", example = "10.5")
    private String closePx;

    @ApiModelProperty(value = "成交额", example = "1000000")
    private String businessBalance;

    @ApiModelProperty(value = "成交量", example = "100000")
    private String businessAmount;

    @ApiModelProperty(value = "添加时间", example = "2024-01-01 15:00:00")
    private Date addTime;

    @ApiModelProperty(value = "添加时间字符串", example = "2024-01-01 15:00:00")
    private String addTimeStr;



}
