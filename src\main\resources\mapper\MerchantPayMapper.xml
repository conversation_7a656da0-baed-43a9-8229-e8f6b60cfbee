<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nq.dao.MerchantPayMapper">
  <resultMap id="BaseResultMap" type="com.nq.pojo.MerchantPay">
    <!--@mbg.generated-->
    <!--@Table merchant_pay-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="avatar" jdbcType="VARCHAR" property="avatar" />
    <result column="recharge_range" jdbcType="VARCHAR" property="rechargeRange" />
    <result column="withdraw_range" jdbcType="VARCHAR" property="withdrawRange" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="rate" jdbcType="DECIMAL" property="rate" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="param_str" jdbcType="VARCHAR" property="paramStr" />
    <result column="call_back_ip" jdbcType="VARCHAR" property="callBackIp" />
    <result column="is_rechange" jdbcType="INTEGER" property="isRechange" />
    <result column="is_withdraw" jdbcType="INTEGER" property="isWithdraw" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, avatar, recharge_range, withdraw_range, currency, rate, code, `status`, 
    param_str, call_back_ip, is_rechange, is_withdraw, sort, update_time, create_time
  </sql>
</mapper>