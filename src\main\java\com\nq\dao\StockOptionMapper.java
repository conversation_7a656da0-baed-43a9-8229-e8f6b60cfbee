package com.nq.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nq.pojo.StockOption;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
public interface StockOptionMapper extends BaseMapper<StockOption> {


  StockOption findMyOptionIsExistByCode(@Param("uid") Integer paramInteger, @Param("stockCode") String paramString);
  
  List<StockOption> findMyOptionByKeywords(@Param("uid") Integer paramInteger, @Param("keyWords") String paramString);
  
  StockOption isOption(@Param("uid") Integer paramInteger, @Param("code") String paramString);
}
