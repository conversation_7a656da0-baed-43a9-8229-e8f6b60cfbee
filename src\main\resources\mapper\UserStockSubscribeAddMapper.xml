<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nq.dao.UserStockSubscribeAddMapper">
  <resultMap id="BaseResultMap" type="com.nq.pojo.UserStockSubscribeAdd">
    <!--@mbg.generated-->
    <!--@Table user_stock_subscribe_add-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="stock_type" jdbcType="VARCHAR" property="stockType" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="buy_nums" jdbcType="INTEGER" property="buyNums" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="buy_time" jdbcType="TIMESTAMP" property="buyTime" />
    <result column="list_time" jdbcType="TIMESTAMP" property="listTime" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, order_no, user_id, real_name, phone, code, `name`, stock_type, price, buy_nums, 
    `status`, buy_time, list_time, remarks
  </sql>
</mapper>