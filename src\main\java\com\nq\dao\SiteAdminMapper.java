package com.nq.dao;



import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nq.pojo.SiteAdmin;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
public interface SiteAdminMapper extends BaseMapper<SiteAdmin> {


  SiteAdmin login(@Param("adminPhone") String paramString1, @Param("adminPwd") String paramString2);
  
  List<SiteAdmin> listByAdmin(@Param("adminName") String paramString1, @Param("adminPhone") String paramString2, @Param("superAdmin") String paramString3);
  
  SiteAdmin findAdminByName(String paramString);
  
  SiteAdmin findAdminByPhone(String paramString);
}