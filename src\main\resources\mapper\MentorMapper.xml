<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nq.dao.MentorMapper">
    
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.nq.pojo.Mentor">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="mentor_name" property="mentorName"/>
        <result column="mentor_phone" property="mentorPhone"/>
        <result column="mentor_account" property="mentorAccount"/>
        <result column="company" property="company"/>
        <result column="investment_years" property="investmentYears"/>
        <result column="salary_rate" property="salaryRate"/>
        <result column="description" property="description"/>
        <result column="image_url" property="imageUrl"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, mentor_name, mentor_phone, mentor_account, company, investment_years, salary_rate, description, image_url, status, create_time, update_time
    </sql>
</mapper> 