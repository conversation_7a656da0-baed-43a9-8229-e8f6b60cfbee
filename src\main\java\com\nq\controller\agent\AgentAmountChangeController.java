package com.nq.controller.agent;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.enums.TypeEnum;
import com.nq.service.AmountChangeService;
import com.nq.vo.app.AmountTypeGroupItemVO;
import com.nq.vo.user.AmountChangeTypeVo;
import com.nq.vo.user.AmountChangeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Api(tags = "后台-账变记录管理")
@RestController
@RequestMapping("/agent/amountChange")
public class AgentAmountChangeController {

    @Resource
    private AmountChangeService amountChangeService;

    @ApiOperation(value = "获取所有的账遍类型", notes = "获取所有的账遍类型")
    @PostMapping("/getAmountChangeType")
    public ServerResponse<List<AmountTypeGroupItemVO>> getAmountChangeType() {
        List<AmountTypeGroupItemVO> amountTypeMap = getAmountTypeMap();
        return ServerResponse.createBySuccess(amountTypeMap);
    }
    public List<AmountTypeGroupItemVO> getAmountTypeMap() {
        List<AmountTypeGroupItemVO> result = new ArrayList<>();
        List<AmountChangeTypeVo> zeroList = new ArrayList<>();
        // 先收集 amountType=0 的类型
        for (TypeEnum type : TypeEnum.values()) {
            if (type.getAmountType() == 0) {
                zeroList.add(new AmountChangeTypeVo(type.getCode(), type.getDesc()));
            }
        }
        // 只处理 1 和 2
        for (int amtType : new int[]{1, 2}) {
            List<AmountChangeTypeVo> list = new ArrayList<>();
            for (TypeEnum type : TypeEnum.values()) {
                if (type.getAmountType() == amtType) {
                    list.add(new AmountChangeTypeVo(type.getCode(), type.getDesc()));
                }
            }
            // 追加 0 的类型
            list.addAll(zeroList);
            AmountTypeGroupItemVO item = new AmountTypeGroupItemVO();
            item.setAmountType(amtType);
            item.setTypes(list);
            result.add(item);
        }
        return result;
    }

    @ApiOperation("分页查询账变记录")
    @GetMapping("/list")
    public ServerResponse<PageInfo<AmountChangeVO>> list(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("用户ID") @RequestParam(required = false) Long userId,
            @ApiParam("昵称") @RequestParam(required = false) String nickName,
            @ApiParam("账户类型, 1 平台账户, 2 证券账户") @RequestParam(required = false) Integer amountType,
            @ApiParam("订单类型") @RequestParam(required = false) Integer type,
            @ApiParam("收支类型") @RequestParam(required = false) Integer accountType) {
        
        PageInfo<AmountChangeVO> pageInfo = amountChangeService.pageList(page, size, userId,nickName, amountType, type, accountType, true);
        return ServerResponse.createBySuccess(pageInfo);
    }

    @ApiOperation("获取账变详情")
    @GetMapping("/detail")
    public ServerResponse<AmountChangeVO> detail(@ApiParam("账变ID") @RequestParam Long id) {
        AmountChangeVO vo = amountChangeService.getDetailById(id);
        return ServerResponse.createBySuccess(vo);
    }
} 