package com.nq.controller.admin;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dto.common.CommonPage;
import com.nq.pojo.LoadEarn;
import com.nq.service.LoadEarnService;
import com.nq.vo.user.LoadEarnVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "后台-工资列表")
@RestController
@RequestMapping({"/admin/loadEarn/"})
public class AdminLoadEarnController {
    @Resource
    private LoadEarnService loadEarnService;

    @ApiOperation("查询工资列表")
    @PostMapping("list.do")
    public ServerResponse<PageInfo<LoadEarnVO>> pageList(LoadEarn loadEarn, CommonPage commonPage) {
        PageInfo<LoadEarnVO>  voPageInfo =  loadEarnService.pageList(loadEarn,commonPage);
        return ServerResponse.createBySuccess(voPageInfo);
    }

}
