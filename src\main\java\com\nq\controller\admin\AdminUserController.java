package com.nq.controller.admin;


import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dto.common.CommonPage;
import com.nq.pojo.User;
import com.nq.pojo.UserBank;
import com.nq.service.IUserBankService;
import com.nq.service.IUserService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.nq.vo.user.UserInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping({"/admin/user/"})
@Api(tags = {"后台-用户管理"})
public class AdminUserController {
    private static final Logger log = LoggerFactory.getLogger(AdminUserController.class);
    @Resource
    private IUserService iUserService;
    @Resource
    private IUserBankService iUserBankService;
    //分页查询所有用户列表信息 及模糊查询用户信息
    @PostMapping({"list.do"})
    @ApiOperation("分页查询所有用户列表信息及模糊查询用户信息")
    public ServerResponse<PageInfo<User>> list(User user, CommonPage commonPage) {
        PageInfo<User> userPageInfo = this.iUserService.listByAdmin(user, commonPage);
        return ServerResponse.createBySuccess(userPageInfo);
    }

    //查询用户信息是否存在
    @PostMapping({"detail.do"})
    @ApiOperation("查询用户信息是否存在")
    public ServerResponse<User> detail(Integer userId) {
        User user = this.iUserService.findByUserId(userId);
        return ServerResponse.createBySuccess(user);
    }

    @GetMapping({"updateLock.do"})
    @ApiOperation("修改用户交易锁定状态")
    public ServerResponse<String> updateLock(Integer userId) {
        this.iUserService.updateLock(userId);
        return ServerResponse.createBySuccess();
    }


    @GetMapping({"updateLoginLock.do"})
    @ApiOperation("修改用户登录锁定状态")
    public ServerResponse<String> updateLoginLock(Integer userId) {
        this.iUserService.updateLoginLock(userId);
        return ServerResponse.createBySuccess();
    }


    //修改用户列表 用户资金入款/扣款
    @PostMapping({"updateAmt.do"})
    @ApiOperation("修改用户资金入款/扣款")
    public ServerResponse<String> updateAmt(Integer userId, String amt, Integer direction) {
        //amt转Integer
        this.iUserService.updateAmt(userId, amt, direction);
        return ServerResponse.createBySuccess();
    }

    //修改用户列表 用户信息
    @PostMapping({"update.do"})
    @ApiOperation("修改用户信息")
    public ServerResponse<String> update(User user) {
        this.iUserService.update(user);
        return ServerResponse.createBySuccess();
    }

    //修改用户列表 银行卡信息
    @PostMapping({"updateBank.do"})
    @ApiOperation("修改用户银行卡信息")
    public ServerResponse<String> updateBank(UserBank userBank) {
        this.iUserBankService.updateBankByAdmin(userBank);
        return ServerResponse.createBySuccess();
    }


    @PostMapping({"authByAdmin.do"})
    @ApiOperation("后台实名认证审核")
    public ServerResponse<String> authByAdmin(Integer userId, Integer state, String authMsg) {
        this.iUserService.authByAdmin(userId, state, authMsg);
        return ServerResponse.createBySuccess();
    }

    //查看指定 用户列表的用户信息
    @PostMapping({"getBank.do"})
    @ApiOperation("查看指定用户的银行卡信息")
    public ServerResponse<UserBank> getBank(Integer userId) {
        UserBank bank = this.iUserBankService.getBank(userId);
        return ServerResponse.createBySuccess(bank);
    }

    //删除用户列表 用户信息
    @PostMapping({"delete.do"})
    @ApiOperation("删除用户信息")
    public ServerResponse<String> delete(Integer userId) {
        this.iUserService.delete(userId);
        return ServerResponse.createBySuccess();
    }

    @PostMapping({"addSimulatedAccount.do"})
    @ApiOperation("新增用户")
    public ServerResponse<String> addSimulatedAccount(
            @RequestParam("nickName") String nickName,
            @RequestParam("phone") String phone,
            @RequestParam("amt") String amt,
            @RequestParam("pwd") String pwd,
            @RequestParam("pid") Long pid
    ) {
        this.iUserService.addSimulatedAccount(nickName,phone, pwd, amt, pid);
        return ServerResponse.createBySuccess();
    }


}
