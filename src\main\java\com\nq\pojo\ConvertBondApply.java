package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;

/**
 * @TableName convert_bond_apply
 */
@TableName(value ="convert_bond_apply")
@Data
@ApiModel(description = "可转债申购申请信息")
@AllArgsConstructor
@NoArgsConstructor
public class ConvertBondApply extends BaseEntity implements Serializable {
    @TableId(type = IdType.AUTO,value = "id")
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "代理商ID", example = "1001")
    private Integer agentId;

    @ApiModelProperty(value = "用户ID", example = "2001")
    private Integer userId;

    @ApiModelProperty(value = "手机号码", example = "13800138000")
    private String phone;

    @ApiModelProperty(value = "可转债ID", example = "3001")
    private Integer bondId;

    @ApiModelProperty(value = "申购金额", example = "10000.00")
    private BigDecimal applyMoney;

    @ApiModelProperty(value = "申购数量", example = "100")
    private Integer applyNum;

    @ApiModelProperty(value = "中签数量", example = "10")
    private Integer sucNum;

    @ApiModelProperty(value = "中签金额", example = "1000.00")
    private BigDecimal sucMony;

    @ApiModelProperty(value = "申购日期", example = "2024-01-01")
    private Date applyDate;

    @ApiModelProperty(value = "状态：0-待处理，1-已中签，2-未中签", example = "1")
    private Integer status;

    @ApiModelProperty(value = "退款金额", example = "9000.00")
    private BigDecimal refundMony;

    @ApiModelProperty(value = "中签日期", example = "2024-01-02")
    private Date sucDate;

    private static final long serialVersionUID = 1L;
}