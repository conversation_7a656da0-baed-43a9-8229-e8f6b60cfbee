package com.nq.vo.user;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 商户账变表
 */
@ApiModel(description="账变头部")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AmountChangeTypeVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 收入
     */
    @ApiModelProperty(value = "类型")
    private Integer type;
    /**
     * 关联的订单号
     */
    @ApiModelProperty(value="类型名称")
    private String typeName;

}
