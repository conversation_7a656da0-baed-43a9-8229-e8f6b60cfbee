
package com.nq.vo.stockfutures;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
public class StockFuturesListVO {
    private Integer id;

    private String futuresName;

    private String futuresCode;

    private String futuresGid;

    private String futuresUnit;

    private Integer futuresStandard;

    private String coinCode;

    private Integer homeShow;

    private Integer listShow;
    private Integer depositAmt;

    private Integer transFee;
    /*是否添加自选：1、添加自选，0、未添加自选*/
    private String isOption;

    private Integer minNum;

    private Integer maxNum;

    private Integer transState;

    private String transAmBegin;

    private String transAmEnd;

    private String transPmBegin;

    private String transPmEnd;

    private Date addTime;

    private String tDesc;

    private String nowPrice;

    private String lastClose;

    private BigDecimal coinRate;


}

