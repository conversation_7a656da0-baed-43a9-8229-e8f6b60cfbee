package com.nq.controller.app;

import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.SiteNews;
import com.nq.service.ISiteNewsService;
import com.nq.service.IUserPositionService;
import com.nq.utils.HttpClientRequest;
import com.nq.vo.position.UserPositionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

//新闻资讯-列表查询
@RestController
@RequestMapping({"/api/news/"})
@Api(tags = "APP-新闻")
public class AppNewsController {
    @Resource
    private ISiteNewsService iSiteNewsService;


    //新闻资讯-列表查询
    @GetMapping({"getNewsList.do"})
    @ApiOperation("新闻资讯-列表查询")
    public ServerResponse<PageInfo<SiteNews>> getNewsList(@RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "15") int pageSize, @RequestParam(value = "type", defaultValue = "0") Integer type, @RequestParam(value = "sort", defaultValue = "time1") String sort, @RequestParam(value = "keyword", required = false) String keyword, HttpServletRequest request) {
        PageInfo<SiteNews> pageInfo = iSiteNewsService.getList(pageNum, pageSize, type, sort, keyword, request);
        return ServerResponse.createBySuccess(pageInfo);
    }

    //新闻资讯-详情
    @GetMapping({"getDetail.do"})
    @ApiOperation("新闻资讯-详情")
    public ServerResponse<SiteNews> getDetail(int id, HttpServletRequest request) {
        SiteNews detail = iSiteNewsService.getDetail(id, request);
        return ServerResponse.createBySuccess(detail);
    }


}