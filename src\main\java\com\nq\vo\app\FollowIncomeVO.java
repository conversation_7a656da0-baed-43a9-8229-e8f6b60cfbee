package com.nq.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "跟单收益展示对象")
public class FollowIncomeVO {
    
    @ApiModelProperty(value = "总收益", example = "1874.69")
    private BigDecimal totalIncome;
    
    @ApiModelProperty(value = "今日收益", example = "-21.06")
    private BigDecimal todayIncome;
    
    @ApiModelProperty(value = "跟单总数", example = "94")
    private Integer totalFollowNum;
    
    @ApiModelProperty(value = "今日跟单数", example = "2")
    private Integer todayFollowNum;
} 