<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nq.dao.StockMapper" >
  <resultMap id="BaseResultMap" type="com.nq.pojo.Stock" >
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="stock_name" property="stockName" jdbcType="VARCHAR"/>
    <result column="stock_code" property="stockCode" jdbcType="VARCHAR"/>
    <result column="stock_spell" property="stockSpell" jdbcType="VARCHAR"/>
    <result column="stock_type" property="stockType" jdbcType="VARCHAR"/>
    <result column="stock_gid" property="stockGid" jdbcType="VARCHAR"/>
    <result column="stock_plate" property="stockPlate" jdbcType="VARCHAR"/>
    <result column="is_lock" property="isLock" jdbcType="INTEGER"/>
    <result column="is_show" property="isShow" jdbcType="INTEGER"/>
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
    <result column="spread_rate" property="spreadRate" jdbcType="DECIMAL"/>
    <result column="data_base" property="dataBase" jdbcType="INTEGER"/>
  </resultMap>
  <sql id="Base_Column_List" >
    id, stock_name, stock_code, stock_spell, stock_type, stock_gid, stock_plate, is_lock,
    is_show, add_time,spread_rate,data_base
  </sql>


  <select id="findStockListByKeyWords" resultMap="BaseResultMap" parameterType="map">
    SELECT
    <include refid="Base_Column_List" />
    from stock
    <where>
      is_show = #{show}
      <if test="keyWords != null and keyWords != '' ">

        and (stock_type like concat('%',#{keyWords},'%') or stock_code like concat('%',#{keyWords},'%') or stock_name like concat('%',#{keyWords},'%')  )

      </if>
      <if test="stockPlate != null and stockPlate != '' ">
        and stock_plate = #{stockPlate}
      </if>
      <if test="stockPlate == null or stockPlate == '' ">
        and (stock_plate is null or stock_plate='' or stock_plate='科创' or stock_plate='创业' )
      </if>
      <if test="stockType != null and stockType != '' ">
        and stock_type = #{stockType}
      </if>

    </where>
    ORDER BY id ASC
  </select>

  <select id="findStockCode" resultMap="BaseResultMap" parameterType="map">
    SELECT
    <include refid="Base_Column_List" />
    from stock
    <where>
      <if test="stockType != null and stockType != '' ">
        and stock_type = #{stockType}
      </if>
        GROUP BY stock_gid
        ORDER BY stock_gid DESC
        limit #{stock_num},#{stock_nums}
    </where>
  </select>

  <select id="findStockByCode" resultMap="BaseResultMap" parameterType="string">
    SELECT
    <include refid="Base_Column_List" />
    from stock
    WHERE stock_code = #{code}
  </select>
  <select id="findStockByName" resultMap="BaseResultMap" parameterType="string">
    SELECT
    <include refid="Base_Column_List" />
    from stock
    WHERE stock_name = #{name}
  </select>


  <select id="listByAdmin" resultMap="BaseResultMap" parameterType="map">
    SELECT
    <include refid="Base_Column_List" />
    from stock
    <where>
      <if test="showState != null  ">
        and is_show = #{showState}
      </if>
      <if test="lockState != null ">
        and is_lock = #{lockState}
      </if>
      <if test="code != null and code != '' ">
        and stock_code like CONCAT('%','${code}','%')
      </if>
      <if test="name != null and name != '' ">
        and stock_name like CONCAT('%','${name}','%')
      </if>
      <if test="stockPlate != null and stockPlate != '' ">
        and stock_plate = #{stockPlate}
      </if>
      <if test="stockType != null and stockType != '' ">
        and stock_type = #{stockType}
      </if>
    </where>
  </select>


  <select id="CountStockNum" resultType="integer">
    SELECT COUNT(id) FROM stock
  </select>
  <select id="CountShowNum" resultType="integer" parameterType="integer">
    SELECT COUNT(id) FROM stock WHERE is_show = #{showState}
  </select>
  <select id="CountUnLockNum" resultType="integer" parameterType="integer">
    SELECT COUNT(id) FROM stock WHERE is_lock = #{showState}
  </select>


  <select id="findStockList" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM stock
  </select>

</mapper>