<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nq.dao.SiteBannerMapper" >
  <resultMap id="BaseResultMap" type="com.nq.pojo.SiteBanner" >
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="banner_url" property="bannerUrl" jdbcType="VARCHAR"/>
    <result column="is_order" property="isOrder" jdbcType="INTEGER"/>
    <result column="is_pc" property="isPc" jdbcType="INTEGER"/>
    <result column="is_m" property="isM" jdbcType="INTEGER"/>
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
    <result column="ban_title" property="banTitle" jdbcType="VARCHAR"/>
    <result column="ban_desc" property="banDesc" jdbcType="VARCHAR"/>
    <result column="target_url" property="targetUrl" jdbcType="VARCHAR"/>
  </resultMap>
  <sql id="Base_Column_List" >
    id, banner_url, is_order, is_pc, is_m, add_time, ban_title, ban_desc, target_url
  </sql>

  <select id="listByAdmin" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM site_banner
    ORDER BY is_order DESC
  </select>

  <select id="getBannerByMobile" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM site_banner
    WHERE is_m = 0
    ORDER  BY is_order DESC
  </select>

  <select id="getBannerByPC" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM site_banner
    WHERE is_pc = 0
    ORDER  BY is_order DESC
  </select>


</mapper>