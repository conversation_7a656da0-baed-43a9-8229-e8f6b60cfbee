package com.nq.vo.mentor;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(description = "导师信息VO")
public class MentorVO  {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "导师ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "绑定的用户id", example = "1")
    private Integer userId;

    @ApiModelProperty(value = "导师姓名", example = "张三")
    private String mentorName;

    @ApiModelProperty(value = "导师手机号", example = "***********")
    private String mentorPhone;

    @ApiModelProperty(value = "导师账户", example = "mentor001")
    private String mentorAccount;

    @ApiModelProperty(value = "就职于公司", example = "某某投资公司")
    private String company;

    @ApiModelProperty(value = "网投资年数", example = "5")
    private Integer investmentYears;

    @ApiModelProperty(value = "返佣比例", example = "0.1")
    private BigDecimal salaryRate;

    @ApiModelProperty(value = "导师描述")
    private String description;

    @ApiModelProperty(value = "形象图片URL")
    private String imageUrl;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "状态", example = "1", notes = "1:正常 0:禁用")
    private Integer status;


} 