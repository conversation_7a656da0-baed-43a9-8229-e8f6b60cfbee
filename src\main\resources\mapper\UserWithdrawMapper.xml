<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nq.dao.UserWithdrawMapper" >
  <resultMap id="BaseResultMap" type="com.nq.pojo.UserWithdraw" >
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="user_id" property="userId" jdbcType="INTEGER"/>
    <result column="nick_name" property="nickName" jdbcType="VARCHAR"/>
    <result column="with_amt" property="withAmt" jdbcType="DECIMAL"/>
    <result column="apply_time" property="applyTime" jdbcType="TIMESTAMP"/>
    <result column="trans_time" property="transTime" jdbcType="TIMESTAMP"/>
    <result column="with_name" property="withName" jdbcType="VARCHAR"/>
    <result column="bank_no" property="bankNo" jdbcType="VARCHAR"/>
    <result column="bank_name" property="bankName" jdbcType="VARCHAR"/>
    <result column="bank_address" property="bankAddress" jdbcType="VARCHAR"/>
    <result column="with_status" property="withStatus" jdbcType="INTEGER"/>
    <result column="with_fee" property="withFee" jdbcType="DECIMAL"/>
    <result column="with_msg" property="withMsg" jdbcType="VARCHAR"/>
  </resultMap>
  <sql id="Base_Column_List" >
    id, user_id, nick_name, with_amt, apply_time, trans_time, with_name, bank_no,
    bank_name, bank_address, with_status, with_fee, with_msg
  </sql>



  <delete id="deleteByUserId" parameterType="integer">
    DELETE FROM user_withdraw WHERE user_id = #{userId}
  </delete>


</mapper>