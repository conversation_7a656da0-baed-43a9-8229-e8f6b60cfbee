package com.nq.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.codehaus.jackson.annotate.JsonIgnore;
import org.codehaus.jackson.map.annotate.JsonSerialize;

import java.io.Serializable;

@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ApiModel(description = "通用响应对象")
@NoArgsConstructor
@Data
public class ServerResponse<T> extends Object implements Serializable {
    @ApiModelProperty(value = "状态码", example = "0")
    private int status;
    
    @ApiModelProperty(value = "响应消息", example = "操作成功")
    private String msg;
    
    @ApiModelProperty(value = "响应数据",example = "1")
    private T data;

    private ServerResponse(int status) {
        this.status = status;
    }

    private ServerResponse(int status, T data) {
        this.status = status;
        this.data = data;
    }

    private ServerResponse(int status, String msg) {
        this.status = status;
        this.msg = msg;
    }

    private ServerResponse(int status, String msg, T data) {
        this.status = status;
        this.msg = msg;
        this.data = data;
    }



    @JsonIgnore
    public boolean isSuccess() {
        return (this.status == ResponseCode.SUCCESS.getCode());
    }

    public static <T> ServerResponse<T> createBySuccess() {
        return new ServerResponse(ResponseCode.SUCCESS.getCode(),"操作成功");
    }

    public static <T> ServerResponse<T> createBySuccessMsg(String msg) {
        return new ServerResponse(ResponseCode.SUCCESS.getCode(), msg);
    }

    public static <T> ServerResponse<T> createBySuccess(T data) {
        return new ServerResponse(ResponseCode.SUCCESS.getCode(), data);
    }

    public static <T> ServerResponse<T> createBySuccess(String msg, T data) {
        return new ServerResponse(ResponseCode.SUCCESS.getCode(), msg, data);
    }


    public static <T> ServerResponse<T> createByError(String msg, T data) {
        return new ServerResponse(ResponseCode.ERROR.getCode(), msg, data);
    }

    public static <T> ServerResponse<T> createByError() {
        return new ServerResponse(ResponseCode.ERROR.getCode(), ResponseCode.ERROR.getMsg());
    }

    public static <T> ServerResponse<T> createByErrorMsg(String errormsg) {
        return new ServerResponse(ResponseCode.ERROR.getCode(), errormsg);
    }

    public static <T> ServerResponse<T> createByErrorCodeMsg(int errorcode, String errormsg) {
        return new ServerResponse(errorcode, errormsg);
    }
}
