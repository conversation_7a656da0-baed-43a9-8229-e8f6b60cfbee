package com.nq.controller.admin;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dto.common.CommonPage;
import com.nq.pojo.MerchantPay;
import com.nq.service.MerchantPayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

@RestController
@RequestMapping({"/admin/merchantPay/"})
@Api(tags = {"后台-支付商户管理"})
public class AdminMerchantPayController {

    @Resource
    private MerchantPayService merchantPayService;

    @PostMapping({"list.do"})
    @ApiOperation(value = "支付商户列表", notes = "支付商户列表")
    public ServerResponse<PageInfo<MerchantPay>> list(@RequestBody Map<String, Object> params) {
        try {
            // 打印接收到的参数，用于调试
            System.out.println("查询支付商户参数：" + params);

            // 创建支付商户对象
            MerchantPay merchantPay = new MerchantPay();
            // 设置查询条件
            if (params.containsKey("name")) {
                merchantPay.setName((String) params.get("name"));
            }

            // 创建分页对象
            CommonPage commonPage = new CommonPage();
            if (params.containsKey("pageNum")) {
                commonPage.setPageNum((Integer) params.get("pageNum"));
            }
            if (params.containsKey("pageSize")) {
                commonPage.setPageSize((Integer) params.get("pageSize"));
            }

            // 执行查询
            PageInfo<MerchantPay> page = merchantPayService.listByAdmin(merchantPay, commonPage);
            return ServerResponse.createBySuccess(page);
        } catch (Exception e) {
            e.printStackTrace();
            return ServerResponse.createByErrorMsg("查询支付商户列表失败：" + e.getMessage());
        }
    }

    // 查询用户信息是否存在
    @PostMapping({"detail.do"})
    public ServerResponse<MerchantPay> detail(Integer id) {
        MerchantPay merchantPay = merchantPayService.getById(id);
        return ServerResponse.createBySuccess(merchantPay);
    }

    // 删除支付商户
    @PostMapping({"delete.do"})
    @ApiOperation(value = "删除支付商户", notes = "删除支付商户")
    public ServerResponse<String> delete(@RequestBody Map<String, Integer> params) {
        Integer id = params.get("id");
        // 打印接收到的参数，用于调试
        System.out.println("删除支付商户ID：" + id);
        // 验证必填字段
        if (id == null) {
            return ServerResponse.createByErrorMsg("支付商户ID不能为空");
        }
        // 执行删除操作
        boolean success = merchantPayService.removeById(id);
        if (success) {
            return ServerResponse.createBySuccessMsg("删除支付商户成功");
        } else {
            return ServerResponse.createByErrorMsg("删除支付商户失败");
        }

    }

    // 更新支付商户
    @PostMapping({"update.do"})
    @ApiOperation(value = "更新支付商户", notes = "更新支付商户")
    public ServerResponse<String> update(@RequestBody MerchantPay merchantPay) {
        // 打印接收到的参数，用于调试
        System.out.println("更新支付商户参数：" + merchantPay);

        // 验证必填字段
        if (merchantPay.getId() == null) {
            return ServerResponse.createByErrorMsg("支付商户ID不能为空");
        }

        // 设置更新时间
        merchantPay.setUpdateTime(new Date());

        boolean success = merchantPayService.updateById(merchantPay);
        if (success) {
            return ServerResponse.createBySuccessMsg("更新支付商户成功");
        } else {
            return ServerResponse.createByErrorMsg("更新支付商户失败");
        }
    }

    // 新增支付商户
    @PostMapping({"add.do"})
    @ApiOperation(value = "新增支付商户", notes = "新增支付商户")
    public ServerResponse<String> add(@RequestBody MerchantPay merchantPay) {
        // 打印接收到的参数，用于调试
        System.out.println("接收到的支付商户参数：" + merchantPay);

        boolean success = merchantPayService.save(merchantPay);
        if (success) {
            return ServerResponse.createBySuccessMsg("新增支付商户成功");
        } else {
            return ServerResponse.createByErrorMsg("新增支付商户失败");
        }

    }
}
