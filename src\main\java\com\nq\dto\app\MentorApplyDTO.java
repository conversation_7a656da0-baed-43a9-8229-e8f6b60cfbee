package com.nq.dto.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "导师申请请求对象")
public class MentorApplyDTO {
    
    @ApiModelProperty(value = "姓名", example = "张三", required = true)
    private String name;
    
    @ApiModelProperty(value = "年龄", example = "35", required = true)
    private Integer age;
    
    @ApiModelProperty(value = "投资年限", example = "5", required = true)
    private Integer investYears;
    
    @ApiModelProperty(value = "收益比例", example = "20.5", required = true)

    private BigDecimal salaryRate;
    @ApiModelProperty(value = "公司名称", example = "广东公司", required = true)
    private String companyName;

    @ApiModelProperty(value = "担保资金(元)", example = "100000", required = true)
    private BigDecimal guaranteeFund;
    
    @ApiModelProperty(value = "投资简介", example = "专注价值投资，擅长金融、科技等领域")
    private String introduction;
} 