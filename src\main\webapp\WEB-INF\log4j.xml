<?xml version="1.0" encoding="UTF-8" ?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">

<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/">
  <!--
    - This is a sample configuration for log4j.
    - It simply just logs everything into a single log file.
    - Note, that you can use properties for value substitution.
    -->
  <appender name="CORE" class="org.apache.log4j.FileAppender">
    <param name="File"   value="${org.apache.cocoon.work.directory}/cocoon-logs/log4j.log" />
    <param name="Append" value="false" />
    <layout class="org.apache.log4j.PatternLayout">
      <param name="ConversionPattern" value="%d %-5p %t %c - %m%n"/>
    </layout>
  </appender>

  <root>
    <priority value="${org.apache.cocoon.log4j.loglevel}"/>
    <appender-ref ref="CORE"/>
  </root>
</log4j:configuration>
