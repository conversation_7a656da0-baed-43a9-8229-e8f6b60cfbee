package com.nq.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.pojo.UserPosition;
import com.nq.vo.position.*;

import java.math.BigDecimal;

public interface IUserPositionService extends IService<UserPosition> {
    void buy(Integer stockId, Integer buyNum, BigDecimal buyPrice, Integer orderType) throws Exception;

    void addPosition(String positionSn, Integer buyNum, BigDecimal buyPrice, Integer orderType) throws Exception;

    void sell(String position, int num) throws Exception;
     //positionId, state, lockMsg
    void lock(Integer positionId, Integer state, String lockMsg);

    void del(Integer paramInteger);
   //userId stockCode, stockSpell, state, pageNum, pageSize
    PageInfo<UserPositionVO> findPositionByCodeByState(Integer userId,String stockCode, String stockSpell, Integer state,  int pageNum, int pageSize);


    int CountPositionNum(Integer paramInteger);

    BigDecimal CountPositionProfitAndLose();

    BigDecimal CountPositionAllProfitAndLose();
     //userId, stockCode, buyPrice, buyTime, buyNum, buyType, profitTarget, stopTarget
    void create(Integer userId, String stockCode, BigDecimal now_price,  Integer
                        actualBuyNum,Integer positionId,String buyTime);

    int deleteByUserId(Integer userId);


    void revocation(Integer id);


    PositionProfitVO getPositionProfitVO(UserPosition position);


    void newStockToPosition(Integer id);


    void buyVipQc(String stockCode, Integer buyNum, Integer buyType, Integer lever, BigDecimal profitTarget, BigDecimal stopTarget) throws Exception;


}
