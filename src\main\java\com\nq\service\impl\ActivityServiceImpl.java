package com.nq.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.nq.dao.ActivityMapper;
import com.nq.dto.common.CommonPage;
import com.nq.pojo.Activity;
import com.nq.pojo.User;
import com.nq.service.ActivityService;
import com.nq.utils.PageUtil;
import com.nq.vo.app.AppActivityVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class ActivityServiceImpl extends ServiceImpl<ActivityMapper, Activity> implements ActivityService {

    @Resource
    private UserServiceImpl userService;

    @Override
    public AppActivityVO queryActivityTeam(User user) {
        List<Activity> activities = this
                .list(new LambdaQueryWrapper<Activity>().eq(Activity::getType, 1).eq(Activity::getStatus, 1));
        User newUser = userService.getById(user.getId());
        AppActivityVO appActivityVO = new AppActivityVO();
        appActivityVO.setActivities(activities);
        appActivityVO.setMaxLevel(newUser.getMaxLevel());
        return appActivityVO;
    }

    @Override
    public AppActivityVO queryActivityInvite(User user) {
        List<Activity> activities = this
                .list(new LambdaQueryWrapper<Activity>().eq(Activity::getType, 2).eq(Activity::getStatus, 1));
        // 查询用户今天邀请人数的数量
        Date date = new Date();
        DateTime beginTime = DateUtil.beginOfDay(date);
        DateTime endTime = DateUtil.endOfDay(date);
        long count = userService.count(new LambdaQueryWrapper<User>().ge(User::getFirstFollowStopTime, beginTime)
                .le(User::getRegTime, endTime));
        AppActivityVO appActivityVO = new AppActivityVO();
        appActivityVO.setActivities(activities);
        appActivityVO.setTodayInviteNum(count);
        return appActivityVO;
    }

    @Override
    public PageInfo<Activity> listByAdmin(Activity activity, CommonPage commonPage) {
        Integer pageNum = commonPage.getPageNum();
        Integer pageSize = commonPage.getPageSize();
        PageUtil.startPage(pageNum, pageSize);
        LambdaQueryWrapper<Activity> q = new LambdaQueryWrapper<>();
        this.buildQuery(q, activity);
        List<Activity> activities = this.list(q);
        return new PageInfo<>(activities);
    }

    private void buildQuery(LambdaQueryWrapper<Activity> q, Activity activity) {
        // 添加名称查询条件
        q.like(StrUtil.isNotEmpty(activity.getName()), Activity::getName, activity.getName());

        // 添加类型查询条件
        q.eq(activity.getType() != null, Activity::getType, activity.getType());

        // 添加排序条件
        q.orderByAsc(Activity::getSort);
    }

    @Override
    public Activity detail(Integer id) {
        return this.getById(id);
    }

    @Override
    public boolean deleteActivity(Integer id) {
        // 使用 baseMapper 直接执行 SQL 删除语句，确保物理删除
        return baseMapper.deleteById(id) > 0;
    }
}
