package com.nq.service.impl;

import cn.hutool.core.util.StrUtil;
import com.nq.common.ServerResponse;
import com.nq.constant.OrderConstant;
import com.nq.dao.UserMapper;
import com.nq.enums.OrderTypeEnum;
import com.nq.enums.TypeEnum;
import com.nq.function.AmountConsumer;
import com.nq.manage.UserAmountChangeManage;
import com.nq.pojo.MerchantPay;
import com.nq.pojo.User;
import com.nq.pojo.UserRecharge;
import com.nq.service.IPayService;
import com.nq.service.IUserRechargeService;
import com.nq.service.IUserService;
import com.nq.utils.Md5Utils;
import com.nq.utils.RedisLockKey;
import com.nq.utils.RedisLockUtil;
import com.nq.utils.SnowIdUtil;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Date;
import java.util.concurrent.TimeUnit;


@Service("iPayService")
public class PayServiceImpl implements IPayService {
    private static final Logger log = LoggerFactory.getLogger(PayServiceImpl.class);

    @Resource
    private IUserService iUserService;

    @Resource
    private IUserRechargeService userRechargeService;

    @Resource
    private UserMapper userMapper;

    @Resource
    private RedisLockUtil redisLockUtil;

    @Resource
    private UserAmountChangeManage userAmountChangeManage;

    @Override
    public ServerResponse<String> recharge(MerchantPay merchantPay, String payAmt) {
        User user = this.iUserService.getCurrentUser();
        if (user == null) {
            return ServerResponse.createBySuccessMsg("請先登錄");
        }
        UserRecharge userRecharge = new UserRecharge();
        userRecharge.setUserId(user.getId());
        userRecharge.setNickName(user.getRealName());
        String orderNo = SnowIdUtil.getId(OrderConstant.rechargeOrder);
        userRecharge.setOrderSn(orderNo);
//        userRecharge.setPayChannel(payType);
        userRecharge.setPayId(merchantPay.getId());
        userRecharge.setPayAmt(new BigDecimal(payAmt));
        userRecharge.setOrderStatus(0);
        userRecharge.setAddTime(new Date());
        Boolean flag = userRechargeService.save(userRecharge);
        if (flag) {
            log.info(StrUtil.format("{}支付，创建支付订单成功！", merchantPay.getName()));
        } else {
            log.error(StrUtil.format("{}支付，创建支付订单失败！", merchantPay.getName()));
            return ServerResponse.createByErrorMsg("支付异常,请通知客服 !");
        }


//        FlyPayVO flyPayVO = new FlyPayVO();
//        flyPayVO.setOrderno(orderNo);
//        flyPayVO.setOrderamount(payAmt);
//        flyPayVO.setPaytype(payType);
//        flyPayVO.setOrdercurrency(currency);
//        String sign = "";
//        String tempStr = flyPayVO.getMerchantid() + orderNo + flyPayVO.getOrderamount() + flyPayVO.getServerbackurl() + flyPayVO.getCallbackurl() + PropertiesUtil.getProperty("fly.pay.token");
//        sign = DigestUtils.md5Hex(tempStr);
//        flyPayVO.setSign(sign);

        //获取支付方充值链接返回
        //return ServerResponse.createBySuccess(flyPayVO);

        //先直接充值成功
        doSuccess(orderNo, payAmt);
        return ServerResponse.createBySuccess();

    }

    @Override
    public ServerResponse<String> rechargeNotify(HttpServletRequest request) {
        //我方订单号
        String orderid = request.getParameter("orderid");
        String lockKey = StrUtil.format(RedisLockKey.notifyLock, orderid);
        return redisLockUtil.tryLock(lockKey, 10, TimeUnit.SECONDS, () -> doNotify(request, orderid));
    }

    @Override
    public ServerResponse<String> withdraw(MerchantPay merchantPay, String payAmt) {
        //创建提现订单
        return null;
    }

    @Override
    public ServerResponse<String> withdrawNotify(HttpServletRequest request) {
        //提现回调
        return null;
    }

    @SneakyThrows
    private ServerResponse<String> doNotify(HttpServletRequest request, String orderid) {
        String memberid = request.getParameter("memberid");
        String amount = request.getParameter("amount");
        String datetime = request.getParameter("datetime");
        String returncode = request.getParameter("returncode");
        String transaction_id = request.getParameter("transaction_id");
        String attach = request.getParameter("attach");
        String sign = request.getParameter("sign");

        log.info("支付通知的 orderid = {}", orderid);
        log.info("支付通知的 transaction_id = {}", transaction_id);
        log.info("支付通知的 amount = {}", amount);
        log.info("支付通知的 returncode = {}", returncode);

        String keyValue = "vd7omkkexkt7fvl6wm2jl9yan3g79y6i";
        String SignTemp = "amount=" + amount + "&datetime=" + datetime + "&memberid=" + memberid + "&orderid=" + orderid + "&returncode=" + returncode + "&transaction_id=" + transaction_id + "&key=" + keyValue + "";
        String md5sign = Md5Utils.getMD5(SignTemp);
        log.info("支付通知的sign = {}", sign);
        log.info("自己加密的sign = {}", md5sign);
        if (!sign.equals(md5sign)) {
            return ServerResponse.createByErrorMsg("签名校验失败");
        }
        //校验状态码,和支付状态
        if (returncode.equals("00")) {
            return doSuccess(orderid, amount);
        } else {
            return ServerResponse.createByErrorMsg("订单状态不对");
        }
    }


    private ServerResponse<String> doSuccess(String orderId, String amountStr) {

        BigDecimal amount = new BigDecimal(amountStr);
        UserRecharge userRecharge = userRechargeService.findUserRechargeByOrderSn(orderId);
        if (userRecharge == null) {
            return ServerResponse.createByErrorMsg("后台通知，查不到订单");
        }
        User user = this.userMapper.selectById(userRecharge.getUserId());
        if (user == null) {
            return ServerResponse.createByErrorMsg("后台通知，查不到用户");
        }
        if (userRecharge.getPayAmt().compareTo(amount) != 0) {
            return ServerResponse.createByErrorMsg("处理失败，后台通知金额和订单金额不一致");
        }
        //查询该订单是不是处理中阶段
        if (userRecharge.getOrderStatus() != 0) {
            return ServerResponse.createByErrorMsg("处理失败，当前订单已处理");
        }

        //修改充值订单
        AmountConsumer amountConsumer = (oldUser, newUser) -> {
            userRecharge.setPayTime(new Date());
            userRecharge.setOrderStatus(1);
            userRechargeService.updateById(userRecharge);
        };

        userAmountChangeManage.changeBalance(user.getId(), amount, orderId, OrderTypeEnum.RECHARGE, TypeEnum.RECHARGE, "", "", amountConsumer);
        return ServerResponse.createBySuccess();
    }

}