package com.nq.constant;

/**
 * 返回状态码
 *
 * <AUTHOR>
 */
public interface OrderConstant
{
    /**
     * 挂单
     */
    public static final String userPositionOrder = "UP";
    public static final String userPendingOrder = "UPIng";


    public static final String rechargeOrder = "RO";

    public static final String withdrawalOrder = "WO";

    public static final String emailToken = "ET";

    public static final String StockSubscribe = "SSB";

    public static final String agentCode = "AC";

    public static final String ActivityReceiveCode = "AR";



    public static final String FollowOrder = "FO";


    public static final String followDetail = "FD";

    public static final String followTrade = "TD";
    //证转银
    public static final String transferToBank = "TBB";
    //银转证
    public static final String transferFromBank = "TBF";



}
