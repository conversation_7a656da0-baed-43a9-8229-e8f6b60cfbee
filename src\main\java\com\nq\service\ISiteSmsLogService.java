package com.nq.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.SiteSetting;
import com.nq.pojo.SiteSmsLog;

import javax.servlet.http.HttpServletRequest;

public interface ISiteSmsLogService extends IService<SiteSmsLog> {
  PageInfo<SiteSmsLog> smsList(String paramString, int paramInt1, int paramInt2);

  void addData(SiteSmsLog siteSmsLog);

  void del(Integer id, HttpServletRequest request);

}
