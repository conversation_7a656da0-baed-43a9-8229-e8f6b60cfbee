package com.nq.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.nq.common.ServerResponse;
import com.nq.pojo.Stock;
import com.nq.pojo.UserBank;
import com.nq.vo.user.UserBankInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import javax.servlet.http.HttpServletRequest;

@Api(tags = "用户银行卡服务接口")
public interface IUserBankService    extends IService<UserBank> {
    
    @ApiOperation(value = "根据用户ID查询银行卡", notes = "通过用户ID查询对应的银行卡信息")
    UserBank findUserBankByUserId(@ApiParam(value = "用户ID", required = true) Integer userId);
    
    @ApiOperation(value = "添加银行卡", notes = "用户添加新的银行卡信息")
    void addBank(@ApiParam(value = "银行卡信息", required = true) UserBank bank,
                                   @ApiParam(value = "HTTP请求对象", required = true) HttpServletRequest request);
    
    @ApiOperation(value = "更新银行卡信息", notes = "更新用户已绑定的银行卡信息")
     void updateBank(@ApiParam(value = "银行卡信息", required = true) UserBank bank,
                                      @ApiParam(value = "HTTP请求对象", required = true) HttpServletRequest request);
    
    @ApiOperation(value = "获取银行卡信息", notes = "获取当前用户的银行卡信息")
    UserBankInfoVO getBankInfo(@ApiParam(value = "HTTP请求对象", required = true) HttpServletRequest request);
    
    @ApiOperation(value = "管理员更新银行卡", notes = "管理员更新用户的银行卡信息")
    void updateBankByAdmin(@ApiParam(value = "银行卡信息", required = true) UserBank bank);
    
    @ApiOperation(value = "获取银行卡", notes = "根据ID获取银行卡信息")
    UserBank getBank(@ApiParam(value = "银行卡ID", required = true) Integer id);
}
