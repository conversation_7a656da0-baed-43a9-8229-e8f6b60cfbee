package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@TableName(value ="user_position")
@Data
@ApiModel(description = "用户持仓信息")
@AllArgsConstructor
@NoArgsConstructor
public class UserPosition extends BaseEntity implements Serializable {
    @TableId(type = IdType.AUTO,value = "id")
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "持仓编号", example = "POS202401010001")
    private String positionSn;

    @ApiModelProperty(value = "用户ID", example = "1")
    private Integer userId;

    @ApiModelProperty(value = "用户昵称", example = "张三")
    private String nickName;



    @ApiModelProperty(value = "股票名称", example = "贵州茅台")
    private String stockName;

    @ApiModelProperty(value = "股票代码", example = "600519")
    private String stockCode;

    @ApiModelProperty(value = "股票GID", example = "SH600519")
    private String stockGid;

    @ApiModelProperty(value = "股票拼音", example = "GZMT")
    private String stockSpell;

    @ApiModelProperty(value = "买入订单ID", example = "BUY202401010001")
    private String buyOrderId;

    @ApiModelProperty(value = "买入时间", example = "2024-01-01 09:30:00")
    private Date buyOrderTime;

    @ApiModelProperty(value = "买入价格", example = "1000.00")
    private BigDecimal buyOrderPrice;

    @ApiModelProperty(value = "卖出订单ID", example = "SELL202401010001")
    private String sellOrderId;

    @ApiModelProperty(value = "卖出时间", example = "2024-01-01 10:30:00")
    private Date sellOrderTime;

    @ApiModelProperty(value = "卖出价格", example = "1050.00")
    private BigDecimal sellOrderPrice;

    @ApiModelProperty(value = "止盈价格", example = "1100.00")
    private BigDecimal profitTargetPrice;

    @ApiModelProperty(value = "止损价格", example = "950.00")
    private BigDecimal stopTargetPrice;

    @ApiModelProperty(value = "交易方向：1、买涨，2、买跌", example = "1")
    private String orderDirection;

    @ApiModelProperty(value = "交易数量", example = "100")
    private Integer orderNum;

    @ApiModelProperty(value = "交易总金额", example = "100000.00")
    private BigDecimal orderTotalPrice;

    @ApiModelProperty(value = "手续费", example = "100.00")
    private BigDecimal orderFee;

    @ApiModelProperty(value = "盈亏金额", example = "5000.00")
    private BigDecimal profitAndLose;

    @ApiModelProperty(value = "总盈亏金额", example = "5000.00")
    private BigDecimal allProfitAndLose;

    @ApiModelProperty(value = "是否锁定：0、未锁定，1、已锁定", example = "0")
    private Integer isLock;

    @ApiModelProperty(value = "锁定原因", example = "系统风控")
    private String lockMsg;

    @ApiModelProperty(value = "股票板块", example = "白酒")
    private String stockPlate;

    @ApiModelProperty(value = "补仓关联的下单ID", example = "1")
    private Integer positionAppendId;

    private static final long serialVersionUID = 1L;
}

