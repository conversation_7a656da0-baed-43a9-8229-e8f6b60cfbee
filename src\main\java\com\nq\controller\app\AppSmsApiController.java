package com.nq.controller.app;


import com.nq.common.ServerResponse;
import com.nq.pojo.SiteSmsLog;
import com.nq.service.ISiteSmsLogService;
import com.nq.utils.DateTimeUtil;
import com.nq.utils.smsUtil.SmsUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;

@Api(tags = "APP-短信发送")
@RestController
@RequestMapping({"/api/sms/"})
public class AppSmsApiController {
    private static final Logger log = LoggerFactory.getLogger(AppSmsApiController.class);

    @Resource
    private ISiteSmsLogService iSiteSmsLogService;

    //注册用户 短信发送
    @GetMapping({"sendRegSms.do"})
    @ApiOperation("注册用户 短信发送")
    public ServerResponse<String> sendRegSms(String phoneNum) {
        if (StringUtils.isBlank(phoneNum)) {
            return ServerResponse.createByErrorMsg("发送失败，手机号不能为空");
        }
        SmsUtil smsUtil = new SmsUtil();
        String code = smsUtil.sendSMS(phoneNum);
        if (!StringUtils.isEmpty(code)) {
            SiteSmsLog siteSmsLog = new SiteSmsLog();
            siteSmsLog.setSmsPhone(phoneNum);
            siteSmsLog.setSmsTitle("注册验证码");
            siteSmsLog.setSmsCnt(code);
            siteSmsLog.setSmsStatus(Integer.valueOf(0));
            siteSmsLog.setSmsTemplate("字段无用");
            siteSmsLog.setAddTime(DateTimeUtil.getCurrentDate());
            iSiteSmsLogService.addData(siteSmsLog);
            return ServerResponse.createBySuccessMsg("发送成功");
        } else {
            return ServerResponse.createByErrorMsg("短信发送失败，请重试");
        }
    }


    //找回密码 短信发送
    @ApiOperation("找回密码 短信发送")
    @GetMapping({"sendForgetSms.do"})
    public ServerResponse<String> sendForgetSms(String phoneNum) {
        if (StringUtils.isBlank(phoneNum)) {
            return ServerResponse.createByErrorMsg("发送失败，手机号不能为空");
        }
        SmsUtil smsUtil = new SmsUtil();
        String code = smsUtil.sendForgetSms(phoneNum);
        if (!StringUtils.isEmpty(code)) {
            SiteSmsLog siteSmsLog = new SiteSmsLog();
            siteSmsLog.setSmsPhone(phoneNum);
            siteSmsLog.setSmsTitle("找回密码验证码");
            siteSmsLog.setSmsCnt(code);
            siteSmsLog.setSmsStatus(Integer.valueOf(0));
            siteSmsLog.setSmsTemplate("字段无用");
            siteSmsLog.setAddTime(DateTimeUtil.getCurrentDate());
            iSiteSmsLogService.addData(siteSmsLog);
            return ServerResponse.createBySuccessMsg("发送成功");
        } else {
            return ServerResponse.createByErrorMsg("短信发送失败，请重试");
        }
    }


}
