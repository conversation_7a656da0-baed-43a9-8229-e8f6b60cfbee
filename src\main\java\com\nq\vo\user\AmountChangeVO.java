package com.nq.vo.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(description = "用户账变VO")
public class AmountChangeVO {

    @ApiModelProperty(value = "商户账变id", example = "1")
    private Long id;



    @ApiModelProperty(value = "用户ID", example = "1")
    private Integer userId;

    @ApiModelProperty(value = "用户名称", example = "张三")
    private String userName;

    @ApiModelProperty(value = "关联的订单号", example = "ORDER202401010001")
    private String orderNo;

    @ApiModelProperty(value = "金额类型(1 平台账户, 2 证券账户)", example = "1")
    private Integer amountType;

    @ApiModelProperty(value = "订单类型(1.充值 2.提现 3.股票 4.投顾 5.账户转账 6.团队工资 7.奖金)", example = "1")
    private Integer orderType;

    @ApiModelProperty(value = "订单类型描述", example = "充值")
    private String orderTypeDesc;

    @ApiModelProperty(value = "收支类型(1.收入 2.支出)", example = "1")
    private Integer accountType;

    @ApiModelProperty(value = "收支类型描述", example = "收入")
    private String accountTypeDesc;

    @ApiModelProperty(value = "帐变类型(1.充值 2.提现 3.买入股票 4.卖出股票 5.股票退回 6.申请投顾 7.投顾驳回 8.投顾退回 9.投顾收益 10.投顾追加 11.投顾追加退回 12.证转银 13.银转证 14.团队工资 15.奖金)", example = "1")
    private Integer type;

    @ApiModelProperty(value = "帐变类型描述", example = "充值")
    private String typeDesc;

    @ApiModelProperty(value = "变更金额", example = "1000.00")
    private BigDecimal amount;

    @ApiModelProperty(value = "变更前金额", example = "0.00")
    private BigDecimal oldAmount;

    @ApiModelProperty(value = "变更后金额", example = "1000.00")
    private BigDecimal newAmount;

    @ApiModelProperty(value = "备注", example = "用户充值")
    private String remark;

    @ApiModelProperty(value = "操作人", example = "admin")
    private String operator;

    @ApiModelProperty(value = "变更时间", example = "2024-01-01 12:00:00")
    private Date createTime;
} 