package com.nq.controller.app;

import com.nq.common.ServerResponse;
import com.nq.dto.app.MentorApplyDTO;
import com.nq.dto.app.MentorQueryDTO;
import com.nq.pojo.User;
import com.nq.service.IUserService;
import com.nq.service.MentorApplyService;
import com.nq.service.MentorService;
import com.nq.vo.mentor.MentorVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
//完事
@Api(tags = "APP-导师相关接口")
@RestController
@RequestMapping("/api/mentor")
public class AppMentorApiController {
    
    @Resource
    private MentorApplyService mentorApplyService;
    
    @Resource
    private MentorService mentorService;
    
    @Resource
    private IUserService iUserService;
    

    
    @ApiOperation("申请成为导师")
    @PostMapping("/apply.do")
    public ServerResponse<Boolean> apply(@RequestBody MentorApplyDTO dto, HttpServletRequest request) {
        // 获取当前用户
        User user = iUserService.getCurrentUser();
        if (user == null) {
            return ServerResponse.createBySuccessMsg("用户未登录");
        }
        // 调用service处理申请
        boolean success = mentorApplyService.createMentorApply(dto, user.getId());
        return success ? ServerResponse.createBySuccess(true) : ServerResponse.createByError();
    }
    
    @ApiOperation("搜索导师")
    @PostMapping("/search.do")
    public ServerResponse<MentorVO> search(@RequestBody MentorQueryDTO dto) {
        MentorVO mentorVO = mentorService.searchMentors(dto.getKeyword());
        return ServerResponse.createBySuccess(mentorVO);
    }
}

