package com.nq.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nq.pojo.SiteNews;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 新闻资讯
 * <AUTHOR>
 * @date 2020/08/05
 */
@Mapper
public interface SiteNewsMapper extends BaseMapper<SiteNews> {


    /**
     * [查询] 根据主键 id 查询
     * <AUTHOR>
     * @date 2020/08/05
     **/
    SiteNews load(int id);


    /**
     * [查询] 分页查询 count
     * <AUTHOR>
     * @date 2020/08/05
     **/
    int pageListCount(int offset,int pagesize);

    /**
     * [查询]根据来源id查询新闻数
     * <AUTHOR>
     * @date 2020/08/05
     **/
    int getNewsBySourceIdCount(@Param("sourceId") String sourceId);

    /**
     * [修改]修改新闻浏览量
     * <AUTHOR>
     * @date 2020/08/05
     **/
    int updateViews(@Param("id") Integer id);

    /**
     * [查询] top最新新闻资讯
     * <AUTHOR>
     * @date 2020/08/05
     **/
    List<SiteNews> getTopNewsList(@Param("pageSize") int pageSize);

}
