package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("follow")
@ApiModel(description = "跟单申请")
public class FollowApplyStatusDto  extends BaseEntity {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    
    @ApiModelProperty(value = "状态", example = "1")
    private Integer status;
    

} 