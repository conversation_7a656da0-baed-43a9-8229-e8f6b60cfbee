<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nq.dao.ScheduleJobDao">

	<!-- 批量更新状态 -->
	<update id="updateBatch">
		update schedule_job set status = #{status} where job_id in
		<foreach item="jobId" collection="list"  open="(" separator="," close=")">
			#{jobId}
		</foreach>
	</update>

	<!-- 批量删除 -->
	<delete id="deleteBatch">
		delete from schedule_job where job_id in
		<foreach item="jobId" collection="array" open="(" separator="," close=")">
			#{jobId}
		</foreach>
	</delete>

</mapper>
