package com.nq.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nq.pojo.UserStockSubscribe;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 新股申购
 * <AUTHOR>
 * @date 2020/09/11
 */
@Mapper
public interface UserStockSubscribeMapper extends BaseMapper<UserStockSubscribe>   {


    /**
     * [查询] 根据主键 id 查询
     * <AUTHOR>
     * @date 2020/09/11
     **/
    UserStockSubscribe load(int id);

    /**
     * [查询] 分页查询
     * <AUTHOR>
     * @date 2020/09/11
     **/
    List<UserStockSubscribe> pageList(@Param("pageNum") int pageNum, @Param("pageSize") int pageSize, @Param("keyword") String keyword,@Param("status") Integer status);

    /**
     * [查询] 分页查询 count
     * <AUTHOR>
     * @date 2020/09/11
     **/
    int pageListCount(int offset, int pagesize);

    /**
     * [查询] 查询用户最新新股申购数据
     * <AUTHOR>
     * @date 2020/09/11
     **/
    List<UserStockSubscribe> getOneSubscribeByUserId(String phone);


}

