package com.nq.controller.admin;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.Mentor;
import com.nq.pojo.User;
import com.nq.service.IUserService;
import com.nq.service.MentorService;
import com.nq.vo.mentor.MentorVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;

@Api(tags = "后台-导师管理")
@RestController
@RequestMapping("/admin/mentor")
public class AdminMentorController {

    @Resource
    private MentorService mentorService;
    @Resource
    private IUserService userService;

    @ApiOperation("分页查询导师列表")
    @GetMapping("/list.do")
    public ServerResponse<PageInfo<MentorVO>> list(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页数量") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("导师姓名") @RequestParam(required = false) String mentorName,
            @ApiParam("导师账户") @RequestParam(required = false) String mentorAccount,
            @ApiParam("导师手机号") @RequestParam(required = false) String mentorPhone,
            @ApiParam("状态") @RequestParam(required = false) Integer status
    ) {
        PageInfo<MentorVO> voPage = mentorService.pageWithUser(pageNum, pageSize, mentorName, mentorAccount, mentorPhone, status);
        return ServerResponse.createBySuccess(voPage);
    }

    @ApiOperation("添加导师")
    @PostMapping("/add.do")
    public ServerResponse<String> add(@RequestBody Mentor mentor) {
        mentor.setCreateTime(new Date());
        mentor.setUpdateTime(new Date());
        //查询用户
        Integer userId = mentor.getUserId();
        User user = userService.getById(userId);
        mentor.setMentorPhone(user.getPhone());
        mentor.setMentorAccount(user.getNickName());
        mentorService.save(mentor);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation("修改导师信息")
    @PostMapping("/update.do")
    public ServerResponse<String> update(@RequestBody Mentor mentor) {
        mentor.setUpdateTime(new Date());
        mentorService.updateById(mentor);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation("删除导师")
    @GetMapping("/delete.do")
    public ServerResponse<String> delete(@RequestParam(defaultValue = "0") Integer id) {
        mentorService.removeById(id);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation("修改导师状态")
    @PostMapping("/updateStatus.do")
    public ServerResponse<String> updateStatus(@RequestBody Mentor mentor) {
        mentorService.updateById(mentor);
        return ServerResponse.createBySuccess();
    }
}