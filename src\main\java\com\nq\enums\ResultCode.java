package com.nq.enums;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;

/**
 * 响应码枚举
 *
 * <AUTHOR>
 * @date 2020/10/19
 **/
@Getter
@AllArgsConstructor
public enum ResultCode {
    /**
     * 成功
     */
    SUCCESS(200, "成功"),
    /**
     * 未登录
     */
    NO_LOGIN(400, "未登录"),
    /**
     * 系统繁忙，请稍后再试
     */
    PROGRAM_ERROR(500, "系统繁忙，请稍后再试"),

    /**
     * 余额不足
     */

    INSUFFICIENT_ERROR(402, "余额不足"),
    /**
     * 实名未认证
     */
    REAL_NAME_NOT_AUTHENTICATION(403, "实名未认证"),

    /**
     * 未登录,请先登录
     */
    ERR_1(-1, "无效的token"),

    /**
     * 登录超时
     */
    ERR_2(-2, "当前设备登录信息已失效,请重新登录 !"),
    ERR_3(-3, "当前设备登录信息已过期,请重新登录 !"),
    ERR_4(-4, "当前设备已被顶下线 !"),
    ERR_5(-5, "当前设备已被踢下线 !");



    private static Map<Integer, ResultCode> codeEnumMap;

    static {
        codeEnumMap = Maps.newHashMap();
        for (ResultCode upPlatformEnum : values()) {
            codeEnumMap.put(upPlatformEnum.getCode(), upPlatformEnum);
        }
    }
    public static ResultCode getCodeEnum(Integer code) {
        return codeEnumMap.get(code);
    }

    private final int code;
    private final String msg;


}

