package com.nq.utils;

import org.hashids.Hashids;

public class InviteCodeUtil {
    private static final String SALT = "your-very-secret-salt";
    private static final int MIN_LENGTH = 6;
    private static final Hashids hashids = new Hashids(SALT, MIN_LENGTH);

    // 生成邀请码
    public static String generateInviteCode(long userId) {
        return hashids.encode(userId);
    }

    // 解析邀请码
    public static long decodeInviteCode(String code) {
        long[] numbers = hashids.decode(code);
        return numbers.length > 0 ? numbers[0] : -1;
    }

    public static void main(String[] args) {
        System.out.println(generateInviteCode(1));
    }
}