<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nq.dao.UserPendingOrderMapper">

    <resultMap id="BaseResultMap" type="com.nq.pojo.UserPendingOrder">
            <id column="id" property="id" jdbcType="INTEGER"/>
            <result column="user_id" property="userId" jdbcType="INTEGER"/>
            <result column="nick_name" property="nickName" jdbcType="VARCHAR"/>
            <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
            <result column="stock_gid" property="stockGid" jdbcType="VARCHAR"/>
            <result column="stock_code" property="stockCode" jdbcType="VARCHAR"/>
            <result column="stock_name" property="stockName" jdbcType="VARCHAR"/>
            <result column="buy_num" property="buyNum" jdbcType="INTEGER"/>
            <result column="buy_type" property="buyType" jdbcType="INTEGER"/>
            <result column="target_price" property="targetPrice" jdbcType="DECIMAL"/>
            <result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
            <result column="position_id" property="positionId" jdbcType="INTEGER"/>
            <result column="status" property="status" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, nick_name, order_no, stock_gid, stock_code, stock_name, buy_num, buy_type, target_price, add_time, position_id, status
    </sql>

    <select id="findAllUserPendingOrder" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_pending_order
    </select>

</mapper>
