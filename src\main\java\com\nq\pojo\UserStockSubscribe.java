package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
* @Description:  新股申购
* @Param:
* @return:
* @Author: tf
* @Date: 2022/10/25
*/
@Data
@ApiModel(description = "新股申购信息")
@AllArgsConstructor
@NoArgsConstructor
public class UserStockSubscribe extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value="id",type= IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;
    /**
    *訂單編號
    */
    @ApiModelProperty(value = "订单编号", example = "SUB202401010001")
    private String orderNo;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户ID", example = "1")
    private Integer userId;

    /**
     * 用户真实姓名
     */
    @ApiModelProperty(value = "用户真实姓名", example = "张三")
    private String realName;

    /**
     * 用户手机号
     */
    @ApiModelProperty(value = "用户手机号", example = "13800138000")
    private String phone;



    /**
     * 申购股票代码
     */
    @ApiModelProperty(value = "申购股票代码", example = "600519")
    private String newCode;
    /**
     * 申购股票名称
     */
    @ApiModelProperty(value = "申购股票名称", example = "贵州茅台")
    private String newName;
    /**
     * 申购股票类型
     */
    @ApiModelProperty(value = "申购股票类型", example = "A股")
    private String newType;
    /**
     * 保证金
     *
     */
    @ApiModelProperty(value = "保证金", example = "10000.00")
    private BigDecimal bond;
    /**
     * 发行价格
     *
     */
    @ApiModelProperty(value = "发行价格", example = "1000.00")
    private BigDecimal buyPrice;
    /**
     * 申购数量
     *
     */
    @ApiModelProperty(value = "申购数量", example = "100")
    private Integer applyNums;
    /**
     * 中签数量
     *
     */
    @ApiModelProperty(value = "中签数量", example = "10")
    private Integer applyNumber;
    @ApiModelProperty(value = "类型", example = "1")
    private Integer type;
    /**
     * 申购状态
     *状态：1、待审核，2、未中签，3、已中签，4、已缴纳 5.已转持仓
     */
    @ApiModelProperty(value = "申购状态：1、待审核，2、未中签，3、已中签，4、已缴纳，5、已转持仓", example = "1")
    private Integer status;
    /**
     * 添加时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "添加时间", example = "2024-01-01 09:30:00")
    private Date addTime;

    /**
     * 提交时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "提交时间", example = "2024-01-01 09:30:00")
    private Date submitTime;

    /**
     * 中签审核时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "中签审核时间", example = "2024-01-01 10:30:00")
    private Date endTime;
    /**
     * 转持仓时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "转持仓时间", example = "2024-01-01 11:30:00")
    private Date fixTime;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", example = "新股申购备注信息")
    private String remarks;



}
