package com.nq.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nq.pojo.StockSubscribe;
import com.nq.pojo.User;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
@Mapper
public interface UserMapper extends BaseMapper<User> {

    User findByPhone(String phone);

    User login(@Param("phone") String phone, @Param("userPwd") String userPwd);




    void addTodayFollowBuy(@Param("user_id") Integer userId);

    void addTodayFollowSell(@Param("user_id") Integer userId, @Param("money") BigDecimal profit);

    List<User> queryAllLowerLevelTask(@Param("user_id")Integer userId);

    List<User> queryAllLowerEfficientTask(@Param("user_id")Integer userId);

    void addLeadEarnAmount(@Param("user_id")Integer userId,@Param("money") BigDecimal amount);

    void addRechargeMoney(@Param("user_id")Integer userId, @Param("money")BigDecimal amount);

    void addWithdrawMoney(@Param("user_id")Integer userId,@Param("money") BigDecimal amount);

    /**
     * 审核通过 第一次结束时间 然后 跟单时间也跟新一下
     * @param userId
     * @param endTime
     */
    void setFirstFollowInfo(@Param("user_id")Integer userId, @Param("date")Date endTime);
}
