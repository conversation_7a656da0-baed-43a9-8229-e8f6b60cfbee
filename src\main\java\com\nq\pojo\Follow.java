package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("follow")
@ApiModel(description = "跟单申请")
public class Follow extends BaseEntity {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;
    
    @ApiModelProperty(value = "跟单单号", example = "FD202403010001")
    private String followNo;
    
    @ApiModelProperty(value = "套餐ID", example = "1")
    private Integer packageId;
    
    @ApiModelProperty(value = "导师ID", example = "1")
    private Integer mentorId;
    
    @ApiModelProperty(value = "用户ID", example = "1")
    private Integer userId;
    
    @ApiModelProperty(value = "申请时间")
    private Date applyTime;
    
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    
    @ApiModelProperty(value = "终止时间")
    private Date stopTime;
    
    @ApiModelProperty(value = "跟单金额", example = "30000.00")
    private BigDecimal amount;
    
    @ApiModelProperty(value = "仓位比例", example = "40.00")
    private BigDecimal positionRate;
    
    @ApiModelProperty(value = "工资比例", example = "0.03")
    private BigDecimal salaryRate;
    
    @ApiModelProperty(value = "跟单最低", example = "1000.00")
    private BigDecimal minAmount;
    
    @ApiModelProperty(value = "跟单最高", example = "50000.00")
    private BigDecimal maxAmount;
    
    @ApiModelProperty(value = "是否追加(0-否，1-是)", example = "0")
    private Integer isAdd;
    
    @ApiModelProperty(value = "追加金额", example = "0.00")
    private BigDecimal addAmount;
    
    @ApiModelProperty(value = "今日日期", example = "2024-03-01")
    private Date today;
    
    @ApiModelProperty(value = "状态(1-待审核，2-跟单中，3-被驳回，4-已撤销，5-已结束，6-已中止)", example = "1")
    private Integer status;
    
    @ApiModelProperty(value = "审核时间")
    private Date auditTime;
    
    @ApiModelProperty(value = "套餐天数", example = "30")
    private Integer packageDays;
    
    @ApiModelProperty(value = "已用套餐天数", example = "0")
    private Integer usedDays;
    
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
} 