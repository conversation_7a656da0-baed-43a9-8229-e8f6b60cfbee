package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("mentor_apply")
@ApiModel(description = "导师申请")
public class MentorApply extends BaseEntity {
    
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "申请ID", example = "1")
    private Integer id;
    
    @ApiModelProperty(value = "用户ID", example = "1001")
    private Integer userId;
    
    @ApiModelProperty(value = "姓名", example = "张三")
    private String name;
    
    @ApiModelProperty(value = "年龄", example = "30")
    private Integer age;
    
    @ApiModelProperty(value = "投资年限", example = "5")
    private Integer investYears;
    
    @ApiModelProperty(value = "收益比例", example = "0.20")
    private BigDecimal salaryRate;
    
    @ApiModelProperty(value = "担保资金(元)", example = "100000.00")
    private BigDecimal guaranteeFund;
    
    @ApiModelProperty(value = "公司名称", example = "某某投资公司")
    private String companyName;
    
    @ApiModelProperty(value = "投资简介", example = "专注于A股市场投资，具有丰富的投资经验和稳定的收益表现。")
    private String introduction;
    
    @ApiModelProperty(value = "申请状态(0-待审核，1-已通过，2-已拒绝)", example = "0")
    private Integer status;
    
    @ApiModelProperty(value = "创建时间", example = "2024-01-01 12:00:00")
    private Date createTime;
    
    @ApiModelProperty(value = "更新时间", example = "2024-01-01 12:00:00")
    private Date updateTime;
    
    @ApiModelProperty(value = "审核时间", example = "2024-01-01 12:00:00")
    private Date auditTime;
    
    @ApiModelProperty(value = "审核备注", example = "资质符合要求，通过审核")
    private String auditRemark;
} 