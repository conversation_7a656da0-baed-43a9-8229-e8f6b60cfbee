package com.nq.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.dao.SiteLoginLogMapper;
import com.nq.pojo.SiteLoginLog;
import com.nq.pojo.User;
import com.nq.service.ISiteLoginLogService;
import com.nq.utils.DateUtils;
import com.nq.utils.ip.IpUtils;
import com.nq.utils.ip.JuheIpApi;
import com.nq.excepton.CustomException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Service("iSiteLoginLogService")
public class SiteLoginLogServiceImpl extends ServiceImpl<SiteLoginLogMapper, SiteLoginLog> implements ISiteLoginLogService {
    private static final Logger log = LoggerFactory.getLogger(SiteLoginLogServiceImpl.class);

    @Resource
    private SiteLoginLogMapper siteLoginLogMapper;

    @Override
    public void saveLog(User user, HttpServletRequest request) {
        if (user == null || request == null) {
            throw new CustomException("参数不能为空");
        }

        SiteLoginLog siteLoginLog = new SiteLoginLog();
        siteLoginLog.setUserId(user.getId());
        siteLoginLog.setUserName(user.getRealName());
        String ips = IpUtils.getIp(request);

        siteLoginLog.setLoginIp(ips);
        String ipAddress = JuheIpApi.ip2Add(ips);
        siteLoginLog.setLoginAddress(ipAddress);
        siteLoginLog.setAddTime(new Date());
        
        int result = this.siteLoginLogMapper.insert(siteLoginLog);
        if (result <= 0) {
            throw new CustomException("保存登录日志失败");
        }
    }

    @Override
    public PageInfo<SiteLoginLog> loginList(Integer userId, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<SiteLoginLog> siteLoginLogs = this.siteLoginLogMapper.loginList(userId);
        return new PageInfo<>(siteLoginLogs);
    }

    @Override
    public int deleteByUserId(Integer userId) {
        if (userId == null) {
            throw new CustomException("用户ID不能为空");
        }
        return this.siteLoginLogMapper.deleteByUserId(userId);
    }

    @Override
    public void del(Integer id, HttpServletRequest request) {
        if (id == null) {
            throw new CustomException("id不能为空");
        }

        int updateCount = this.siteLoginLogMapper.deleteById(id);
        if (updateCount <= 0) {
            throw new CustomException("删除失败");
        }
    }

    @Override
    public Long countActiveUserByDate(Integer type) {
        HashMap<String, Date> todayMap = DateUtils.changeTimeSection(type);
        Date startTime = todayMap.get("startTime");
        Date endTime = todayMap.get("endTime");
        
        // 使用正确的SQL语法进行去重统计
        QueryWrapper<SiteLoginLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("add_time", startTime, endTime);
        queryWrapper.select("DISTINCT user_id");
        
        return this.baseMapper.selectCount(queryWrapper);
    }
}
