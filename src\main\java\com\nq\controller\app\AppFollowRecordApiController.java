package com.nq.controller.app;


import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dto.app.*;
import com.nq.pojo.User;
import com.nq.service.FollowService;
import com.nq.service.IUserService;

import com.nq.vo.app.FollowDetailVO;
import com.nq.vo.app.FollowRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


@Api(tags = "APP-跟单记录")
@RestController
@RequestMapping("/api/followRecord")
public class AppFollowRecordApiController {
    @Resource
    private IUserService iUserService;
    @Resource
    private FollowService followService;


    @ApiOperation("查询跟单记录")
    @PostMapping("/queryFollowRecord.do")
    public ServerResponse<PageInfo<FollowRecordVO>> queryFollowRecord(HttpServletRequest request, @RequestBody FollowRecordQueryDTO dto) {
        User user = iUserService.getCurrentUser();
        if (user == null) {
            return ServerResponse.createBySuccessMsg("用户未登录");
        }
        PageInfo<FollowRecordVO> page = followService.queryFollowRecord(user, dto);
        return ServerResponse.createBySuccess(page);
    }

    @ApiOperation("查询某一个跟单的具体信息")
    @PostMapping("/queryFollowRecordDetail.do")
    public ServerResponse<FollowDetailVO> queryFollowRecordDetail(HttpServletRequest request, @RequestBody FollowRecordDetailQueryDTO dto) {
        User user = iUserService.getCurrentUser();
        if (user == null) {
            return ServerResponse.createBySuccessMsg("用户未登录");
        }
        FollowDetailVO vo = followService.queryFollowRecordDetail(user, dto);
        return ServerResponse.createBySuccess(vo);
    }

    @ApiOperation("撤单")
    @PostMapping("/cancelFollow.do")
    public ServerResponse<Boolean> cancelFollow(HttpServletRequest request, @RequestBody FollowCancelQueryDTO dto) {
        User user = iUserService.getCurrentUser();
        if (user == null) {
            return ServerResponse.createByErrorMsg("用户未登录");
        }
        followService.cancelFollow(user, dto.getId());
        return ServerResponse.createBySuccess(true);
    }

    @ApiOperation("追加")
    @PostMapping("/appendFollow.do")
    public ServerResponse<Boolean> appendFollow(HttpServletRequest request, @RequestBody FollowAppendDTO dto) {
        User user = iUserService.getCurrentUser();
        if (user == null) {
            return ServerResponse.createByErrorMsg("用户未登录");
        }
        followService.appendFollow(user, dto);
        return ServerResponse.createBySuccess(true);
    }


}

