package com.nq.controller.app;


import com.nq.common.ServerResponse;
import com.nq.common.satoken.StpUserUtil;
import com.nq.service.IUserService;
import com.nq.vo.user.UserLoginResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;


@RequestMapping({"/api/user/"})
@RestController
@Api(tags = "APP-登录注册")
public class AppLoginController {

    @Resource
    private IUserService iUserService;

    //注册
    @PostMapping("reg.do")
    @ApiOperation("注册")
    public ServerResponse<String> reg(@RequestParam("phone") String phone, @RequestParam(value = "yzmCode", defaultValue = "") String yzmCode, @RequestParam("userPwd") String userPwd, @RequestParam("yqm") String yqm,HttpServletRequest request) {
        iUserService.reg(yzmCode, phone, userPwd, yqm,request);
        return ServerResponse.createBySuccess();
    }

    //登录
    @PostMapping("login.do")
    @ApiOperation("登录")
    public ServerResponse<UserLoginResultVO> login(@RequestParam("phone") String phone, @RequestParam("userPwd") String userPwd,HttpServletRequest request) {
        UserLoginResultVO vo = iUserService.login(phone, userPwd,request);
        return ServerResponse.createBySuccess("登陆成功", vo);
    }

    //注销
    @GetMapping({"logout.do"})
    @ApiOperation("退出登录")
    public ServerResponse<String> logout() {
        // Sa-Token 方式退出
        StpUserUtil.logout();
        // 可选：如果你用 cookie 存 token，也可以手动删掉
        return ServerResponse.createBySuccess();
    }

    //查询手机号是否存在
    @PostMapping({"checkPhone.do"})
    @ApiOperation("查询手机号是否存咋")
    public ServerResponse<String> checkPhone(String phoneNum) {
        iUserService.checkPhone(phoneNum);
        return ServerResponse.createBySuccess();
    }


    //找回密码
    @GetMapping({"updatePwd.do"})
    @ApiOperation("找回密码")
    public ServerResponse<String> updatePwd(String phoneNum, String code, String newPwd) {
        iUserService.updatePwd(phoneNum, code, newPwd);
        return ServerResponse.createBySuccess();
    }


}

