package com.nq.pojo;

import java.io.Serializable;
import java.util.Date;

import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;

@ApiModel(description = "站内消息实体类")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SiteMessage extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "WAP端消息类型", example = "1")
    public static final int MSG_TYPE_WAP = 1;
    
    @ApiModelProperty(value = "管理后台消息类型", example = "2")
    public static final int MSG_TYPE_ADMIN = 2;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户ID", example = "1001")
    private Integer userId;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名", example = "张三")
    private String userName;

    /**
     * 类型名称
     */
    @ApiModelProperty(value = "消息类型名称", example = "系统通知")
    private String typeName;



    /**
     * 状态：1、未读，2、已读
     */
    @ApiModelProperty(value = "消息状态：1-未读，2-已读", example = "1")
    private Integer status;

    /**
     * 添加时间
     */
    @ApiModelProperty(value = "添加时间", example = "2024-01-01 12:00:00")
    private Date addTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", example = "2024-01-01 12:00:00")
    private Date updateTime;

    /**
     * 内容
     */
    @ApiModelProperty(value = "消息内容", example = "您有一笔新的交易")
    private String content;

    /**
     * 消息类别，1--客户端，2--管理后台，3--客户端和管理后台
     */
    @ApiModelProperty(value = "消息类别：1-客户端，2-管理后台，3-客户端和管理后台", example = "1")
    private Integer messageType;


}

