package com.nq.controller.agent;


import com.nq.common.ServerResponse;
import com.nq.common.satoken.StpAgentUtil;
import com.nq.service.IAgentUserService;
import com.nq.vo.agent.AgentLoginResultVO;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

@Api(tags = {"代理后台-登录管理"})
@RestController
@RequestMapping({"/api/agent/"})
public class AgentApiController {
    @Resource
    private IAgentUserService iAgentUserService;

    //代理后台登录
    @RequestMapping({"login.do"})
    public ServerResponse<AgentLoginResultVO> login(@RequestParam("agentPhone") String agentPhone, @RequestParam("agentPwd") String agentPwd, @RequestParam(value = "verifyCode", required = false, defaultValue = "") String verifyCode, HttpSession httpSession, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        AgentLoginResultVO vo = this.iAgentUserService.login(agentPhone, agentPwd, verifyCode, httpServletRequest);
        return ServerResponse.createBySuccess(vo);
    }

    //代理后台退出登录
    @RequestMapping({"logout.do"})
    public ServerResponse<String> logout(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        // Sa-Token 方式退出
        StpAgentUtil.logout();
        // 可选：如果你用 cookie 存 token，也可以手动删掉
        // CookieUtils.delLoginToken(httpServletRequest, httpServletResponse, "AGENTTOKEN");
        return ServerResponse.createBySuccess();
    }
}
