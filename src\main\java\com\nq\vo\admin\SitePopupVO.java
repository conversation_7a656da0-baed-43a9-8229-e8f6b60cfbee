package com.nq.vo.admin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

@Data
@ApiModel(description = "首页弹窗VO")
public class SitePopupVO {
    @ApiModelProperty("主键ID")
    private Integer id;

    @ApiModelProperty("弹窗图片URL")
    private String popupImg;

    @ApiModelProperty("状态：0-关闭，1-开启")
    private Integer status;

    @ApiModelProperty("开启时间")
    private Date startTime;

    @ApiModelProperty("关闭时间")
    private Date endTime;
} 