package com.nq.pojo;

import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "系统交易设置")
@AllArgsConstructor
@NoArgsConstructor
public class SiteSetting extends BaseEntity {
    @ApiModelProperty(value = "配置ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "买入手续费", example = "0.0035")
    private BigDecimal buyFee;

    @ApiModelProperty(value = "卖出手续费", example = "0.0035")
    private BigDecimal sellFee;

    @ApiModelProperty(value = "最小充值金额", example = "500")
    private Integer chargeMinAmt;

    @ApiModelProperty(value = "最小提现金额", example = "1000")
    private Integer withMinAmt;

    @ApiModelProperty(value = "提现开始时间(小时)", example = "13")
    private Integer withTimeBegin;

    @ApiModelProperty(value = "提现结束时间(小时)", example = "15")
    private Integer withTimeEnd;

    @ApiModelProperty(value = "上午交易开始时间", example = "9:32")
    private String transAmBegin;

    @ApiModelProperty(value = "上午交易结束时间", example = "11:30")
    private String transAmEnd;

    @ApiModelProperty(value = "下午交易开始时间", example = "13:00")
    private String transPmBegin;

    @ApiModelProperty(value = "下午交易结束时间", example = "14:57")
    private String transPmEnd;

    @ApiModelProperty(value = "提现手续费比例", example = "0.0080")
    private BigDecimal withFeePercent;

    @ApiModelProperty(value = "最大涨幅比例", example = "7.0000")
    private BigDecimal creaseMaxPercent;

    @ApiModelProperty(value = "科创板最大涨幅比例", example = "15.00")
    private BigDecimal kcCreaseMaxPercent;

    @ApiModelProperty(value = "创业板最大涨幅比例", example = "20.00")
    private BigDecimal cyCreaseMaxPercent;

    @ApiModelProperty(value = "VIP抢筹最大金额", example = "100000")
    private BigDecimal vipQcMaxAmt;
    @ApiModelProperty(value = "买入多长时间内不能平仓/分钟", example = "100000")
    private Integer cantSellTimes;
}
