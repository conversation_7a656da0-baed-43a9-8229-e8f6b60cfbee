package com.nq.vo.position;

import com.nq.pojo.UserPosition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
@Data
@ApiModel(description = "用户持仓信息视图对象")
public class UserPositionVO {
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;



    @ApiModelProperty(value = "持仓编号", example = "POS202401010001")
    private String positionSn;

    @ApiModelProperty(value = "用户ID", example = "10001")
    private Integer userId;

    @ApiModelProperty(value = "用户昵称", example = "张三")
    private String nickName;

    @ApiModelProperty(value = "可用数量", example = "1000")
    private Long availableNum;



    @ApiModelProperty(value = "股票名称", example = "贵州茅台")
    private String stockName;

    @ApiModelProperty(value = "股票代码", example = "600519")
    private String stockCode;

    @ApiModelProperty(value = "股票GID", example = "SH600519")
    private String stockGid;

    @ApiModelProperty(value = "股票拼音", example = "GZMT")
    private String stockSpell;

    @ApiModelProperty(value = "买入订单ID", example = "BUY202401010001")
    private String buyOrderId;

    @ApiModelProperty(value = "买入时间", example = "2024-01-01 09:30:00")
    private Date buyOrderTime;

    @ApiModelProperty(value = "买入价格", example = "1500.00")
    private BigDecimal buyOrderPrice;

    @ApiModelProperty(value = "卖出订单ID", example = "SELL202401010001")
    private String sellOrderId;

    @ApiModelProperty(value = "卖出时间", example = "2024-01-01 15:00:00")
    private Date sellOrderTime;

    @ApiModelProperty(value = "卖出价格", example = "1600.00")
    private BigDecimal sellOrderPrice;

    @ApiModelProperty(value = "止盈价格", example = "1700.00")
    private BigDecimal profitTargetPrice;

    @ApiModelProperty(value = "止损价格", example = "1400.00")
    private BigDecimal stopTargetPrice;

    @ApiModelProperty(value = "交易方向：BUY-买入，SELL-卖出", example = "BUY")
    private String orderDirection;

    @ApiModelProperty(value = "交易数量", example = "100")
    private Integer orderNum;


    @ApiModelProperty(value = "订单总金额", example = "150000.00")
    private BigDecimal orderTotalPrice;

    @ApiModelProperty(value = "订单手续费", example = "150.00")
    private BigDecimal orderFee;





    @ApiModelProperty(value = "盈亏金额", example = "10000.00")
    private BigDecimal profitAndLose;

    @ApiModelProperty(value = "总盈亏金额", example = "15000.00")
    private BigDecimal allProfitAndLose;

    @ApiModelProperty(value = "当前价格", example = "1600.00")
    private BigDecimal now_price;

    @ApiModelProperty(value = "股票板块", example = "白酒")
    private String stockPlate;



    @ApiModelProperty(value = "类型0买，1卖", example = "白酒")
    private Integer buyType;

    @ApiModelProperty(value = "买入手续费", example = "150.00")
    private BigDecimal buyOrderFee;

    @ApiModelProperty(value = "时间", example = "150.00")
    private Date createTime;

    @ApiModelProperty(value = "可用持仓列表")
    private List<UserPosition> availablePositionSn;
    @ApiModelProperty(value = "是否锁定：0、未锁定，1、已锁定", example = "0")
    private Integer isLock;

}
