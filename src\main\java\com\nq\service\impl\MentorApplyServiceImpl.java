package com.nq.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.dto.app.MentorApplyDTO;
import com.nq.excepton.CustomException;
import com.nq.dao.MentorApplyMapper;
import com.nq.pojo.Mentor;
import com.nq.pojo.MentorApply;
import com.nq.pojo.User;
import com.nq.service.IUserService;
import com.nq.service.MentorApplyService;
import com.nq.service.MentorService;
import com.nq.utils.PageUtil;
import com.nq.vo.app.MentorApplyVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class MentorApplyServiceImpl extends ServiceImpl<MentorApplyMapper, MentorApply> implements MentorApplyService {

    @Resource
    private MentorService mentorService;

    @Resource
    private IUserService userService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createMentorApply(MentorApplyDTO dto, Integer userId) {
        // 参数校验
        if (dto.getName() == null || dto.getName().trim().isEmpty()) {
            throw new CustomException("姓名不能为空");
        }
        if (dto.getAge() == null || dto.getAge() < 18) {
            throw new CustomException("年龄不能小于18岁");
        }
        if (dto.getInvestYears() == null || dto.getInvestYears() < 0) {
            throw new CustomException("投资年限不能为空且不能小于0");
        }
        if (dto.getSalaryRate() == null) {
            throw new CustomException("收益比例不能为空");
        }
        if (dto.getGuaranteeFund() == null) {
            throw new CustomException("担保资金不能为空");
        }

        // 检查是否已经申请过
        LambdaQueryWrapper<MentorApply> q = new LambdaQueryWrapper<>();
        q.eq(MentorApply::getUserId, userId)
                .eq(MentorApply::getStatus, 0);
        long count = this.count(q);
        if (count > 0) {
            throw new CustomException("您已有待审核的申请记录");
        }

        // 转换DTO为实体
        MentorApply mentorApply = new MentorApply();
        BeanUtils.copyProperties(dto, mentorApply);
        mentorApply.setUserId(userId);
        mentorApply.setStatus(0); // 待审核
        mentorApply.setCreateTime(new Date());
        mentorApply.setUpdateTime(new Date());

        return save(mentorApply);
    }

    @Override
    public PageInfo<MentorApplyVO> pageList(Integer page, Integer size, String name, Integer status) {
        PageHelper.startPage(page, size);

        // 构建查询条件
        LambdaQueryWrapper<MentorApply> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(status != null, MentorApply::getStatus, status)
                .like(name != null && !name.trim().isEmpty(), MentorApply::getName, name)  // 使用更直接的判断
                .orderByDesc(MentorApply::getCreateTime);

        // 执行查询
        List<MentorApply> list = this.list(wrapper);
        List<MentorApplyVO> voList = list.stream().map(this::convertToVO).collect(Collectors.toList());
        return PageUtil.buildPageDto(list,voList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean audit(Integer id, Integer status, String auditRemark) {
        // 参数校验
        if (id == null) {
            throw new CustomException("申请ID不能为空");
        }
        if (status == null || (status != 1 && status != 2)) {
            throw new CustomException("无效的审核状态");
        }

        MentorApply mentorApply = getById(id);
        if (mentorApply == null) {
            throw new CustomException("申请记录不存在");
        }

        if (mentorApply.getStatus() != 0) {
            throw new CustomException("该申请已审核，不能重复审核");
        }

        // 更新审核信息
        mentorApply.setStatus(status);
        mentorApply.setAuditRemark(auditRemark);
        mentorApply.setAuditTime(new Date());
        mentorApply.setUpdateTime(new Date());

        // 如果审核通过，创建导师记录
        if (status == 1) {
            createMentorFromApply(mentorApply);
        }

        return updateById(mentorApply);
    }

    /**
     * 从申请记录创建导师记录
     * @param mentorApply 申请记录
     */
    private void createMentorFromApply(MentorApply mentorApply) {
        // 获取用户信息
        User user = userService.getById(mentorApply.getUserId());
        if (user == null) {
            throw new CustomException("用户不存在");
        }

        // 创建导师记录
        Mentor mentor = new Mentor();
        mentor.setUserId(mentorApply.getUserId());
        mentor.setMentorName(mentorApply.getName());
        mentor.setMentorPhone(user.getPhone());
        mentor.setMentorAccount(user.getNickName());
        mentor.setInvestmentYears(mentorApply.getInvestYears());
        mentor.setSalaryRate(mentorApply.getSalaryRate());
        mentor.setDescription(mentorApply.getIntroduction());
        mentor.setCompany(mentorApply.getCompanyName()); // 使用申请中的公司名称
        mentor.setStatus(1); // 正常状态
        mentor.setCreateTime(new Date());
        mentor.setUpdateTime(new Date());

        // 保存导师记录
        boolean saved = mentorService.save(mentor);
        if (!saved) {
            throw new CustomException("创建导师记录失败");
        }
    }

    @Override
    public MentorApplyVO getDetailById(Integer id) {
        if (id == null) {
            throw new CustomException("申请ID不能为空");
        }

        MentorApply mentorApply = getById(id);
        if (mentorApply == null) {
            return null;
        }
        return convertToVO(mentorApply);
    }


    /**
     * 将实体转换为VO
     */
    private MentorApplyVO convertToVO(MentorApply mentorApply) {
        MentorApplyVO vo = new MentorApplyVO();
        BeanUtils.copyProperties(mentorApply, vo);

        // 设置状态描述
        switch (mentorApply.getStatus()) {
            case 0:
                vo.setStatusDesc("待审核");
                break;
            case 1:
                vo.setStatusDesc("通过");
                break;
            case 2:
                vo.setStatusDesc("驳回");
                break;
            default:
                vo.setStatusDesc("未知状态");
        }

        return vo;
    }


}