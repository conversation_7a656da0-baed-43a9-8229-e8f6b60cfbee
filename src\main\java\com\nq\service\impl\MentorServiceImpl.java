package com.nq.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.dao.MentorMapper;
import com.nq.pojo.Mentor;
import com.nq.service.MentorService;
import com.nq.utils.BeanCopyUtil;
import com.nq.utils.PageUtil;
import com.nq.vo.mentor.MentorVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class MentorServiceImpl extends ServiceImpl<MentorMapper, Mentor> implements MentorService {

    @Override
    public PageInfo<MentorVO> pageWithUser(Integer page, Integer size, String mentorName, String mentorAccount, String mentorPhone, Integer status) {
        PageHelper.startPage(page,size);
        // 1. 查询导师信息
        LambdaQueryWrapper<Mentor> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(mentorName != null && !mentorName.isEmpty(), Mentor::getMentorName, mentorName)
                .like(mentorAccount != null && !mentorAccount.isEmpty(), Mentor::getMentorAccount, mentorAccount)
                .like(mentorPhone != null && !mentorPhone.isEmpty(), Mentor::getMentorPhone, mentorPhone)
                .eq(status != null, Mentor::getStatus, status)
                .orderByDesc(Mentor::getCreateTime);
        List<Mentor> mentorList = this.list(wrapper);
        List<MentorVO> mentorVOS = BeanCopyUtil.copyToList(mentorList, MentorVO.class);
        // 2. 查询导师对应的用户信息
        return PageUtil.buildPageDto(mentorList,mentorVOS);
    }

    @Override
    public MentorVO getDetailById(Integer id) {
        // 1. 查询导师信息
        Mentor mentor = this.getById(id);
        if (mentor == null) {
            return null;
        }

        // 2. 组装VO对象
        MentorVO vo = new MentorVO();
        BeanUtils.copyProperties(mentor, vo);

        return vo;
    }

    @Override
    public  MentorVO  searchMentors(String keyword) {
        LambdaQueryWrapper<Mentor> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(Mentor::getMentorName, keyword)
                .or()
                .like(Mentor::getMentorPhone, keyword)
                .or()
                .like(Mentor::getMentorAccount, keyword).last("limit 1");

        Mentor  mentor = this.getOne(wrapper);
        return mentor != null ? convertToVO(mentor) : null;
    }

    private MentorVO convertToVO(Mentor mentor) {
        MentorVO vo = new MentorVO();
        BeanUtils.copyProperties(mentor, vo);
        return vo;
    }
}