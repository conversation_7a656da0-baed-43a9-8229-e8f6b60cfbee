<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nq.dao.SiteSettingMapper" >
  <resultMap id="BaseResultMap" type="com.nq.pojo.SiteSetting" >
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="buy_fee" property="buyFee" jdbcType="DECIMAL"/>
    <result column="sell_fee" property="sellFee" jdbcType="DECIMAL"/>
    <result column="charge_min_amt" property="chargeMinAmt" jdbcType="INTEGER"/>
    <result column="with_min_amt" property="withMinAmt" jdbcType="INTEGER"/>
    <result column="with_time_begin" property="withTimeBegin" jdbcType="INTEGER"/>
    <result column="with_time_end" property="withTimeEnd" jdbcType="INTEGER"/>
    <result column="trans_am_begin" property="transAmBegin" jdbcType="VARCHAR"/>
    <result column="trans_am_end" property="transAmEnd" jdbcType="VARCHAR"/>
    <result column="trans_pm_begin" property="transPmBegin" jdbcType="VARCHAR"/>
    <result column="trans_pm_end" property="transPmEnd" jdbcType="VARCHAR"/>
    <result column="with_fee_percent" property="withFeePercent" jdbcType="DECIMAL"/>
    <result column="crease_max_percent" property="creaseMaxPercent" jdbcType="DECIMAL"/>
    <result column="kc_crease_max_percent" property="kcCreaseMaxPercent" jdbcType="DECIMAL"/>
    <result column="cy_crease_max_percent" property="cyCreaseMaxPercent" jdbcType="DECIMAL"/>
    <result column="vip_qc_max_amt" property="vipQcMaxAmt" jdbcType="DECIMAL"/>
    <result column="cant_sell_times" property="cantSellTimes" jdbcType="INTEGER"/>
  </resultMap>

  <sql id="Base_Column_List" >
    id, buy_fee, sell_fee, charge_min_amt, with_min_amt,
    with_time_begin, with_time_end, trans_am_begin, trans_am_end, trans_pm_begin,
    trans_pm_end, with_fee_percent, crease_max_percent, kc_crease_max_percent,
    cy_crease_max_percent, vip_qc_max_amt, cant_sell_times
  </sql>

</mapper>