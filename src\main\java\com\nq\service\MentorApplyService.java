package com.nq.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.dto.app.MentorApplyDTO;
import com.nq.pojo.MentorApply;
import com.nq.vo.app.MentorApplyVO;

public interface MentorApplyService extends IService<MentorApply> {
    
    /**
     * 创建导师申请
     *
     * @param dto 导师申请信息
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean createMentorApply(MentorApplyDTO dto, Integer userId);
    
    /**
     * 分页查询导师申请列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param status 状态
     * @return 分页数据
     */
    PageInfo<MentorApplyVO> pageList(Integer page, Integer size,String name,  Integer status);
    
    /**
     * 审核导师申请
     *
     * @param id 申请ID
     * @param status 审核状态
     * @param auditRemark 审核备注
     * @return 是否成功
     */
    boolean audit(Integer id, Integer status, String auditRemark);
    
    /**
     * 获取导师申请详情
     *
     * @param id 申请ID
     * @return 导师申请详情
     */
    MentorApplyVO getDetailById(Integer id);
    

} 