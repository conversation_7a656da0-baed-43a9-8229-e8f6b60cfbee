package com.nq.excepton;

import com.nq.common.ResponseCode;

/**
 * 自定义异常
 *
 * <AUTHOR>
 */
public class CustomException extends RuntimeException
{
    private static final long serialVersionUID = 1L;

    private Integer code = ResponseCode.ERROR.getCode();

    private String message;

    public CustomException(String message)
    {
        this.message = message;
    }

    public CustomException(Integer code,String message)
    {
        this.message = message;
        this.code = code;
    }

    public CustomException(String message, Throwable e)
    {
        super(message, e);
        this.message = message;
    }

    @Override
    public String getMessage()
    {
        return message;
    }

    public Integer getCode()
    {
        return code;
    }
}
