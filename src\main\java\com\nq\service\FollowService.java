package com.nq.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dto.app.FollowAppendDTO;
import com.nq.dto.app.FollowRecordDetailQueryDTO;
import com.nq.dto.app.FollowRecordQueryDTO;
import com.nq.pojo.Follow;
import com.nq.pojo.User;
import com.nq.vo.admin.FollowAppendVO;
import com.nq.vo.admin.FollowVO;
import com.nq.vo.app.FollowDetailVO;
import com.nq.vo.app.FollowRecordVO;

import java.math.BigDecimal;
import java.util.List;

public interface FollowService extends IService<Follow> {
    
    /**
     * 分页查询跟单列表
     */
    PageInfo<FollowVO> pageList(Integer page, Integer size, Integer status,Integer addStatus,
                                String keyword, String beginTime, String endTime);
    
    /**
     * 获取跟单详情
     */
    FollowVO getDetailById(Integer id);
    
    /**
     * 审核跟单申请
     */
    void audit(Integer id, Integer status);
    
    /**
     * 审核追加申请
     */
    void auditAdd(Integer id, Integer status);
    
    /**
     * 创建跟单申请
     */
    void createFollow(Follow follow);

    /**
     * 分页查询跟单记录
     */
    PageInfo<FollowRecordVO> queryFollowRecord(User user, FollowRecordQueryDTO dto);
    /**
     * 查询跟单记录详情
     */
    FollowDetailVO queryFollowRecordDetail(User user, FollowRecordDetailQueryDTO dto);

    /**
     * 撤销跟单
     * @param user 当前用户
     * @param id 跟单号
     * @return 操作结果
     */
    void cancelFollow(User user, Integer id);

    /**
     * 追加跟单
     * @param user 当前用户
     * @param dto 追加请求参数
     * @return 操作结果
     */
    void appendFollow(User user, FollowAppendDTO dto);
    /**
     * 修改跟单
     * @return 操作结果
     */
    void update(Follow follow);
    /**
     * 查询追加审核
     */
    Long getPendingAddCount();
    /**
     * 中止跟单
     */
    void stopFollow(Integer id);
    /**
     * 查询追加审核详情
     */
    FollowAppendVO queryAppendDetail(Integer id);

    /**
     * 根据日期查询跟单数量
     * @param i
     * @return
     */

    Long countApplyFollowNumByDate(int i);
    /**
     * 根据日期查询跟单金额
     * @param i
     * @return
     */

    BigDecimal countApplyFollowAmountByDate(int i);

    /**
     * 查询待审核追加跟单数量
     * @return
     */
    Long countAppendFollowPendingCount();

}