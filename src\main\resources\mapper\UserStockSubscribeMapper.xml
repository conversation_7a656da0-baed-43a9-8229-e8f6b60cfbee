<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nq.dao.UserStockSubscribeMapper">

    <resultMap id="BaseResultMap" type="com.nq.pojo.UserStockSubscribe" >
        <result column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="user_id" property="userId" />
        <result column="real_name" property="realName" />
        <result column="phone" property="phone" />
        <result column="new_code" property="newCode" />
        <result column="new_name" property="newName" />
        <result column="new_type" property="newType" />
        <result column="bond" property="bond" />
        <result column="buy_price" property="buyPrice" />
        <result column="apply_nums" property="applyNums" />
        <result column="apply_number" property="applyNumber" />
        <result column="status" property="status" />
        <result column="add_time" property="addTime" />
        <result column="submit_time" property="submitTime" />
        <result column="end_time" property="endTime" />
        <result column="fix_time" property="fixTime" />
        <result column="remarks" property="remarks" />
        <result column="type" property="type" />
    </resultMap>

    <sql id="Base_Column_List">
                id,
                order_no,
                user_id,
                real_name,
                phone,
                new_code,
                new_name,
                new_type,
                bond,
                buy_price,
                apply_nums,
                apply_number,
                status,
                add_time,
                submit_time,
                end_time,
                fix_time,
                remarks,
                type
    </sql>


    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_stock_subscribe
        WHERE id = #{id}
    </select>

    <select id="pageList" resultMap="BaseResultMap" parameterType="map">
        SELECT <include refid="Base_Column_List" />
        FROM user_stock_subscribe
        <where>
        <if test="keyword != null and keyword != ''">
            and  phone =#{keyword}
        </if>
        <if test="status != null">
            and  status =#{status}
        </if>
        and (type = 1 or type = 2)
        </where>
        order by id desc
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM user_stock_subscribe
    </select>

    <!--查询用户最新新股申购数据-->
    <select id="getOneSubscribeByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_stock_subscribe
        WHERE phone = #{phone}  order by id
    </select>

</mapper>