package com.nq.pojo;

import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;
import java.util.Date;

@Data
@ApiModel(description = "用户登录日志")
@AllArgsConstructor
@NoArgsConstructor
public class SiteLoginLog extends BaseEntity {
    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "用户ID", example = "1001")
    private Integer userId;

    @ApiModelProperty(value = "用户名", example = "zhangsan")
    private String userName;

    @ApiModelProperty(value = "登录IP", example = "***********")
    private String loginIp;

    @ApiModelProperty(value = "登录地址", example = "广东省深圳市")
    private String loginAddress;

    @ApiModelProperty(value = "登录时间", example = "2024-01-01 12:00:00")
    private Date addTime;


}