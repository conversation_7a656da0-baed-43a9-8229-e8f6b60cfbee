package com.nq.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dto.app.TeamInfoDTO;
import com.nq.dto.common.CommonPage;
import com.nq.pojo.User;
import com.nq.vo.user.UserAgentInfoVO;
import com.nq.vo.agent.AgentUserListVO;
import com.nq.vo.app.FollowIncomeVO;
import com.nq.vo.app.TeamInfoVO;
import com.nq.vo.app.TeamVO;
import com.nq.vo.user.UserInfoVO;
import com.nq.vo.user.UserLoginResultVO;

import java.math.BigDecimal;
import java.util.List;
import javax.servlet.http.HttpServletRequest;

public interface IUserService extends IService<User>  {
  //yzmCode, agentCode, phone, userPwd,yqm
  void reg(String yzmCode, String phone, String userPwd,String yqm,HttpServletRequest request);
  //hone, userPwd
  UserLoginResultVO login(String hone, String userPwd, HttpServletRequest request);


  void addOption(String code);

  void delOption(String code);

  Boolean isOption(String code);

  UserInfoVO getUserInfo();
//oldPwd, String newPwd
  void updatePwd(String oldPwd, String newPwd);

  void checkPhone(String paramString);

//phoneNum, code, newPwd
  void updatePwd(String phoneNum, String code, String newPwd);

  void update(User paramUser);
  //String realName, String idCard, String img1key, String img2key, String img3key, HttpServletRequest request
  void auth(String realName, String idCard, String img1key, String img2key, String img3key);


  PageInfo<User>listByAdmin(User user, CommonPage commonPage);

  User findByUserId(Integer userId);

  void updateLock(Integer userId);

  void updateLoginLock(Integer userId);
//serId, amt, direction
  void updateAmt(Integer userId, String amt, Integer direction);

  void delete(Integer userId);


  //userId, state, authMsg
  void authByAdmin(Integer userId, Integer state, String authMsg);



  /**
   * 设置支付密码
   * @param pwd
   */
  void setWithPwd(String pwd);

  void updateWithPwd(String oldPwd, String newPwd);


  User queryUserByYqm(String yqm);

  List<User> queryUserByKeyword(String keyword);

  FollowIncomeVO getFollowIncome(User user);

  PageInfo<TeamInfoVO> getTeamInfo(User user, TeamInfoDTO dto);

  TeamVO getTeam(User user);
  /**
   * 增加跟单信息
   */
  void addTodayFollowBuy(Integer userId);
  /**
   * 增加卖单信息
   * @param userId
   */

  void addTodayFollowSell(Integer userId, BigDecimal profit);
  /**
   * 查询所有下级
   */
  List<User> queryAllLowerLevelTask(Integer id);
  /**
   * 查询有效人数
   */
  List<User> queryAllLowerEfficientTask(Integer id);
  /**
   * 增加
   */
  void addLeadEarnAmount(Integer userId, BigDecimal amount);


  User getCurrentUser();
  /**
   *  转账到可用余额
   * @param amount
   */
  void transferToEnable(BigDecimal amount);

  /**
   *  转账到总余额
   * @param amount
   */
  void transferToTotal(BigDecimal amount);

  /**
   * 查询累类返佣和累计线下人数
   * @return
   */

    UserAgentInfoVO getUserAgentInfoVO();

  /**
   * 新增用户
   * @param nickName
   * @param phone
   * @param pwd
   * @param amt
   * @param pid
   */

   void addSimulatedAccount(String nickName,String phone, String pwd, String amt, Long pid);

  /**
   * 昨日薪资 type  0 全部 1 今日 2 昨日 3 近七日 4 近一个月
   * @return
   */
  Long countNewUserByDate(Integer type);

  /**
   * 提现总金额
   * @return
   */
  BigDecimal countWithdrawAmount();

  /**
   * 充值总金额
   * @return
   */
  BigDecimal countChargeAmount();

  /**
   * 今日首冲人数
   * @param type
   * @return
   */

  Long countFirstChargeNumByDate(Integer type);

  /**
   * 查询当日等待实名审核人数
   * @return
   */

  Long countRealNamePendingCount();


  List<User> queryLowerUser(Integer userId);
}
