package com.nq.controller.app;

import com.nq.common.ServerResponse;
import com.nq.pojo.UserBank;
import com.nq.service.IUserBankService;
import com.nq.vo.user.UserBankInfoVO;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/user/bank")
@Api(tags = "APP-用户银行卡管理接口")
public class AppUserBankController {

    @Resource
    private IUserBankService iUserBankService;

    @PostMapping("/add.do")
    @ApiOperation(value = "添加银行卡", notes = "用户添加新的银行卡信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bank", value = "银行卡信息", required = true, dataType = "UserBank")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "添加成功", response = ServerResponse.class),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public ServerResponse<String> add(@RequestBody UserBank bank, HttpServletRequest request) {
        iUserBankService.addBank(bank, request);
        return ServerResponse.createBySuccess("添加成功");
    }

    @PostMapping("/update.do")
    @ApiOperation(value = "修改银行卡信息", notes = "修改用户已绑定的银行卡信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bank", value = "银行卡信息", required = true, dataType = "UserBank")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "修改成功", response = ServerResponse.class),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public ServerResponse<String> update(@RequestBody UserBank bank, HttpServletRequest request) {
        iUserBankService.updateBank(bank, request);
        return ServerResponse.createBySuccess("添加成功");
    }

    @PostMapping("/getBankInfo.do")
    @ApiOperation(value = "查询用户银行卡信息", notes = "获取当前用户绑定的银行卡信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功", response = ServerResponse.class),
            @ApiResponse(code = 400, message = "参数错误"),
            @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public ServerResponse<UserBankInfoVO> getBankInfo(HttpServletRequest request) {
        UserBankInfoVO vo =   iUserBankService.getBankInfo(request);
        return ServerResponse.createBySuccess(vo);
    }


}
