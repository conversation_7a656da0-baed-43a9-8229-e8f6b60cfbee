/**
 * Copyright (c) 2016-2019 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package com.nq.job.controller;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.job.entity.ScheduleJobLogEntity;
import com.nq.job.service.ScheduleJobLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 定时任务日志
 *
 * <AUTHOR> sunlight<PERSON>@gmail.com
 */
@Api(tags = "定时任务日志管理")
@RestController
@RequestMapping("/admin/scheduleLog")
public class ScheduleJobLogController {
	@Resource
	private ScheduleJobLogService scheduleJobLogService;

	/**
	 * 定时任务日志列表
	 */
	@ApiOperation("分页查询定时任务日志列表")
	@GetMapping("/list.do")
	public ServerResponse<PageInfo<ScheduleJobLogEntity>> list(
			@ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
			@ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer pageSize,
			@ApiParam("任务ID") @RequestParam(required = false) String jobId) {
		Map<String, Object> params = new HashMap<>();
		params.put("page", pageNum.toString());
		params.put("limit", pageSize.toString());
		if (jobId != null) {
			params.put("jobId", jobId);
		}

		PageInfo<ScheduleJobLogEntity> page = scheduleJobLogService.queryPage(params);
		return ServerResponse.createBySuccess(page);
	}

	/**
	 * 定时任务日志信息
	 */
	@ApiOperation("获取定时任务日志详情")
	@GetMapping("/info.do")
	public ServerResponse<ScheduleJobLogEntity> info(@ApiParam("日志ID") @RequestParam("logId") String logId) {
		ScheduleJobLogEntity log = scheduleJobLogService.getById(logId);
		return ServerResponse.createBySuccess(log);
	}
}
