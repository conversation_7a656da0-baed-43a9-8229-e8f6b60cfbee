package com.nq.controller.admin;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.UserRecharge;
import com.nq.service.IUserRechargeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping({"/admin/recharge/"})
@Api(tags = "后台-充值管理")
public class AdminRechargeController {

    private static final Logger log = LoggerFactory.getLogger(AdminRechargeController.class);
    @Resource
    private IUserRechargeService iUserRechargeService;

    //分页查询资金管理 充值列表信息及模糊查询
    @PostMapping({"list.do"})
    @ApiOperation("分页查询充值列表信息")
    public ServerResponse<PageInfo<UserRecharge>> list(@RequestParam(value = "userId", required = false) Integer userId,
                                                       @RequestParam(value = "nickName", required = false) String nickName,
                                                       @RequestParam(value = "state", required = false) Integer state,
                                                       @RequestParam(value = "orderSn", required = false) String orderSn,
                                                       @RequestParam(value = "beginTime", required = false) String beginTime,
                                                       @RequestParam(value = "endTime", required = false) String endTime,
                                                       @RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        PageInfo<UserRecharge> userRechargePageInfo = this.iUserRechargeService.listByAdmin(userId,orderSn,nickName, state, beginTime, endTime, pageNum, pageSize, false);
        return ServerResponse.createBySuccess(userRechargePageInfo);
    }

    /**
     * 分页查询资金管理 充值列表信息及模糊查询 导出
     *
     * @param userId
     * @param nickName
     * @param state
     * @param beginTime
     * @param endTime
     * @param response
     */
    @PostMapping({"export.do"})
    @ApiOperation("导出充值列表信息")
    public void export(@RequestParam(value = "userId", required = false) Integer userId,
                       @RequestParam(value = "orderSn", required = false) String orderSn,
                       @RequestParam(value = "nickName", required = false) String nickName,
                       @RequestParam(value = "state", required = false) Integer state, @RequestParam(value = "beginTime", required = false) String beginTime, @RequestParam(value = "endTime", required = false) String endTime, HttpServletResponse response) {
        List<UserRecharge> userRechargeList = this.iUserRechargeService.exportByAdmin(userId,orderSn, nickName, state, beginTime, endTime, false);
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("充值导出", "充值数据"),
                UserRecharge.class, userRechargeList);
        try {
            ServletOutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            log.error("导出充值数据失败", e);

        }
    }

    //修改资金管理 充值列表订单状态
    @PostMapping({"updateState.do"})
    @ApiOperation("修改充值订单状态")
    public ServerResponse<String> updateState(@RequestParam(value = "chargeId", required = false) Long chargeId, @RequestParam(value = "state", required = false) Integer state, @RequestParam(value = "orderDesc", required = false) String orderDesc) {
        this.iUserRechargeService.updateState(chargeId, state, orderDesc);
        return ServerResponse.createBySuccess("<UNK>");
    }

    //创建资金管理 充值订单
    @PostMapping({"createOrder.do"})
    @ApiOperation("创建充值订单")
    public ServerResponse<String> createOrder(@RequestParam(value = "userId", required = false) Integer userId, @RequestParam(value = "state", required = false) Integer state, @RequestParam(value = "amt", required = false) Integer amt, @RequestParam(value = "payChannel", required = false) String payChannel) {
        this.iUserRechargeService.createOrder(userId, state, amt, payChannel);
        return ServerResponse.createBySuccess();
    }

    //删除资金管理 充值列表订单信息
    @PostMapping({"del.do"})
    @ApiOperation("删除充值订单信息")
    public ServerResponse<String> del(@RequestParam("cId") Long cId) {
        this.iUserRechargeService.del(cId);
        return ServerResponse.createBySuccess();
    }
}
