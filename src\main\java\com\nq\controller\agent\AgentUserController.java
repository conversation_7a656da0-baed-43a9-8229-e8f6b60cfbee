package com.nq.controller.agent;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.service.IAgentUserService;
import com.nq.service.IUserService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.nq.vo.agent.AgentUserListVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/agent/user/"})
public class AgentUserController {
    private static final Logger log = LoggerFactory.getLogger(AgentUserController.class);

    @Resource
    private IUserService iUserService;


    //分页查询用户管理信息及模糊查询
    @RequestMapping({"list.do"})

    public ServerResponse<PageInfo<AgentUserListVO>> list(@RequestParam(value = "realName", required = false) String realName, @RequestParam(value = "phone", required = false) String phone, @RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "12") int pageSize) {
        return  null;
    }
}
