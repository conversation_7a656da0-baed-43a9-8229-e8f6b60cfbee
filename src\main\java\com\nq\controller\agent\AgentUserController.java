package com.nq.controller.agent;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dto.common.CommonPage;
import com.nq.pojo.User;
import com.nq.service.IUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Api(tags = {"代理后台-用户管理"})
@RequestMapping({"/agent/user/"})
public class AgentUserController {
    private static final Logger log = LoggerFactory.getLogger(AgentUserController.class);

    @Resource
    private IUserService iUserService;

    //分页查询所有用户列表信息 及模糊查询用户信息
    @PostMapping({"list.do"})
    @ApiOperation("分页查询所有用户列表信息及模糊查询用户信息")
    public ServerResponse<PageInfo<User>> list(User user, CommonPage commonPage) {
        PageInfo<User> userPageInfo = this.iUserService.listByAgent(user, commonPage);
        return ServerResponse.createBySuccess(userPageInfo);
    }

    //查询用户信息是否存在
    @PostMapping({"detail.do"})
    @ApiOperation("查询用户信息是否存在")
    public ServerResponse<User> detail(Integer userId) {
        User user = this.iUserService.findByUserId(userId);
        return ServerResponse.createBySuccess(user);
    }
}
