package com.nq.controller.app;

import com.nq.common.ServerResponse;
import com.nq.pojo.SiteBanner;
import com.nq.service.ISiteBannerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "APP-轮播图接口")
@RestController
@RequestMapping({"/api/site/"})
public class AppBannerApiController {
    private static final Logger log = LoggerFactory.getLogger(AppBannerApiController.class);

    @Resource
    private ISiteBannerService iSiteBannerService;


    // 查询官网PC端交易轮播图信息
    @PostMapping({"getBannerByPlat.do"})
    @ApiOperation("查询轮播图")
    public ServerResponse<List<SiteBanner>> getBannerByPlat(String platType) {
        List<SiteBanner> bannerByPlat = iSiteBannerService.getBannerByPlat(platType);
        return ServerResponse.createBySuccess(bannerByPlat);
    }



}



