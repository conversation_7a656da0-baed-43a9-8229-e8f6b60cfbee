package com.nq.controller.admin;


import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dto.common.CommonPage;
import com.nq.pojo.Activity;
import com.nq.pojo.ActivityReceive;
import com.nq.service.ActivityReceiveService;
import com.nq.service.ActivityService;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RestController
@RequestMapping({"/admin/activityReceive/"})
@Api(tags = {"后台-活动领取管理"})
public class AdminActivityReceiveController {
    private static final Logger log = LoggerFactory.getLogger(AdminActivityReceiveController.class);


    @Resource
    private ActivityReceiveService activityReceiveService;

    @PostMapping({"list.do"})
    public ServerResponse<PageInfo<ActivityReceive>> list(ActivityReceive activityReceive, CommonPage commonPage) {
        PageInfo<ActivityReceive> page = activityReceiveService.listByAdmin(activityReceive, commonPage);
        return ServerResponse.createBySuccess(page);
    }
}
