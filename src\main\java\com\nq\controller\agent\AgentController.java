package com.nq.controller.agent;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.AgentUser;
import com.nq.service.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.nq.vo.agent.AgentInfoVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/agent/"})
public class AgentController {
    private static final Logger log = LoggerFactory.getLogger(AgentController.class);
    @Resource
    private IAgentUserService iAgentUserService;





    //修改代理用户密码
    @RequestMapping({"updatePwd.do"})
    public ServerResponse<String> updatePwd(@RequestParam("oldPwd") String oldPwd, @RequestParam("newPwd") String newPwd, HttpServletRequest request) {
        this.iAgentUserService.updatePwd(oldPwd, newPwd);
        return ServerResponse.createBySuccess();
    }








}
