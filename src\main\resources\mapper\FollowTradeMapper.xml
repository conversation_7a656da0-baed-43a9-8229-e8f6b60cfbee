<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nq.dao.FollowTradeMapper">
    <resultMap id="BaseResultMap" type="com.nq.pojo.FollowTrade">
        <id column="id" property="id"/>
        <result column="trade_no" property="tradeNo"/>
        <result column="mentor_id" property="mentorId"/>
        <result column="stock_code" property="stockCode"/>
        <result column="stock_name" property="stockName"/>
        <result column="buy_price" property="buyPrice"/>
        <result column="sell_price" property="sellPrice"/>
        <result column="profit_loss_percent" property="profitLossPercent"/>
        <result column="buy_time" property="buyTime"/>
        <result column="sell_time" property="sellTime"/>
        <result column="create_time" property="createTime"/>
        <result column="publish_time" property="publishTime"/>
        <result column="status" property="status"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, trade_no, mentor_id, stock_code, stock_name, buy_price, sell_price,
        profit_loss_percent, buy_time, sell_time, create_time, publish_time, status
    </sql>
</mapper> 