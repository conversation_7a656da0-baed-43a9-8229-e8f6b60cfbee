package com.nq.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AccountTypeEnum {
    INCOME(1, "收入"),
    EXPENSE(2, "支出");

    private final Integer code;
    private final String desc;

    public static AccountTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (AccountTypeEnum value : AccountTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 