package com.nq.excepton;


import cn.dev33.satoken.exception.NotLoginException;
import cn.hutool.json.JSONException;
import com.nq.common.ResultUtils;
import com.nq.common.ServerResponse;
import com.nq.enums.ResultCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.time.format.DateTimeParseException;
import java.util.List;


/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);


    @ExceptionHandler(CustomException.class)
    public ServerResponse handlerNoFoundException(CustomException e) {
        log.error("全局异常捕获:msg:{}", e.getMessage());
        return  ServerResponse.createByError(e.getMessage(),e.getCode());
    }


    /**
     * 数据解析错误
     **/
    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    public ServerResponse handleMessageNotReadableException(HttpMessageNotReadableException e) {
        log.error("全局异常捕获:msg:{}", e.getMessage());
        Throwable t = e.getCause();
        if (t instanceof JSONException) {
            t = t.getCause();
            if (t instanceof DateTimeParseException) {
                return ResultUtils.error(ResultCode.PROGRAM_ERROR, "日期格式不正确");
            }
            return ResultUtils.error(ResultCode.PROGRAM_ERROR, "数据格式不正确");
        }
        return ResultUtils.error(ResultCode.PROGRAM_ERROR);
    }

    /**
     * 处理请求参数格式错误 @RequestBody上validate失败后抛出的异常
     *
     * @param exception
     * @return
     */
    @ExceptionHandler(value = {MethodArgumentNotValidException.class})
    @ResponseStatus(HttpStatus.OK)
    public ServerResponse handleValidationExceptionHandler(MethodArgumentNotValidException exception) {
        log.error("全局异常捕获:msg:{}", exception.getMessage());
        BindingResult bindResult = exception.getBindingResult();
        String msg;
        if (bindResult.hasErrors()) {
            msg = bindResult.getAllErrors().get(0).getDefaultMessage();
            if (msg.contains("NumberFormatException")) {
                msg = "参数类型错误！";
            }
        } else {
            msg = "系统繁忙，请稍后重试...";
        }
        return ResultUtils.error(ResultCode.PROGRAM_ERROR, msg);
    }


    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.OK)
    public ServerResponse handleBindException(BindException e) {
        log.error("全局异常捕获:msg:{}", e.getMessage());
        //抛出异常可能不止一个 输出为一个List集合
        List<ObjectError> errors = e.getAllErrors();
        //取第一个异常
        ObjectError error = errors.get(0);
        //获取异常信息
        String errorMsg = error.getDefaultMessage();
        return ResultUtils.error(ResultCode.PROGRAM_ERROR, errorMsg);
    }


    /**
     * 自定义验证异常
     */
    @ExceptionHandler(AssertException.class)
    public ServerResponse assertExceptionHandler(AssertException e) {
        log.error("全局异常捕获:msg:{}", e.getMessage());
        log.error(e.getMessage(), e);
        return ResultUtils.error(e.getCode(), e.getMsg());
    }
    /**
     * 自定义验证异常
     */
    @ExceptionHandler(NotLoginException.class)
    public ServerResponse notLoginExceptionHandler(NotLoginException e) {
        ResultCode codeEnum = ResultCode.getCodeEnum(Integer.parseInt(e.getType()));
        return ResultUtils.error(codeEnum.getCode(), e.getMessage());
    }

}
