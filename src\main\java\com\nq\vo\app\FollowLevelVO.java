package com.nq.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "跟单队长等级信息")
public class FollowLevelVO {

    @ApiModelProperty(value = "队长等级", example = "跟单成员")
    private String levelName;

    @ApiModelProperty(value = "投顾最低金额", example = "5000")
    private BigDecimal minAmount;

    @ApiModelProperty(value = "投顾最高金额", example = "30000")
    private BigDecimal maxAmount;

    @ApiModelProperty(value = "返佣比例", example = "0")
    private BigDecimal salaryRate;

    @ApiModelProperty(value = "仓位比例", example = "40")
    private BigDecimal positionRate;

    @ApiModelProperty(value = "有效跟单人数最小值", example = "1")
    private Integer minPeople;

    @ApiModelProperty(value = "有效跟单人数最大值", example = "2")
    private Integer maxPeople;

}