package com.nq.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.pojo.Mentor;
import com.nq.vo.mentor.MentorVO;

import java.util.List;

public interface MentorService extends IService<Mentor> {

    /**
     * 分页查询导师信息（包含用户信息）
     */
    PageInfo<MentorVO> pageWithUser(Integer page, Integer size, String mentorName, String mentorAccount, String mentorPhone, Integer status);

    /**
     * 获取导师详情（包含用户信息）
     */
    MentorVO getDetailById(Integer id);

    /**
     * 根据关键词查询导师
     *
     * @param keyword 关键词(姓名/手机号)
     * @return 导师列表
     */
    MentorVO searchMentors(String keyword);
}