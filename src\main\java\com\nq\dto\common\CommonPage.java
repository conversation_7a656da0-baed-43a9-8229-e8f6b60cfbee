package com.nq.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-02-23
 */
@Data
public class CommonPage implements Serializable {

    private static final long serialVersionUID = 1L;


    @NotNull(message = "页码不能为空")
    @ApiModelProperty(value = "页码", example = "1", required = true)
    private Integer pageNum = 1;
    @NotNull(message = "size不能为空")
    @ApiModelProperty(value = "size", example = "10", required = true)
    private Integer pageSize = 30;
}
