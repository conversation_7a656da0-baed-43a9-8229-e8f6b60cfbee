package com.nq.config;

import com.nq.common.interceptor.ApiAdminAuthorityInterceptor;
import com.nq.common.interceptor.ApiAgentAuthorityInterceptor;
import com.nq.common.interceptor.ApiUserAuthorityInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import java.text.SimpleDateFormat;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;

import javax.annotation.Resource;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Resource
    private RedisTemplate<String,String> redisTemplate;
//    @Override
//    public void addCorsMappings(CorsRegistry registry) {
//        registry.addMapping("/**")
//                .allowedOrigins("*")
//                .allowedMethods("*")
//                .allowCredentials(true)  // 允許帶cookie訪問
//                .allowedHeaders("*")
//                .maxAge(3600);
//                ;
//
//    }



    //註冊攔截器
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //SpringMVC下，攔截器的註冊需要排除對靜態資源的攔截(*.css,*.js)
        //SpringBoot已經做好了靜態資源的映射，因此我們無需任何操作


        registry.addInterceptor(new ApiAgentAuthorityInterceptor()).addPathPatterns("/agentNew/**")
                .excludePathPatterns("/index.html", "/", "/user/login")
        ;
        registry.addInterceptor(new ApiUserAuthorityInterceptor()).addPathPatterns("/user/**")
                .excludePathPatterns("/index.html", "/", "/user/login", "/user/cash/getUnreadCount.do")
        ;
        registry.addInterceptor(new ApiAdminAuthorityInterceptor()).addPathPatterns("/admin/**")
                .excludePathPatterns("/index.html", "/", "/user/login")
        ;
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/**")
                .addResourceLocations("classpath:/static/");
        
        registry.addResourceHandler("swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
    }

    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        objectMapper.setTimeZone(java.util.TimeZone.getTimeZone("Asia/Shanghai"));
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        return objectMapper;
    }

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {
        return builder -> {
            builder.simpleDateFormat("yyyy-MM-dd HH:mm:ss");
            builder.timeZone(java.util.TimeZone.getTimeZone("Asia/Shanghai"));
            builder.featuresToDisable(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        };
    }
}
