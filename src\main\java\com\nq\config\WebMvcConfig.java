package com.nq.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.nq.common.interceptor.ApiAdminAuthorityInterceptor;
import com.nq.common.interceptor.ApiAgentAuthorityInterceptor;
import com.nq.common.interceptor.ApiUserAuthorityInterceptor;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Resource
    private RedisTemplate<String,String> redisTemplate;
    @Resource
    private ApiUserAuthorityInterceptor apiUserAuthorityInterceptor;
    @Resource
    private ApiAgentAuthorityInterceptor apiAgentAuthorityInterceptor;
    @Resource
    private ApiAdminAuthorityInterceptor apiAdminAuthorityInterceptor;

    //註冊攔截器
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(apiAgentAuthorityInterceptor).addPathPatterns("/agentNew/**")
                .excludePathPatterns("/index.html", "/", "/user/login");
        registry.addInterceptor(apiUserAuthorityInterceptor).addPathPatterns("/user/**")
                .excludePathPatterns("/index.html", "/", "/user/login", "/user/cash/getUnreadCount.do");
        registry.addInterceptor(apiAdminAuthorityInterceptor).addPathPatterns("/admin/**")
                .excludePathPatterns("/index.html", "/", "/user/login");
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/**")
                .addResourceLocations("classpath:/static/");
        
        registry.addResourceHandler("swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
    }

    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        objectMapper.setTimeZone(java.util.TimeZone.getTimeZone("Asia/Shanghai"));
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper;
    }

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {
        return builder -> {
            builder.simpleDateFormat("yyyy-MM-dd HH:mm:ss");
            builder.timeZone(java.util.TimeZone.getTimeZone("Asia/Shanghai"));
            builder.featuresToDisable(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        };
    }
}
