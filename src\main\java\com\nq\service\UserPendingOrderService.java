package com.nq.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.pojo.UserPendingOrder;
import com.nq.vo.position.UserPendingOrderVO;
import com.nq.vo.position.UserPositionVO;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【user_pendingOrder】的数据库操作Service
* @createDate 2022-11-10 06:10:40
*/
public interface UserPendingOrderService extends IService<UserPendingOrder> {



    void orderTask();
    PageInfo<UserPendingOrder> findMyPendingOrder(Integer userId,String stockCode, int pageNum, int pageSize);
}
