package com.nq.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TypeEnum {
    //金额类型( 0    1 平台账户, 2 证券账户)',
    RECHARGE(1, "充值",1),
    WITHDRAW(2, "提现",1),
    BUY_PING_STOCK(3, "买入委托",2),
    STOCK_BUY(4, "买入股票",2),
    RETURN_PING_STOCK(5, "委托退回",2),
    STOCK_SELL(6, "卖出股票",2),
    STOCK_REFUND(7, "股票退回",2),
    ADVISOR_APPLY(8, "申请投顾",1),
    ADVISOR_REJECT(9, "投顾驳回",1),
    ADVISOR_REFUND(10, "投顾退回",1),
    ADVISOR_PROFIT(11, "投顾收益",1),
    ADVISOR_APPEND(12, "投顾追加",1),
    ADVISOR_APPEND_REFUND(13, "投顾追加退回",1),
    STOCK_TO_BANK(14, "证转银",0),
    BANK_TO_STOCK(15, "银转证",0),
    TEAM_SALARY(16, "团队工资",1),
    BONUS(17, "奖金",1),
    //金额类型( 0    1 平台账户, 2 证券账户)',
    BACKUP(18, "后台增加",1),
    BACKDOWN(19, "后台减少",1),

    WITHDRAWBACK(20, "提现驳回",1);
    private final Integer code;
    private final String desc;
    private final Integer amountType;

    public static TypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TypeEnum value : TypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 