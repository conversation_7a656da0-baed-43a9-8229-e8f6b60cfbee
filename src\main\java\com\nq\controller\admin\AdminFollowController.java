package com.nq.controller.admin;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.Follow;
import com.nq.service.FollowService;
import com.nq.vo.admin.FollowAppendVO;
import com.nq.vo.admin.FollowVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "后台-跟单管理")
@RestController
@RequestMapping("/admin/follow/")
public class AdminFollowController {

    @Resource
    private FollowService followService;

    @ApiOperation("分页查询跟单列表")
    @GetMapping("list.do")
    public ServerResponse<PageInfo<FollowVO>> pageList(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页数量") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("追加审核状态 (0-待审核, 1-通过, 2-拒绝)") @RequestParam(required = false) Integer addStatus,
            @ApiParam("跟单状态 (1-待审核, 2-跟单中, 3-已结束, 4-已撤销)") @RequestParam(required = false) Integer status,
            @ApiParam("关键字 (用户姓名/手机号/账号)") @RequestParam(required = false) String keyword,
            @ApiParam("开始时间 (yyyy-MM-dd HH:mm:ss)") @RequestParam(required = false) String beginTime,
            @ApiParam("结束时间 (yyyy-MM-dd HH:mm:ss)") @RequestParam(required = false) String endTime) {
        PageInfo<FollowVO> pageInfo = followService.pageList(pageNum, pageSize, status,
                addStatus, keyword, beginTime, endTime, false);
        return ServerResponse.createBySuccess(pageInfo);
    }

    @ApiOperation("获取跟单详情")
    @PostMapping("detail.do")
    public ServerResponse<FollowVO> getDetail(@RequestBody Follow follow) {
        FollowVO followVO = followService.getDetailById(follow.getId());
        return ServerResponse.createBySuccess(followVO);
    }

    @ApiOperation("创建跟单申请")
    @PostMapping("create.do")
    public ServerResponse<String> create(@RequestBody Follow follow) {
        followService.createFollow(follow);
        return ServerResponse.createBySuccessMsg("创建成功");
    }

    @ApiOperation("审核跟单申请")
    @PostMapping("audit.do")
    public ServerResponse<String> audit(@RequestBody Follow follow) {
        followService.audit(follow.getId(), follow.getStatus(), follow.getRemark());
        return ServerResponse.createBySuccessMsg("审核成功");
    }

    @ApiOperation("审核追加申请")
    @PostMapping("audit-add.do")
    public ServerResponse<String> auditAdd(@RequestBody Follow follow) {
        followService.auditAdd(follow.getId(), follow.getStatus());
        return ServerResponse.createBySuccessMsg("审核成功");
    }

    @ApiOperation("修改仓位")
    @PostMapping("update.do")
    public ServerResponse<FollowVO> update(@RequestBody Follow follow) {
        followService.update(follow);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation("获取待审核追加数量")
    @GetMapping("pending-add-count.do")
    public ServerResponse<Long> getPendingAddCount() {
        Long count = followService.getPendingAddCount(); // Assumes method exists in FollowService
        return ServerResponse.createBySuccess(count);
    }

    @ApiOperation("中止跟单")
    @PostMapping("stop.do")
    public ServerResponse<String> stopFollow(@RequestBody Follow follow) {
        followService.stopFollow(follow.getId());
        return ServerResponse.createBySuccessMsg("中止成功");
    }

    @ApiOperation("查询追加审核详情")
    @PostMapping("queryAppendDetail.do")
    public ServerResponse<FollowAppendVO> queryAppendDetail(@RequestBody Follow follow) {
        FollowAppendVO appendVO = followService.queryAppendDetail(follow.getId());
        return ServerResponse.createBySuccess(appendVO);
    }
}