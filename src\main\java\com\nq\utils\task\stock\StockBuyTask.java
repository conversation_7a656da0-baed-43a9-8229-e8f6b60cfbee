package com.nq.utils.task.stock;


import com.nq.excepton.CustomException;
import com.nq.job.task.ITask;
import com.nq.pojo.SiteProduct;
import com.nq.pojo.SiteSetting;
import com.nq.service.ISiteProductService;
import com.nq.service.ISiteSettingService;
import com.nq.service.UserPendingOrderService;
import com.nq.utils.DateTimeUtil;
import com.nq.utils.stock.BuyAndSellUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;


@Component("stockBuyTask")
public class StockBuyTask implements ITask {

    private static final Logger log = LoggerFactory.getLogger(StockBuyTask.class);

    @Resource
    private UserPendingOrderService userPendingOrderService;
    @Resource
    private ISiteSettingService iSiteSettingService;
    @Resource
    private ISiteProductService iSiteProductService;


    @Override
    public void run(String params) throws InterruptedException {
        /*实名认证开关开启*/
        SiteProduct siteProduct = iSiteProductService.getProductSetting();
        SiteSetting siteSetting = this.iSiteSettingService.getSiteSetting();

        String am_begin = siteSetting.getTransAmBegin();
        String am_end = siteSetting.getTransAmEnd();
        String pm_begin = siteSetting.getTransPmBegin();
        String pm_end = siteSetting.getTransPmEnd();
        boolean am_flag = false;
        boolean pm_flag = false;
        try {
            am_flag = BuyAndSellUtils.isTransTime(am_begin, am_end);
            pm_flag = BuyAndSellUtils.isTransTime(pm_begin, pm_end);
        } catch (Exception e) {
            return;
        }
        log.info("=====掃描挂單執行，當前時間 {} =====", DateTimeUtil.dateToStr(new Date()));
        log.info("是否在上午交易时间 = {} 是否在下午交易时间 = {}", am_flag, pm_flag);
        //hyh
        if (!am_flag && !pm_flag) {
            return;
        }
        if (siteProduct.getHolidayDisplay()) {
            return;
        }
        this.userPendingOrderService.orderTask();
        log.info("=====掃描挂單結束，當前時間 {} =====", DateTimeUtil.dateToStr(new Date()));
    }
}
