<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nq.dao.SiteAdminMapper" >
  <resultMap id="BaseResultMap" type="com.nq.pojo.SiteAdmin" >
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="admin_name" property="adminName" jdbcType="VARCHAR"/>
    <result column="admin_pwd" property="adminPwd" jdbcType="VARCHAR"/>
    <result column="admin_phone" property="adminPhone" jdbcType="VARCHAR"/>
    <result column="is_lock" property="isLock" jdbcType="INTEGER"/>
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
  </resultMap>
  <sql id="Base_Column_List" >
    id, admin_name, admin_pwd, admin_phone, is_lock, add_time
  </sql>
  <select id="login"  parameterType="map" resultType="com.nq.pojo.SiteAdmin">
    SELECT
    <include refid="Base_Column_List"/>
    FROM site_admin
    WHERE admin_phone = #{adminPhone} and admin_pwd = #{adminPwd}
  </select>


  <select id="listByAdmin" parameterType="map" resultType="com.nq.pojo.SiteAdmin">
    SELECT
    <include refid="Base_Column_List"/>
    FROM site_admin
    <where>
      <if test="adminName != null and adminName != '' ">
        and admin_name = #{adminName}
      </if>
      <if test="adminPhone != null and adminPhone != '' ">
        and admin_phone = #{adminPhone}
      </if>
      and admin_phone != #{superAdmin}
    </where>
  </select>



  <select id="findAdminByName" parameterType="string" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM site_admin
    WHERE admin_name = #{name}
  </select>


  <select id="findAdminByPhone" parameterType="string" resultType="com.nq.pojo.SiteAdmin">
    SELECT
    <include refid="Base_Column_List"/>
    FROM site_admin
    WHERE admin_phone = #{phone}
  </select>


</mapper>


