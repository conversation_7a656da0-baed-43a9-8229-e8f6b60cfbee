<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nq.dao.AgentUserMapper" >
  <resultMap id="BaseResultMap" type="com.nq.pojo.AgentUser" >
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="agent_name" property="agentName" jdbcType="VARCHAR"/>
    <result column="agent_pwd" property="agentPwd" jdbcType="VARCHAR"/>
    <result column="user_id" property="userId" jdbcType="INTEGER"/>
    <result column="user_nick_name" property="userNickName" jdbcType="VARCHAR"/>
    <result column="user_real_name" property="userRealName" jdbcType="VARCHAR"/>
    <result column="agent_phone" property="agentPhone" jdbcType="VARCHAR"/>
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
    <result column="is_lock" property="isLock" jdbcType="INTEGER"/>
  </resultMap>
  <sql id="Base_Column_List" >
    id, agent_name, agent_pwd, user_id, user_nick_name, user_real_name, agent_phone, add_time, is_lock
  </sql>

  <select id="login" parameterType="map" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM agent_user
    WHERE agent_phone = #{agentPhone} and agent_pwd = #{agentPwd}
  </select>



</mapper>