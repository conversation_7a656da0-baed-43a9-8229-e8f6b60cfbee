/**
 * Copyright (c) 2016-2019 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package com.nq.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nq.job.entity.ScheduleJobLogEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 定时任务日志
 *
 * <AUTHOR> sunlight<PERSON>@gmail.com
 */
@Mapper
public interface ScheduleJobLogDao extends BaseMapper<ScheduleJobLogEntity> {

    /**
     * 根据任务id查询日志
     */
    List<ScheduleJobLogEntity> getLogsByJobId(String jobId);

    /**
     * 批量删除日志
     */
    int deleteBatch(String[] logIds);

    /**
     * 根据任务id删除日志
     */
    int deleteByJobId(String jobId);
}
