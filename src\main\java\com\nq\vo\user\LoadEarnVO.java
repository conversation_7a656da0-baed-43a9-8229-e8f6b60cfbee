package com.nq.vo.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(description = "队长分佣VO")
public class LoadEarnVO {
    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    @ApiModelProperty(value = "用户名", example = "zhangsan")
    private String userName;

    @ApiModelProperty(value = "真实姓名", example = "张三")
    private String realName;

    @ApiModelProperty(value = "分佣等会")
    private String levelName;

    @ApiModelProperty(value = "等级", example = "1")
    private Integer level;

    @ApiModelProperty(value = "工资比例", example = "0.10")
    private BigDecimal salaryRate;

    @ApiModelProperty(value = "总投顾金额挣钱金额", example = "10000.00")
    private BigDecimal totalFollowEarnAmount;

    @ApiModelProperty(value = "周期")
    private String period;

    @ApiModelProperty(value = "分佣金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
} 