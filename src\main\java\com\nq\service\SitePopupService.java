package com.nq.service;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.SitePopup;
import com.nq.vo.admin.SitePopupVO;
import java.util.List;
import io.swagger.annotations.ApiOperation;

public interface SitePopupService {
    @ApiOperation("新增首页弹窗")
    int insert(SitePopup popup);
    @ApiOperation("更新首页弹窗")
    int update(SitePopup popup);
    @ApiOperation("删除首页弹窗")
    int deleteById(Integer id);
    @ApiOperation("根据ID查询首页弹窗实体")
    SitePopup selectById(Integer id);

    @ApiOperation("分页查询首页弹窗VO列表")
    PageInfo<SitePopupVO> selectAllVO(Integer pageNum, Integer pageSize);

    @ApiOperation("根据ID查询首页弹窗VO")
    SitePopupVO selectVOById(Integer id);

    ServerResponse<SitePopupVO> getPopup();
} 