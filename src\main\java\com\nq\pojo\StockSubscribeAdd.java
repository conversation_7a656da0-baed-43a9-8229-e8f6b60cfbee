package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 增发股
 */
@ApiModel(description="增发股")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "stock_subscribe_add")
public class StockSubscribeAdd {
    @TableId(value = "newlist_id", type = IdType.AUTO)
    @ApiModelProperty(value="")
    private Integer newlistId;

    /**
     * 新股名称
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value="新股名称")
    private String name;

    /**
     * 申购代码
     */
    @TableField(value = "code")
    @ApiModelProperty(value="申购代码")
    private String code;

    /**
     * 发行价格
     */
    @TableField(value = "price")
    @ApiModelProperty(value="发行价格")
    private BigDecimal price;

    /**
     * 发行数量 单位万股
     */
    @TableField(value = "order_number")
    @ApiModelProperty(value="发行数量 单位万股")
    private Long orderNumber;

    /**
     * 类型
     */
    @TableField(value = "stock_type")
    @ApiModelProperty(value="类型")
    private String stockType;

    /**
     * 市盈率
     */
    @TableField(value = "pe")
    @ApiModelProperty(value="市盈率")
    private String pe;

    /**
     * 状态 1是现实 0是隐藏
     */
    @TableField(value = "zt")
    @ApiModelProperty(value="状态 1是现实 0是隐藏")
    private Integer zt;

    /**
     * 锁定状态 0锁定 1不锁定
     */
    @TableField(value = "is_lock")
    @ApiModelProperty(value="锁定状态 0锁定 1不锁定")
    private Integer isLock;

    /**
     * 增发开始日期
     */
    @TableField(value = "start_time")
    @ApiModelProperty(value="增发开始日期")
    private Date startTime;

    /**
     * 增发结束日期
     */
    @TableField(value = "end_time")
    @ApiModelProperty(value="增发结束日期")
    private Date endTime;

    /**
     * 上市时间
     */
    @TableField(value = "list_date")
    @ApiModelProperty(value="上市时间")
    private Date listDate;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value="创建人")
    private String createBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value="修改时间")
    private Date updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value="修改人")
    private String updateBy;
}