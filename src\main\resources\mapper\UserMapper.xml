<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nq.dao.UserMapper">
    <resultMap id="BaseResultMap" type="com.nq.pojo.User">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="user_pwd" property="userPwd" jdbcType="VARCHAR"/>
        <result column="with_pwd" property="withPwd" jdbcType="VARCHAR"/>
        <result column="nick_name" property="nickName" jdbcType="VARCHAR"/>
        <result column="real_name" property="realName" jdbcType="VARCHAR"/>
        <result column="id_card" property="idCard" jdbcType="VARCHAR"/>
        <result column="user_amt" property="userAmt" jdbcType="DECIMAL"/>
        <result column="enable_amt" property="enableAmt" jdbcType="DECIMAL"/>
        <result column="bzj_amt" property="bzjAmt" jdbcType="DECIMAL"/>
        <result column="sum_charge_amt" property="sumChargeAmt" jdbcType="DECIMAL"/>
        <result column="sum_withdraw_amt" property="sumWithdrawAmt" jdbcType="DECIMAL"/>
        <result column="is_lock" property="isLock" jdbcType="INTEGER"/>
        <result column="is_login" property="isLogin" jdbcType="INTEGER"/>
        <result column="reg_time" property="regTime" jdbcType="TIMESTAMP"/>
        <result column="reg_ip" property="regIp" jdbcType="VARCHAR"/>
        <result column="reg_address" property="regAddress" jdbcType="VARCHAR"/>
        <result column="img1_key" property="img1Key" jdbcType="VARCHAR"/>
        <result column="img2_key" property="img2Key" jdbcType="VARCHAR"/>
        <result column="is_active" property="isActive" jdbcType="INTEGER"/>
        <result column="auth_msg" property="authMsg" jdbcType="VARCHAR"/>
        <result column="today_follow_income" property="todayFollowIncome" jdbcType="DECIMAL"/>
        <result column="total_follow_income" property="totalFollowIncome" jdbcType="DECIMAL"/>
        <result column="today_lead_income" property="todayLeadIncome" jdbcType="DECIMAL"/>
        <result column="total_lead_income" property="totalLeadIncome" jdbcType="DECIMAL"/>
        <result column="today_follow_num" property="todayFollowNum" jdbcType="INTEGER"/>
        <result column="total_follow_num" property="totalFollowNum" jdbcType="INTEGER"/>
        <result column="yqm" property="yqm" jdbcType="VARCHAR"/>
        <result column="lower_num" property="lowerNum" jdbcType="INTEGER"/>
        <result column="lower_efficient_num" property="lowerEfficientNum" jdbcType="INTEGER"/>
        <result column="pid" property="pid" jdbcType="INTEGER"/>
        <result column="p_path" property="pPath" jdbcType="VARCHAR"/>
        <result column="is_today_follow" property="isTodayFollow" jdbcType="INTEGER"/>
        <result column="is_today_recharge" property="isTodayRecharge" jdbcType="INTEGER"/>
        <result column="is_recharge" property="isRecharge" jdbcType="INTEGER"/>
        <result column="is_week_follow" property="isWeekFollow" jdbcType="INTEGER"/>
        <result column="level" property="level" jdbcType="INTEGER"/>
        <result column="follow_time" property="followTime" jdbcType="TIMESTAMP"/>
        <result column="first_recharge_time" property="firstRechargeTime" jdbcType="TIMESTAMP"/>
        <result column="first_follow_stop_time" property="firstFollowStopTime" jdbcType="TIMESTAMP"/>
        <result column="is_follow" property="isFollow" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, phone, user_pwd, with_pwd, nick_name, real_name, id_card,
        user_amt, enable_amt, bzj_amt, sum_charge_amt, sum_withdraw_amt, is_lock, is_login,
        reg_time, reg_ip, reg_address, img1_key, img2_key, is_active, auth_msg,
        today_follow_income, total_follow_income, today_lead_income, total_lead_income,
        today_follow_num, total_follow_num, yqm, lower_num, lower_efficient_num, pid, p_path, 
        is_today_follow, is_today_recharge, is_recharge, is_week_follow, level, follow_time, first_recharge_time, first_follow_stop_time,
        is_follow
    </sql>
    <select id="findByPhone" parameterType="string" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user
        WHERE phone = #{phoneNum}
    </select>

    <select id="login" resultMap="BaseResultMap" parameterType="map">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user
        WHERE phone = #{phone} and user_pwd = #{userPwd}
    </select>


    <update id="addTodayFollowBuy">
        update `user` set is_today_follow = 1,today_follow_num = today_follow_num + 1,is_week_follow = 1,follow_time = now() where id = #{user_id}
    </update>


    <update id="addTodayFollowSell">
        update `user` set today_follow_income = today_follow_income + #{money} where id = #{user_id}
    </update>

    <select id="queryAllLowerLevelTask"  resultType="com.nq.pojo.User">
        select id  from user where find_in_set(#{user_id},p_path)  ORDER BY id desc
    </select>
    <select id="queryAllLowerEfficientTask"  resultType="com.nq.pojo.User">
        select id  from user where find_in_set(#{user_id},p_path)  and  is_recharge = 1 and is_week_follow = 1 ORDER BY id desc
    </select>
    <update id="addLeadEarnAmount">
        update `user` set today_lead_income = today_lead_income + #{money} where id = #{user_id}
    </update>
    <update id="addRechargeMoney">
        update `user`
        set
            sum_charge_amt = sum_charge_amt + #{money},
            is_today_recharge = 1,
            first_recharge_time = CASE
                WHEN is_recharge = 0 THEN now()
                ELSE first_recharge_time
            END,
            is_recharge = 1
        where id = #{user_id}
    </update>
    <update id="addWithdrawMoney">
        update `user` set sum_withdraw_amt = sum_withdraw_amt + #{money} where id = #{user_id}
    </update>

    <update id="setFirstFollowInfo">
        update `user`
        set
            first_follow_stop_time = CASE WHEN is_follow = 0 THEN  #{date} ELSE first_follow_stop_time END,
            is_follow = 1,
            follow_time = now()
    where id = #{user_id}
</update>
</mapper>