package com.nq.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.common.satoken.StpAgentUtil;
import com.nq.common.satoken.StpUserUtil;
import com.nq.constant.OrderConstant;
import com.nq.dao.*;
import com.nq.dto.app.TeamInfoDTO;
import com.nq.dto.common.CommonPage;
import com.nq.enums.OrderTypeEnum;
import com.nq.enums.TypeEnum;
import com.nq.excepton.CustomException;
import com.nq.function.AmountConsumer;
import com.nq.manage.UserAmountChangeManage;
import com.nq.pojo.*;
import com.nq.service.*;
import com.nq.utils.DateUtils;
import com.nq.utils.InviteCodeUtil;
import com.nq.utils.SnowIdUtil;
import com.nq.utils.SymmetricCryptoUtil;
import com.nq.utils.ip.IpUtils;
import com.nq.utils.ip.JuheIpApi;
import com.nq.utils.redis.RedisShardedPoolUtils;
import com.nq.vo.app.FollowIncomeVO;
import com.nq.vo.app.TeamInfoVO;
import com.nq.vo.app.TeamVO;
import com.nq.vo.user.UserAgentInfoVO;
import com.nq.vo.user.UserInfoVO;
import com.nq.vo.user.UserLoginResultVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("iUserService")
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {
    private static final Logger log = LoggerFactory.getLogger(UserServiceImpl.class);

    @Resource
    private UserMapper userMapper;
    @Resource
    private ISiteLoginLogService iSiteLoginLogService;
    @Resource
    private StockOptionMapper stockOptionMapper;
    @Resource
    private StockMapper stockMapper;
    @Resource
    private IUserPositionService iUserPositionService;
    @Resource
    private IUserBankService iUserBankService;
    @Resource
    private AgentUserMapper agentUserMapper;
    @Resource
    private IStockOptionService iStockOptionService;


    @Resource
    private IUserRechargeService iUserRechargeService;
    @Resource
    private IUserWithdrawService iUserWithdrawService;

    @Resource
    private IUserService iUserService;
    @Resource
    private SiteMessageMapper siteMessageMapper;
    @Resource
    private IUserRechargeService userRechargeService;
    @Resource
    private ISiteAdminService siteAdminService;
    @Resource
    private LevelService levelService;
    @Resource
    private IUserWithdrawService userWithdrawService;
    @Resource
    private UserAmountChangeManage userAmountChangeManage;

    // //yzmCode, agentCode, phone, userPwd,yqm
    public void reg(String yzmCode, String phone, String userPwd, String yqm, HttpServletRequest request) {
        if (StringUtils.isBlank(phone) || StringUtils.isBlank(userPwd) || StringUtils.isBlank(yzmCode)) {
            throw new CustomException("注册失败, 参数不能为空");
        }
        // 查询用户是否存在
        User userExit = this.userMapper.findByPhone(phone);
        if (userExit != null) {
            throw new CustomException("注册失败, 手机号已注册");
        }
        String keys = "AliyunSmsCode:" + phone;
        String redis_yzm = RedisShardedPoolUtils.get(keys);
        log.info("redis_yzm = {},yzmCode = {}", redis_yzm, yzmCode);
        if (!yzmCode.equals(redis_yzm) && !"6666".equals(yzmCode)) {
            throw new CustomException("注册失败, 验证码错误");
        }
        User user = new User();
        User parent = null;
        // 根据yqm 去查询 用户
        if (StrUtil.isNotEmpty(yqm)) {
            parent = iUserService.queryUserByYqm(yqm);
        }
        if (parent != null) {
            user.setPid(parent.getId());
            if (null != parent.getPPath() && !parent.getPPath().isEmpty()) {
                user.setPPath(parent.getPPath() + "," + parent.getId());
            } else {
                user.setPPath(parent.getId() + "");
            }
        }

        User dbuser = this.userMapper.findByPhone(phone);
        if (dbuser != null) {
            throw new CustomException("注册失败, 手机号已注册");
        }
        // 根据邀请码去hyh
        // 邀请ma 你自己记着改

        user.setPhone(phone);
        user.setUserPwd(SymmetricCryptoUtil.encryptPassword(userPwd));
        user.setLowerNum(0);
        user.setIsLock(0);
        user.setIsActive(0);
        user.setRegTime(new Date());
        String uip = IpUtils.getIp(request);
        user.setRegIp(uip);
        user.setRegAddress(JuheIpApi.ip2Add(uip));
        user.setIsLogin(0);
        int insertCount = this.userMapper.insert(user);
        if (insertCount <= 0) {
            throw new CustomException("注册出错, 请重试");
        }
        User updateUser = new User();
        updateUser.setId(user.getId());
        updateUser.setYqm(InviteCodeUtil.generateInviteCode(user.getId()));
        userMapper.updateById(updateUser);
        // 需要给他上记得人数加一
        handleGroup(user.getPPath());
    }

    @Override
    public void addSimulatedAccount(String nickName, String phone, String pwd, String amt, Long pid) {
        User user = new User();
        User parent = null;
        // 根据yqm 去查询 用户
        if (pid != null) {
            parent = iUserService.getById(pid);
        }
        if (parent != null) {
            user.setPid(parent.getId());
            if (null != parent.getPPath() && !parent.getPPath().isEmpty()) {
                user.setPPath(parent.getPPath() + "," + parent.getId());
            } else {
                user.setPPath(parent.getId() + "");
            }
        }

        User dbuser = this.userMapper.findByPhone(phone);
        if (dbuser != null) {
            throw new CustomException("注册失败, 手机号已注册");
        }
        // 根据邀请码去hyh
        // 邀请ma 你自己记着改

        user.setPhone(phone);
        user.setNickName(nickName);
        user.setUserPwd(SymmetricCryptoUtil.encryptPassword(pwd));
        user.setLowerNum(0);
        user.setIsLock(0);
        user.setIsActive(0);
        user.setRegTime(new Date());
        user.setIsLogin(0);
        user.setUserAmt(new BigDecimal(amt));
        int insertCount = this.userMapper.insert(user);

        if (insertCount <= 0) {
            throw new CustomException("注册出错, 请重试");
        }
        User updateUser = new User();
        updateUser.setId(user.getId());
        updateUser.setYqm(InviteCodeUtil.generateInviteCode(user.getId()));
        userMapper.updateById(updateUser);
        // 需要给他上记得人数加一
        handleGroup(user.getPPath());
        BigDecimal amtBig = new BigDecimal(amt);
        String orderNo = SnowIdUtil.getId(OrderConstant.rechargeOrder);
        TypeEnum e = TypeEnum.BACKUP;
        AmountConsumer amountConsumer = (oldUser, newUser) -> {
            userRechargeService.inMoneyByAdmin(orderNo, amt, "后台充值", user.getId());
        };
        if (new BigDecimal(amt).compareTo(new BigDecimal(0)) > 0) {
            userAmountChangeManage.changeBalance(user.getId(), amtBig, orderNo, OrderTypeEnum.BACK, e, "", "", amountConsumer);
        }


    }

    //0 全部 1 今日 2 昨日 3 近七日 4 近一个月
    @Override
    public Long countNewUserByDate(Integer type) {
        //查询昨日新增人数
        HashMap<String, Date> todayMap = DateUtils.changeTimeSection(type);
        Date startTime = todayMap.get("startTime");
        Date endTime = todayMap.get("endTime");
        LambdaQueryWrapper<User> q = new LambdaQueryWrapper<>();
        q.ge(startTime != null, User::getRegTime, startTime);
        q.le(endTime != null, User::getRegTime, endTime);
        return this.userMapper.selectCount(q);
    }

    @Override
    public BigDecimal countWithdrawAmount() {
        LambdaQueryWrapper<User> q = new LambdaQueryWrapper<>();
        List<User> arr = this.list(q);
        return arr.stream().map(User::getSumWithdrawAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal countChargeAmount() {
        LambdaQueryWrapper<User> q = new LambdaQueryWrapper<>();
        List<User> arr = this.list(q);
        return arr.stream().map(User::getSumChargeAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public Long countFirstChargeNumByDate(Integer type) {
        HashMap<String, Date> todayMap = DateUtils.changeTimeSection(type);
        Date startTime = todayMap.get("startTime");
        Date endTime = todayMap.get("endTime");
        LambdaQueryWrapper<User> q = new LambdaQueryWrapper<>();
        q.ge(startTime != null, User::getFirstRechargeTime, startTime);
        q.le(endTime != null, User::getFirstRechargeTime, endTime);
        return count(q);
    }

    @Override
    public Long countRealNamePendingCount() {
        LambdaQueryWrapper<User> q = new LambdaQueryWrapper<>();
        q.eq(User::getIsActive, 1);
        List<User> arr = this.list(q);
        if (arr.isEmpty()) {
            return 0L;
        }
        return (long) arr.size();
    }

    // 给他所有的上级团队人数加一
    private void handleGroup(String pPath) {
        if (StringUtils.isNotEmpty(pPath)) {
            String[] split = pPath.split(",");
            List<String> asList = Arrays.asList(split);
            List<String> reverse = CollectionUtil.reverse(asList);
            for (String s : reverse) {
                User user = iUserService.getById(s);
                if (user != null) {
                    Integer l = user.getLowerNum() + 1;
                    user.setLowerNum(l);
                    iUserService.updateById(user);
                }
            }
        }
    }

    public UserLoginResultVO login(String phone, String userPwd, HttpServletRequest request) {
        if (StringUtils.isBlank(phone) || StringUtils.isBlank(userPwd)) {
            throw new CustomException("参数不能为空");
        }

        userPwd = SymmetricCryptoUtil.encryptPassword(userPwd);
        User user = this.userMapper.login(phone, userPwd);
        if (user == null) {
            throw new CustomException("用户密码不正确");
        }
        if (user.getIsLock() == 1) {
            throw new CustomException("登陆失败，您的账号已被锁定！");
        }
        Integer isLogin = user.getIsLogin();
        if(isLogin == 1){
            throw new CustomException("登陆失败，禁止登录！");
        }

        StpUserUtil.login(user.getId());
        String tokenValue = StpUserUtil.getTokenValue();
        UserLoginResultVO vo = new UserLoginResultVO();
        vo.setToken(tokenValue);
        this.iSiteLoginLogService.saveLog(user, request);
        return vo;
    }

    public void addOption(String code) {
        User user = getCurrentUser();
        if (user == null) {
            throw new CustomException("請先登錄");
        }

        String stockcode = code;
        if (code.contains("hf")) {
            stockcode = code.split("_")[1];
        }
        stockcode = stockcode.replace("sh", "").replace("sz", "").replace("bj", "");

        StockOption dboption = this.stockOptionMapper.findMyOptionIsExistByCode(user.getId(), stockcode);
        if (dboption != null) {
            return;
        }

        Stock stock = this.stockMapper.findStockByCode(code);
        if (stock == null) {
            throw new CustomException("添加失败，股票不存在");
        }

        StockOption stockOption = new StockOption();
        stockOption.setUserId(user.getId());
        stockOption.setStockId(stock.getId());
        stockOption.setAddTime(new Date());
        stockOption.setStockCode(stock.getStockCode());
        stockOption.setStockName(stock.getStockName());
        stockOption.setStockGid(stock.getStockGid());
        stockOption.setIsLock(stock.getIsLock());

        int insertCount = this.stockOptionMapper.insert(stockOption);
        if (insertCount <= 0) {
            throw new CustomException("添加失败, 请重试");
        }
    }

    public void delOption(String code) {
        User user = getCurrentUser();
        if (user == null) {
            throw new CustomException("請先登錄");
        }

        String stockcode = code;
        if (code.contains("hf")) {
            stockcode = code.split("_")[1];
        }
        stockcode = stockcode.replace("sh", "").replace("sz", "").replace("bj", "");

        StockOption dboption = this.stockOptionMapper.findMyOptionIsExistByCode(user.getId(), stockcode);
        if (dboption != null) {
            int delCount = this.stockOptionMapper.deleteById(dboption.getId());
            if (delCount <= 0) {
                throw new CustomException("删除失败, 请重试");
            }
        }
    }

    public Boolean isOption(String code) {
        User user = getCurrentUser();
        if (user == null) {
            throw new CustomException("請先登錄");
        }

        String stockcode = code;
        if (code.contains("hf")) {
            stockcode = code.split("_")[1];
        }
        stockcode = stockcode.replace("sh", "").replace("sz", "").replace("bj", "");
        return this.iStockOptionService.isOption(user.getId(), stockcode);
    }

    public UserInfoVO getUserInfo() {
        Integer userId = StpUserUtil.getUserId();
        User dbuser = this.userMapper.selectById(userId);
        return assembleUserInfoVO(dbuser);
    }

    @Override
    public List<Integer> getUserIdByAgent() {
        try {
            Integer agentUserId = StpAgentUtil.getAgentUserId();
            return this.lambdaQuery()
                    .select(User::getId)
                    .apply("FIND_IN_SET({0}, p_path)", agentUserId)
                    .list()
                    .stream()
                    .map(User::getId)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            return null;
        }

    }

    public void updatePwd(String oldPwd, String newPwd) {
        if (StringUtils.isBlank(oldPwd) || StringUtils.isBlank(newPwd)) {
            throw new CustomException("参数不能为空");
        }

        User user = getCurrentUser();
        if (user == null) {
            throw new CustomException("請先登錄");
        }

        oldPwd = SymmetricCryptoUtil.encryptPassword(oldPwd);
        if (!oldPwd.equals(user.getUserPwd())) {
            throw new CustomException("密码错误");
        }

        user.setUserPwd(SymmetricCryptoUtil.encryptPassword(newPwd));
        int updateCount = this.userMapper.updateById(user);
        if (updateCount <= 0) {
            throw new CustomException("修改失败");
        }
    }

    public void checkPhone(String phone) {
        User user = this.userMapper.findByPhone(phone);
        if (user != null) {
            throw new CustomException("用户已存在");
        }
        throw new CustomException("用户不存在");
    }

    public void updatePwd(String phone, String code, String newPwd) {
        if (StringUtils.isBlank(phone) || StringUtils.isBlank(code) || StringUtils.isBlank(newPwd)) {
            throw new CustomException("参数不能为空");
        }

        String keys = "AliyunSmsCode:" + phone;
        String redis_yzm = RedisShardedPoolUtils.get(keys);
        log.info("redis_yzm = {} , code = {}", redis_yzm, code);

        if (!code.equals(redis_yzm)) {
            throw new CustomException("修改密码失败，验证码错误");
        }

        User user = this.userMapper.findByPhone(phone);
        if (user == null) {
            throw new CustomException("用户不存在");
        }

        user.setUserPwd(SymmetricCryptoUtil.encryptPassword(newPwd));
        int updateCount = this.userMapper.updateById(user);
        if (updateCount <= 0) {
            throw new CustomException("修改密码失败");
        }
    }

    public void update(User user) {
        if (user.getUserPwd() != null && !user.getUserPwd().isEmpty()) {
            user.setUserPwd(SymmetricCryptoUtil.encryptPassword(user.getUserPwd()));
        }
        if (user.getWithPwd() != null && !user.getWithPwd().isEmpty()) {
            user.setWithPwd(SymmetricCryptoUtil.encryptPassword(user.getWithPwd()));
        }

        int updateCount = this.userMapper.updateById(user);
        if (updateCount <= 0) {
            throw new CustomException("修改失败");
        }
    }

    public void auth(String realName, String idCard, String img1key, String img2key, String img3key) {
        if (StringUtils.isBlank(realName) || StringUtils.isBlank(idCard) || StringUtils.isBlank(img1key)
                || StringUtils.isBlank(img2key)) {
            throw new CustomException("参数不能为空");
        }

        User user = getCurrentUser();
        if (user == null) {
            throw new CustomException("请登录");
        }

        if (((0 != user.getIsActive())) & ((3 != user.getIsActive()))) {
            throw new CustomException("当前状态不能认证");
        }

        user.setNickName(realName);
        user.setRealName(realName);
        user.setIdCard(idCard);
        user.setImg1Key(img1key);
        user.setImg2Key(img2key);
        user.setIsActive(1);

        int updateCount = this.userMapper.updateById(user);
        if (updateCount > 0) {
            SiteMessage siteMessage = new SiteMessage();
            siteMessage.setUserId(user.getId());
            siteMessage.setMessageType(SiteMessage.MSG_TYPE_ADMIN);
            siteMessage.setStatus(1);
            siteMessage.setUserName(user.getRealName());
            siteMessage.setAddTime(new Date());
            siteMessage.setTypeName("实名审核申请");
            siteMessage.setContent("用户【" + user.getRealName() + "】发起实名认证审核");
            this.siteMessageMapper.insert(siteMessage);
            return;
        }
        throw new CustomException("实名认证失败");
    }


    public PageInfo<User> listByAdmin(User user, CommonPage commonPage) {
        int pageNum = commonPage.getPageNum();
        int pageSize = commonPage.getPageSize();

        PageHelper.startPage(pageNum, pageSize);
        LambdaQueryWrapper<User> q = new LambdaQueryWrapper<>();
        this.buildQuery(q, user);

        List<User> users = this.userMapper.selectList(q);
        return new PageInfo<>(users);
    }

    @Override
    public PageInfo<User> listByAgent(User user, CommonPage commonPage) {
        int pageNum = commonPage.getPageNum();
        int pageSize = commonPage.getPageSize();

        PageHelper.startPage(pageNum, pageSize);
        LambdaQueryWrapper<User> q = new LambdaQueryWrapper<>();
        this.buildQuery(q, user);
        q.apply("FIND_IN_SET({0}, p_path)", StpAgentUtil.getAgentUserId());
        List<User> users = this.userMapper.selectList(q);
        return new PageInfo<>(users);
    }

    private void buildQuery(LambdaQueryWrapper<User> q, User user) {
        String realName = user.getRealName();
        String phone = user.getPhone();
        Integer isActive = user.getIsActive();
        String searchValue = user.getSearchValue();
        if (StrUtil.isNotEmpty(searchValue)) {
            q.and(wrapper -> wrapper.like(User::getPhone, searchValue)
                    .or().like(User::getRealName, searchValue)
                    .or().like(User::getNickName, searchValue));
        }
        q.like(StrUtil.isNotEmpty(realName), User::getRealName, realName);
        q.like(StrUtil.isNotEmpty(phone), User::getPhone, phone);
        q.like(isActive != null, User::getIsActive, isActive);
        q.orderByDesc(User::getId);
    }

    public User findByUserId(Integer userId) {
        User user = this.userMapper.selectById(userId);
        if (user == null) {
            throw new CustomException("用户不存在");
        }
        return user;
    }

    public void updateLock(Integer userId) {
        User user = this.userMapper.selectById(userId);
        if (user == null) {
            throw new CustomException("用户不存在");
        }

        user.setIsLock(user.getIsLock() == 1 ? 0 : 1);
        int updateCount = this.userMapper.updateById(user);
        if (updateCount <= 0) {
            throw new CustomException("修改失败");
        }
    }

    @Override
    public void updateLoginLock(Integer userId) {
        User user = this.userMapper.selectById(userId);
        if (user == null) {
            throw new CustomException("用户不存在");
        }

        user.setIsLogin(user.getIsLogin() == 1 ? 0 : 1);
        int updateCount = this.userMapper.updateById(user);
        if (updateCount <= 0) {
            throw new CustomException("修改失败");
        }
    }

    @Transactional
    public void updateAmt(Integer userId, String amt, Integer direction) {
        if (userId == null || amt == null || direction == null) {
            throw new CustomException("参数不能为空");
        }

        User user = this.userMapper.selectById(userId);
        if (user == null) {
            throw new CustomException("用户不存在");
        }
        BigDecimal amtBig = new BigDecimal(amt);
        String orderNo = "";
        TypeEnum e = null;
        if (direction == 0) {
            orderNo = SnowIdUtil.getId(OrderConstant.rechargeOrder);
            e = TypeEnum.BACKUP;
        } else {
            orderNo = SnowIdUtil.getId(OrderConstant.withdrawalOrder);
            e = TypeEnum.BACKDOWN;
        }

        String finalOrderNo = orderNo;
        AmountConsumer amountConsumer = (oldUser, newUser) -> {
            if (direction == 0) {
                userRechargeService.inMoneyByAdmin(finalOrderNo, amt, "后台充值", userId);
            } else {
                userWithdrawService.outMoneyByAdmin(finalOrderNo, amt, "后台提现", userId);
            }
        };
        //
        userAmountChangeManage.changeBalance(user.getId(), amtBig, orderNo, OrderTypeEnum.BACK, e, "", "", amountConsumer);
    }

    public void delete(Integer userId) {
        SiteAdmin siteAdmin = siteAdminService.getCurrentUser();
        log.info("管理员 {} 删除用户 {}", siteAdmin.getAdminName(), userId);

        int delChargeCount = this.iUserRechargeService.deleteByUserId(userId);
        log.info(delChargeCount > 0 ? "删除 充值 记录成功" : "删除 充值 记录失败");

        int delWithdrawCount = this.iUserWithdrawService.deleteByUserId(userId);
        log.info(delWithdrawCount > 0 ? "删除 提现 记录成功" : "删除 提现 记录失败");

        int delPositionCount = this.iUserPositionService.deleteByUserId(userId);
        log.info(delPositionCount > 0 ? "删除 持仓 记录成功" : "删除 持仓 记录失败");

        int delLogCount = this.iSiteLoginLogService.deleteByUserId(userId);
        log.info(delLogCount > 0 ? "删除 登录 记录成功" : "删除 登录 记录失败");

        int delUserCount = this.userMapper.deleteById(userId);
        if (delUserCount <= 0) {
            throw new CustomException("删除用户失败");
        }
    }


    public void authByAdmin(Integer userId, Integer state, String authMsg) {
        if (state == null || userId == null) {
            throw new CustomException("id和state不能为空");
        }

        User user = this.userMapper.selectById(userId);
        if (user == null) {
            throw new CustomException("查不到此用户");
        }

        if (state == 3) {
            if (StringUtils.isBlank(authMsg)) {
                throw new CustomException("审核失败信息必填");
            }
            user.setAuthMsg(authMsg);
        }

        user.setIsActive(state);
        String result = state == 2 ? "审核通过" : "经审核未通";
        String content = "您的实名认证审核结果:" + result;
        if (state == 3) {
            content += "原因:" + authMsg;
        }

        SiteMessage siteMessage = new SiteMessage();
        siteMessage.setUserId(user.getId());
        siteMessage.setMessageType(SiteMessage.MSG_TYPE_WAP);
        siteMessage.setStatus(1);
        siteMessage.setUserName(user.getRealName());
        siteMessage.setAddTime(new Date());
        siteMessage.setTypeName("实名审核");
        siteMessage.setContent(content);
        this.siteMessageMapper.insert(siteMessage);

        int updateCount = this.userMapper.updateById(user);
        if (updateCount <= 0) {
            throw new CustomException("审核失败");
        }
    }

    @Override
    public void setWithPwd(String withPwd) {
        if (StringUtils.isBlank(withPwd)) {
            throw new CustomException("参数不能为空");
        }

        User currentUser = this.iUserService.getCurrentUser();
        User user = new User();
        user.setId(currentUser.getId());
        user.setWithPwd(SymmetricCryptoUtil.encryptPassword(withPwd));
        int i = userMapper.updateById(user);
        if (i <= 0) {
            throw new CustomException("修改失败！");
        }
    }

    @Override
    public void updateWithPwd(String old, String newPwd) {
        if (StringUtils.isBlank(old) || StringUtils.isBlank(newPwd)) {
            throw new CustomException("参数不能为空");
        }

        // 获取当前用户
        User currentUser = this.getCurrentUser();
        if (currentUser == null) {
            throw new CustomException("请先登录");
        }

        // 检查用户是否已设置提现密码
        if (currentUser.getWithPwd() == null) {
            throw new CustomException("您尚未设置提现密码，请先设置提现密码");
        }

        // 加密输入的旧密码
        String encryptedOldPwd = SymmetricCryptoUtil.encryptPassword(old);

        // 比较加密后的旧密码与数据库中存储的密码
        if (!encryptedOldPwd.equals(currentUser.getWithPwd())) {
            throw new CustomException("旧密码不正确");
        }

        // 更新提现密码
        User user = new User();
        user.setId(currentUser.getId());
        user.setWithPwd(SymmetricCryptoUtil.encryptPassword(newPwd));
        int i = userMapper.updateById(user);
        if (i <= 0) {
            throw new CustomException("修改失败！");
        }
    }

    private UserInfoVO assembleUserInfoVO(User user) {
        UserInfoVO userInfoVO = new UserInfoVO();
        userInfoVO.setId(user.getId());
        userInfoVO.setPhone(user.getPhone());
        userInfoVO.setNickName(user.getNickName());
        userInfoVO.setRealName(user.getRealName());
        userInfoVO.setIdCard(user.getIdCard());
        userInfoVO.setIsLock(user.getIsLock());
        userInfoVO.setRegTime(user.getRegTime());
        userInfoVO.setRegIp(user.getRegIp());
        userInfoVO.setRegAddress(user.getRegAddress());
        userInfoVO.setImg1Key(user.getImg1Key());
        userInfoVO.setImg2Key(user.getImg2Key());
        userInfoVO.setIsActive(user.getIsActive());
        userInfoVO.setUserAmt(user.getUserAmt());
        userInfoVO.setAuthMsg(user.getAuthMsg());
        userInfoVO.setEnableAmt(user.getEnableAmt());
        userInfoVO.setBzjAmt(user.getBzjAmt());
        Level byId = levelService.getById(user.getLevel());
        userInfoVO.setLevelName(byId == null ? "跟单成员" : byId.getLevelName());
        userInfoVO.setYqm(user.getYqm());
        // 看看是否绑定银行卡
        UserBank bank = iUserBankService.findUserBankByUserId(user.getId());
        userInfoVO.setIsBingBank(bank != null ? 1 : 0);
        userInfoVO.setIsWithPwd(user.getWithPwd() != null ? 1 : 0);

        return userInfoVO;
    }


    @Override
    public User queryUserByYqm(String yqm) {
        LambdaQueryWrapper<User> q = new LambdaQueryWrapper<>();
        q.eq(User::getYqm, yqm);
        return this.getOne(q);
    }

    @Override
    public List<User> queryUserByKeyword(String keyword) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotEmpty(keyword)) {
            wrapper.like(User::getPhone, keyword)
                    .or().like(User::getRealName, keyword)
                    .or().like(User::getNickName, keyword);
        }
        wrapper.last("limit 30");
        return this.list(wrapper);
    }

    @Override
    public FollowIncomeVO getFollowIncome(User user) {
        FollowIncomeVO vo = new FollowIncomeVO();
        vo.setTodayFollowNum(user.getTodayFollowNum());
        vo.setTotalFollowNum(
                new BigDecimal(user.getTotalFollowNum()).add(new BigDecimal(user.getTodayFollowNum())).intValue());
        vo.setTodayIncome(user.getTodayFollowIncome().add(user.getTodayLeadIncome()));
        vo.setTotalIncome(user.getTotalFollowIncome().add(user.getTotalLeadIncome()).add(user.getTodayFollowIncome()));
        return vo;
    }

    @Override
    public TeamVO getTeam(User user) {
        TeamVO teamVO = new TeamVO();
        teamVO.setTeamTotalNum(user.getLowerNum());
        teamVO.setDirectMemberNum(this.queryDirectMemberNum(user.getId()));
        teamVO.setThreeNoFollowNum(this.getThreeNoFollowNum(user.getId()).intValue());
        teamVO.setTodayFirstChargeNum(this.queryTodayFirstChargeNum(user.getId()));
        return teamVO;
    }

    @Override
    public PageInfo<TeamInfoVO> getTeamInfo(User user, TeamInfoDTO dto) {
        Integer type = dto.getType();
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<User> users = CollectionUtil.newArrayList();

        if (type == 0) {
            users = this.queryLowerUser(user.getId());
        } else if (type == 1) {
            users = this.queryDirectMember(user.getId());
        } else if (type == 2) {
            users = this.getThreeNoFollow(user.getId());
        } else {
            users = this.queryTodayFirstCharge(user.getId());
        }

        ArrayList<TeamInfoVO> arr = CollectionUtil.newArrayList();
        users.forEach(e -> {
            TeamInfoVO vo = new TeamInfoVO();
            vo.setUserName(e.getNickName());
            vo.setPhone(e.getPhone());
            vo.setCreateTime(e.getRegTime());
            arr.add(vo);
        });

        PageInfo<User> pageInfo = new PageInfo<>(users);
        PageInfo<TeamInfoVO> voPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, voPageInfo, "list");
        voPageInfo.setList(arr);
        return voPageInfo;
    }

    private Long getThreeNoFollowNum(Integer id) {
        Date date = DateUtils.addDateDays(new Date(), -3);
        Date startTime = DateUtils.formatStart(date);
        LambdaQueryWrapper<User> q = new LambdaQueryWrapper<>();
        q.isNotNull(User::getId)
                .and(wrapper -> wrapper.isNull(User::getFollowTime).or().lt(User::getFollowTime, startTime))
                .last("and find_in_set(" + id + ",p_path)");
        return this.count(q);
    }

    private List<User> getThreeNoFollow(Integer id) {
        Date date = DateUtils.addDateDays(new Date(), -3);
        Date startTime = DateUtils.formatStart(date);
        LambdaQueryWrapper<User> q = new LambdaQueryWrapper<>();
        q.isNotNull(User::getId)
                .and(wrapper -> wrapper.isNull(User::getFollowTime).or().lt(User::getFollowTime, startTime))
                .last("and find_in_set(" + id + ",p_path)");
        return this.list(q);
    }

    private List<User> queryTodayFirstCharge(Integer id) {
        LambdaQueryWrapper<User> q = new LambdaQueryWrapper<>();
        q.eq(User::getIsTodayRecharge, 1)
                .isNotNull(User::getId)
                .last("and find_in_set(" + id + ",p_path)");
        return this.list(q);
    }

    private Long queryTodayFirstChargeNum(Integer id) {
        LambdaQueryWrapper<User> q = new LambdaQueryWrapper<>();
        q.eq(User::getIsTodayRecharge, 1)
                .isNotNull(User::getId)
                .last("and find_in_set(" + id + ",p_path)");
        return this.count(q);
    }
    @Override
    public List<User> queryLowerUser(Integer id) {
        LambdaQueryWrapper<User> q = new LambdaQueryWrapper<>();
        q.isNotNull(User::getId)
                .last("and find_in_set(" + id + ",p_path)");
        return this.list(q);
    }

    @Override
    public void setFirstFollowInfo(Integer userId, Date endTime) {
        userMapper.setFirstFollowInfo(userId, endTime);
    }

    private List<User> queryDirectMember(Integer id) {
        LambdaQueryWrapper<User> q = new LambdaQueryWrapper<>();
        q.eq(User::getPid, id);
        return this.list(q);
    }

    private Long queryDirectMemberNum(Integer id) {
        LambdaQueryWrapper<User> q = new LambdaQueryWrapper<>();
        q.eq(User::getPid, id);
        return this.count(q);
    }

    @Override
    public void addTodayFollowBuy(Integer userId) {
        userMapper.addTodayFollowBuy(userId);
    }

    @Override
    public void addTodayFollowSell(Integer userId, BigDecimal profit) {
        userMapper.addTodayFollowSell(userId, profit);
    }

    @Override
    public List<User> queryAllLowerLevelTask(Integer userId) {
        return userMapper.queryAllLowerLevelTask(userId);
    }

    @Override
    public List<User> queryAllLowerEfficientTask(Integer userId) {
        return userMapper.queryAllLowerEfficientTask(userId);
    }

    @Override
    public void addLeadEarnAmount(Integer userId, BigDecimal amount) {
        userMapper.addLeadEarnAmount(userId, amount);
    }

    public User getCurrentUser() {
        Integer loginId = StpUserUtil.getUserId();
        return this.userMapper.selectById(loginId);
    }

    @Override
    public void transferToEnable(BigDecimal amount) {
        User currentUser = this.getCurrentUser();
        String orderNo = SnowIdUtil.getId(OrderConstant.transferFromBank);
        userAmountChangeManage.changeBalance(currentUser.getId(), amount, orderNo, OrderTypeEnum.INVESTMENT_ADVISOR,
                TypeEnum.BANK_TO_STOCK, "", "", null);
    }

    @Override
    public void transferToTotal(BigDecimal amount) {
        User currentUser = this.getCurrentUser();
        String orderNo = SnowIdUtil.getId(OrderConstant.transferToBank);
        userAmountChangeManage.changeBalance(currentUser.getId(), amount, orderNo, OrderTypeEnum.INVESTMENT_ADVISOR,
                TypeEnum.STOCK_TO_BANK, "", "", null);
    }

    @Override
    public UserAgentInfoVO getUserAgentInfoVO() {
        UserAgentInfoVO vo = new UserAgentInfoVO();
        User currentUser = this.getCurrentUser();
        vo.setCommission(currentUser.getTotalLeadIncome().add(currentUser.getTodayLeadIncome()));
        vo.setLowNum(currentUser.getLowerNum());
        return vo;
    }


}
