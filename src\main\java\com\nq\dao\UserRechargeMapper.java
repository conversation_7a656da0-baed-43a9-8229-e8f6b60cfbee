package com.nq.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nq.pojo.UserPosition;
import com.nq.pojo.UserRecharge;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
public interface UserRechargeMapper  extends BaseMapper<UserRecharge> {

  int checkInMoney(@Param("status") int paramInt, @Param("userId") Integer paramInteger);



  int deleteByUserId(@Param("userId") Integer paramInteger);
  

  BigDecimal CountChargeSumAmt(Integer paramInteger);

  BigDecimal CountTotalRechargeAmountByTime(Integer paramInteger);
}
