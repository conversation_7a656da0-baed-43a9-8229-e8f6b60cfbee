package com.nq.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.dto.common.CommonPage;
import com.nq.pojo.AgentUser;
import com.nq.vo.agent.AgentInfoVO;
import com.nq.vo.agent.AgentLoginResultVO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface IAgentUserService extends IService<AgentUser> {
    AgentUser getCurrentAgent();

    //agentPhone, agentPwd, verifyCode, httpServletRequest
    AgentLoginResultVO login(String agentPhone, String agentPwd, String verifyCode, HttpServletRequest paramHttpServletRequest);

   //oldPwd, newPwd, request
    void updatePwd(String oldPwd, String newPwd);


    //realName, phone, pageNum, pageSize, id, request
    PageInfo<AgentUser> listByAdmin(AgentUser agentUser, CommonPage commonPage);

    void add(AgentUser agentUser);

    void update(AgentUser agentUser);



    void delAgent(Integer agentId);
}
