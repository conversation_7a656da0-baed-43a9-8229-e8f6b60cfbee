package com.nq.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderTypeEnum {
    RECHARGE(1, "充值"),
    WITHDRAW(2, "提现"),
    STOCK(3, "股票"),
    INVESTMENT_ADVISOR(4, "投顾"),
    ACCOUNT_TRANSFER(5, "账户转账"),
    TEAM_SALARY(6, "团队工资"),
    BONUS(7, "奖金"),
    BACK(8, "后台");

    private final Integer code;
    private final String desc;

    public static OrderTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OrderTypeEnum value : OrderTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 