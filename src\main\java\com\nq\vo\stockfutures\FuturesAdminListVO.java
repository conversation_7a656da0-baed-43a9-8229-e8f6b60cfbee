
package com.nq.vo.stockfutures;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
@Data
public class FuturesAdminListVO {
    private Integer id;

    private String futuresName;

    private String futuresCode;

    private String futuresGid;

    private String futuresUnit;

    private Integer futuresStandard;

    private String coinCode;

    private Integer homeShow;

    private Integer listShow;

    private Integer depositAmt;

    private Integer transFee;

    private Integer minNum;

    private Integer maxNum;

    private Integer transState;

    private String transAmBegin;

    private String transAmEnd;

    private String transPmBegin;

    private String transPmEnd;

    private Date addTime;

    private String tDesc;

    private String now_price;

    private String last_close;

    private BigDecimal coinRate;

    private String transPmBegin2;

    private String transPmEnd2;
    /*每点浮动价格*/
    private BigDecimal eachPoint;


}

