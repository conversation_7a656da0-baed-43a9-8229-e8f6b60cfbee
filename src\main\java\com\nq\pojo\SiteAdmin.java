package com.nq.pojo;

import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;

import java.util.Date;
@Data
@ApiModel(description = "系统管理员信息")
@AllArgsConstructor
@NoArgsConstructor
public class SiteAdmin extends BaseEntity {
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "管理员用户名", example = "admin")
    private String adminName;

    @ApiModelProperty(value = "管理员密码", example = "******")
    private String adminPwd;

    @ApiModelProperty(value = "管理员手机号", example = "13800138000")
    private String adminPhone;

    @ApiModelProperty(value = "是否锁定：1-锁定，0-未锁定", example = "0")
    private Integer isLock;

    @ApiModelProperty(value = "添加时间", example = "2024-01-01 12:00:00")
    private Date addTime;




}

