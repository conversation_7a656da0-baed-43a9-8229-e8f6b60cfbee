package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 新股
 * @TableName stock_subscribe
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "新股申购信息")
public class StockSubscribe extends BaseEntity implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "新股ID", example = "1")
    private Integer newlistId;

    /**
     * 新股名称
     */
    @ApiModelProperty(value = "新股名称", example = "某某科技")
    private String name;

    /**
     * 申购代码
     */
    @ApiModelProperty(value = "申购代码", example = "300001")
    private String code;
    /**
     * 类型 sh sz hk us
     */
    @ApiModelProperty(value = "股票类型：sh-上海，sz-深圳，hk-香港，us-美国", example = "sh")
    private String stockType;
    /**
     * 发行价格
     */
    @ApiModelProperty(value = "发行价格", example = "10.00")
    private BigDecimal price;

    //折扣
    @ApiModelProperty(value = "市盈率", example = "20.5")
    private String pe;

    /**
     * 发行数量
     */
    @ApiModelProperty(value = "发行数量", example = "1000000")
    private Long orderNumber;

    /**
     * 顯示状态
     */
    @ApiModelProperty(value = "显示状态：0-不显示，1-显示", example = "1")
    private Integer zt;
    /**
     * 鎖定状态
     */
    @ApiModelProperty(value = "锁定状态：0-未锁定，1-锁定", example = "0")
    private Integer isLock;
    /**
     * 申购日期
     */
    @ApiModelProperty(value = "申购日期", example = "2024-01-01 09:30:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date subscribeTime;
    /**
     * 上市日期
     */
    @ApiModelProperty(value = "上市日期", example = "2024-01-15 09:30:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date listDate;
    /**
     * 中签日期
     */
    @ApiModelProperty(value = "中签日期", example = "2024-01-10 09:30:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date subscriptionTime;
    /**
     * 缴费日期
     */
    @ApiModelProperty(value = "缴费日期", example = "2024-01-10 09:30:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 类型
     *1.新股申购 2.线下配售
     */
    @ApiModelProperty(value = "类型：1-新股申购，2-线下配售", example = "1")
    private Integer type;


    private static final long serialVersionUID = 1L;
}