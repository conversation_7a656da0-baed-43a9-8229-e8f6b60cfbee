package com.nq.controller.admin;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.Stock;
import com.nq.service.IStockService;
import javax.annotation.Resource;
import com.nq.vo.stock.StockAdminListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


@RestController
@RequestMapping({"/admin/stock/"})
@Api(tags = "后台-股票管理")
public class AdminStockController {
    private static final Logger log = LoggerFactory.getLogger(AdminStockController.class);

    @Resource
    private IStockService iStockService;

    //查询产品管理 所以股票信息及模糊查询
    @PostMapping({"list.do"})
    @ApiOperation(value = "股票列表", notes = "分页查询所有股票信息及模糊查询")
    public ServerResponse<PageInfo<StockAdminListVO>> list(@RequestParam(value = "showState", required = false) Integer showState, @RequestParam(value = "lockState", required = false) Integer lockState, @RequestParam(value = "code", required = false) String code, @RequestParam(value = "name", required = false) String name, @RequestParam(value = "stockPlate", required = false) String stockPlate, @RequestParam(value = "stockType", required = false) String stockType, @RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        PageInfo<StockAdminListVO> stockAdminListVOPageInfo = this.iStockService.listByAdmin(showState, lockState, code, name, stockPlate, stockType, pageNum, pageSize);
        return ServerResponse.createBySuccess(stockAdminListVOPageInfo);
    }

    //修改产品管理 股票是否锁定
    @PostMapping({"updateLock.do"})
    @ApiOperation(value = "修改股票锁定状态", notes = "锁定或解锁指定股票")
    public ServerResponse<String> updateLock(Integer stockId) {
        this.iStockService.updateLock(stockId);
        return ServerResponse.createBySuccess();
    }

    //修改产品管理 股票状态
    @PostMapping({"updateShow.do"})
    @ApiOperation(value = "修改股票显示状态", notes = "显示或隐藏指定股票")
    public ServerResponse<String> updateShow(Integer stockId) {
        this.iStockService.updateShow(stockId);
        return ServerResponse.createBySuccess();
    }

    //添加产品管理 股票信息
    @PostMapping({"add.do"})
    @ApiOperation(value = "添加股票", notes = "添加新的股票信息")
    public ServerResponse<String> add(@RequestParam(value = "stockName", required = false) String stockName, @RequestParam(value = "stockCode", required = false) String stockCode, @RequestParam(value = "stockType", required = false) String stockType, @RequestParam(value = "stockPlate", required = false) String stockPlate, @RequestParam(value = "isLock", required = false) Integer isLock, @RequestParam(value = "isShow", required = false) Integer isShow) {
        this.iStockService.addStock(stockName, stockCode, stockType, stockPlate, isLock, isShow);
        return ServerResponse.createBySuccess();
    }

    //修改票信息
    @RequestMapping({"updateStock.do"})
    @ApiOperation(value = "修改股票信息", notes = "根据股票ID修改股票信息")
    public ServerResponse<String> updateStock(Stock model) {
        this.iStockService.updateStock(model);
        return ServerResponse.createBySuccess();
    }

    //删除股票
    @PostMapping({"delStock.do"})
    @ApiOperation(value = "删除股票", notes = "根据股票ID删除股票")
    public ServerResponse<String> deleteByPrimaryKey(@RequestParam("id") Integer id) {
        this.iStockService.deleteByPrimaryKey(id);
        return ServerResponse.createBySuccess();
    }
    @PostMapping({"getSingleStock.do"})
    @ApiOperation(value = "查询某个股票", notes = "查询某个股票")
    public ServerResponse<Map> getSingleStock(@RequestParam("code") String code) {
        Map singleStock = this.iStockService.getSingleStock(code);
        return ServerResponse.createBySuccess(singleStock);
    }

}