package com.nq.pojo;

import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

@Data
@ApiModel(description = "首页弹窗配置")
public class SitePopup extends BaseEntity {
    @ApiModelProperty("主键ID")
    private Integer id;

    @ApiModelProperty("弹窗图片URL")
    private String popupImg;

    @ApiModelProperty("状态：0-关闭，1-开启")
    private Integer status;

    @ApiModelProperty("开启时间")
    private Date startTime;

    @ApiModelProperty("关闭时间")
    private Date endTime;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;
} 