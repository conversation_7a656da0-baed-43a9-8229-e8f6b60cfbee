package com.nq.utils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.lettuce.core.RedisFuture;
import io.lettuce.core.dynamic.batch.CommandBatching;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.*;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * redis工具类
 *
 * <AUTHOR>
 * @date 2021/08/03
 */

@Data
@Slf4j
@Component
public class RedisUtil {
    private static final RedissonClient CLIENT = SpringUtils.getBean(RedissonClient.class);
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    private boolean isCluster = false;

    //代付订单提交间隔时间
    public static Long AGENT_PAY_ORDER_TIME = 600L;
    //支付订单-订单号幂等时间
    public static Long PAY_ORDER_POWER_TIME = 86400L;
    //通知 支付结果 幂等控制时间
    public static Long NOTIFY_ORDER_TIME = 86400L;



    /**
     * 如果不存在则设置 并返回 true 如果存在则返回 false
     *
     * @param key   缓存的键值
     * @param value 缓存的值
     * @return set成功或失败
     */
    public static <T> boolean setObjectIfAbsent(final String key, final T value, final Duration duration) {
        RBucket<T> bucket = CLIENT.getBucket(key);
        return bucket.trySet(value, duration.toMillis(), TimeUnit.MILLISECONDS);
    }
    /**
     * 删除单个对象
     *
     * @param key 缓存的键值
     */
    public static boolean deleteObject(final String key) {
        return CLIENT.getBucket(key).delete();
    }
    /**
     * 将 Object 值放入 List 缓存 (using default Object serialization)
     *
     * @param key   键
     * @param value 值
     */
    public boolean lSet(String key, Object value) {
        try {
            redisTemplate.opsForList().rightPush(key, value);
            return true;
        } catch (Exception e) {
            log.error("Error in lSet for key: {}, value: {}", key, value, e);
            return false;
        }
    }

    /**
     * 将 Object 值放入 List 缓存并设置过期时间 (using default Object serialization)
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒)
     */
    public boolean lSet(String key, Object value, long time) {
        try {
            redisTemplate.opsForList().rightPush(key, value);
            if (time > 0)
                expire(key, time);
            return true;
        } catch (Exception e) {
            log.error("Error in lSet with time for key: {}, value: {}", key, value, e);
            return false;
        }
    }

    /**
     * 将 String 值放入 List 缓存
     *
     * @param key   键
     * @param value String 值
     */
    public boolean lSetString(String key, String value) {
        try {
            stringRedisTemplate.opsForList().rightPush(key, value);
            return true;
        } catch (Exception e) {
            log.error("Error in lSetString for key: {}, value: {}", key, value, e);
            return false;
        }
    }

    /**
     * 将 String 列表批量放入 List 缓存 (更高效)
     *
     * @param key    键
     * @param values String 值的列表
     */
    public boolean lPushAllString(String key, List<String> values, long time) {
        if (values == null || values.isEmpty()) {
            return false;
        }
        try {
            // rightPushAll returns the size of the list after the push operation
            Long result = stringRedisTemplate.opsForList().rightPushAll(key, values);
            stringRedisTemplate.expire(key,time, TimeUnit.SECONDS);
            return result != null && result > 0;
        } catch (Exception e) {
            log.error("Error in lPushAllString for key: {}, number of values: {}", key, values.size(), e);
            return false;
        }
    }

    /**
     * 获取 List<String> 缓存指定范围的内容
     *
     * @param key 键
     * @return List<String> 列表, 如果 key 不存在或发生错误则返回空的 List
     */
    public List<String> getListStringRange(String key) {
        try {
            List<String> result = stringRedisTemplate.opsForList().range(key, 0, -1);
            return result != null ? result : Collections.emptyList();
        } catch (Exception e) {
            log.error("Error getting string list range for key: {}, start: {}, end: {}", key, 0, -1, e);
            return Collections.emptyList();
        }
    }

    /**
     * 指定缓存失效时间
     *
     * @param key  键
     * @param time 时间(秒)
     */
    public boolean expire(String key, long time) {
        try {
            if (time > 0) {
                // Use redisTemplate here as expire works on any key type
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception e) {
            log.error("Error setting expire for key: {}", key, e);
            return false;
        }
    }

    /**
     * 获取 List<String> 缓存的内容
     *
     * @param key 键
     * @return List<String> 列表
     */
    public List<String> lGetListString(String key, long start, long end) {
        try {
            return stringRedisTemplate.opsForList().range(key, start, end);
        } catch (Exception e) {
            log.error("Error getting string list for key: {}", key, e);
            return null;
        }
    }

    // Method to get all elements from a string list
    public List<String> lGetAllListString(String key) {
        return lGetListString(key, 0, -1);
    }

    /**
     * 删除缓存
     *
     * @param key 可以传一个值 或多个
     */
    public void del(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                redisTemplate.delete(key[0]);
            } else {
                redisTemplate.delete(CollectionUtils.arrayToList(key));
            }
        }
    }

    /**
     * 是否存在key
     */
    public Boolean exists(String key) {
        return stringRedisTemplate.hasKey(key);
    }


}
