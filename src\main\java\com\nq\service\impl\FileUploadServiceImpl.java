package com.nq.service.impl;

import com.google.common.collect.Lists;
import com.nq.common.ServerResponse;
import com.nq.prop.MinioProperties;
import com.nq.service.IFileUploadService;

import java.io.File;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;


@Service("iFileUploadService")

public class FileUploadServiceImpl

        implements IFileUploadService {

    private static final Logger log = LoggerFactory.getLogger(FileUploadServiceImpl.class);

    @Resource
    private MinioProperties minioProps;
    @Resource
    private MinioService minioSerivce;

    @Override
    public String upload(MultipartFile file) {
        try {
            // 上传
            String fileName = minioSerivce.upload(minioProps.getBucketName(), "file", file);
            if (StringUtils.isEmpty(fileName)) {
                throw new RuntimeException("文件上传失败");
            }
            return fileName;
        } catch (Exception e) {
            log.error("上传文件异常 , 错误信息 = {}", e);
            throw new RuntimeException("文件上传失败");
        }
    }


    @Override
    public String generUrl(String fileName) {
        return minioProps.getDomain() + "/" + minioProps.getBucketName() + "/file/" + fileName;
    }

    @Override
    public String generUri(String fileName) {
        return "/" + minioProps.getBucketName() + "/file/" + fileName;
    }

}
