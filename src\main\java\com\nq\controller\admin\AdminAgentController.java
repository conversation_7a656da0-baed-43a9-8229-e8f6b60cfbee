package com.nq.controller.admin;


import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dto.common.CommonPage;
import com.nq.pojo.AgentUser;
import com.nq.service.IAgentUserService;
import com.nq.service.IUserPositionService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.nq.vo.agent.AgentIncomeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping({"/admin/agent/"})
@Api(tags = "后台-代理商管理")
public class AdminAgentController {
    private static final Logger log = LoggerFactory.getLogger(AdminAgentController.class);

    @Resource
    private IAgentUserService iAgentUserService;



    //分页查询代理管理 所有代理信息 及模糊查询
    @PostMapping({"list.do"})
    @ApiOperation("分页查询代理管理信息")
    public ServerResponse<PageInfo<AgentUser>> list(AgentUser agentUser, CommonPage commonPage) {
        PageInfo<AgentUser> agentUserPageInfo = this.iAgentUserService.listByAdmin(agentUser, commonPage);
        return ServerResponse.createBySuccess(agentUserPageInfo);
    }

    //添加代理管理 下级代理
    @RequestMapping({"add.do"})
    @ApiOperation("添加下级代理")
    public ServerResponse<String> add(AgentUser agentUser) {
        this.iAgentUserService.add(agentUser);
        return ServerResponse.createBySuccess();
    }

    //修改代理管理 代理信息
    @RequestMapping({"update.do"})
    @ApiOperation("修改代理信息")
    public ServerResponse<String> update(AgentUser agentUser) {
        this.iAgentUserService.update(agentUser);
        return ServerResponse.createBySuccess();
    }




    //删除代理
    @RequestMapping({"delAgent.do"})
    @ApiOperation("删除代理")
    public ServerResponse<String> delAgent(Integer agentId) {
        this.iAgentUserService.delAgent(agentId);
        return ServerResponse.createBySuccess();
    }
}
