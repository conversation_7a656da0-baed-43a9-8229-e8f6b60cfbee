/**
 * Copyright (c) 2016-2019 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package com.nq.job.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.job.entity.ScheduleJobEntity;

import java.util.Map;

/**
 * 定时任务
 *
 * @<NAME_EMAIL>
 */
public interface ScheduleJobService extends IService<ScheduleJobEntity> {

	PageInfo<ScheduleJobEntity> queryPage(Map<String, Object> params);

	/**
	 * 保存定时任务
	 */
	void saveJob(ScheduleJobEntity scheduleJob);

	/**
	 * 更新定时任务
	 */
	void update(ScheduleJobEntity scheduleJob);

	/**
	 * 批量删除定时任务
	 */
	void deleteBatch(String[] jobIds);

	/**
	 * 批量更新定时任务状态
	 */
	int updateBatch(String[] jobIds, int status);

	/**
	 * 立即执行
	 */
	void run(String[] jobIds);

	/**
	 * 暂停运行
	 */
	void pause(String[] jobIds);

	/**
	 * 恢复运行
	 */
	void resume(String[] jobIds);
}
