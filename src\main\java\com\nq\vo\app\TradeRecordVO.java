package com.nq.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(description = "交易记录展示对象")
public class TradeRecordVO {

    @ApiModelProperty(value = "总收益", example = "175.83")
    private BigDecimal totalProfit;

    @ApiModelProperty(value = "交易记录列表")
    private List<TradeDetailVO> tradeList;


} 