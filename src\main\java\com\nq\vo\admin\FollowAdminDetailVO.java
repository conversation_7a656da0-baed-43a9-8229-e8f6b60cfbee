package com.nq.vo.admin;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@ApiModel(description = "跟单详情展示对象")
public class FollowAdminDetailVO {
    
    @ApiModelProperty(value = "跟单ID", example = "1")
    private Integer id;
    
    @ApiModelProperty(value = "跟单单号", example = "FD202403010001")
    private String followNo;
    

    
    @ApiModelProperty(value = "导师名称", example = "李四")
    private String mentorName;
    

    @ApiModelProperty(value = "股票代码", example = "603716")
    private String stockCode;

    @ApiModelProperty(value = "股票名称", example = "塞力医疗")
    private String stockName;

    @ApiModelProperty(value = "买入价格", example = "10.50")
    private BigDecimal buyPrice;
    
    @ApiModelProperty(value = "卖出价格", example = "11.20")
    private BigDecimal sellPrice;
    
    @ApiModelProperty(value = "佣金比例", example = "40.00")
    private BigDecimal commissionRate;
    
    @ApiModelProperty(value = "买入数量", example = "100")
    private Integer buyQuantity;
    
    @ApiModelProperty(value = "个股盈亏百分比", example = "6.67")
    private BigDecimal profitLossPercent;
    
    @ApiModelProperty(value = "盈亏金额", example = "150.50")
    private BigDecimal profit;
    

    

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;
    
    /**
     * 状态(0-未结算 1-已结算)
     */
    @ApiModelProperty(value = "状态(0-未结算 1-已结算)", example = "0")
    private Integer settlementStatus;
    
    @ApiModelProperty(value = "状态描述", example = "跟单中")
    private String statusDesc;
    
    /**
     * 持仓状态(1-跟单中，2-已清仓)
     */
    @ApiModelProperty(value = "持仓状态(1-跟单中，2-已清仓)", example = "1")
    private Integer status;
    
    @ApiModelProperty(value = "持仓状态描述", example = "跟单中")
    private String positionStatusDesc;
    
    @ApiModelProperty(value = "跟单金额", example = "30000.00")
    private BigDecimal amount;


    @ApiModelProperty(value = "套餐类型(1-普通，2-套餐)", example = "1")
    private Integer packageType;
    @ApiModelProperty(value = "用户信息")
    private UserInfo userInfo;
    @ApiModelProperty(value = "导师信息")
    private MentorInfo mentorInfo;
    @ApiModelProperty(value = "股票信息")
    private stockInfo stockInfo;

    @ApiModelProperty(value = "结算时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date settlementTime;

    @Data
    @ApiModel(description = "跟单会员信息")
    public static class UserInfo {
        @ApiModelProperty(value = "用户账号", example = "aa0003")
        private String userAccount;

        @ApiModelProperty(value = "用户姓名", example = "张三")
        private String userName;

        @ApiModelProperty(value = "用户手机号", example = "***********")
        private String userPhone;

        @ApiModelProperty(value = "金额", example = "500")
        private BigDecimal amount;
    }

    @Data
    @ApiModel(description = "导师信息")
    public  static class MentorInfo {
        @ApiModelProperty(value = "导师账号", example = "mentor001")
        private String mentorAccount;

        @ApiModelProperty(value = "导师姓名", example = "王翔鹏")
        private String mentorName;

        @ApiModelProperty(value = "导师手机号", example = "***********")
        private String mentorPhone;

        @ApiModelProperty(value = "跟单金额最低", example = "5000.00")
        private BigDecimal minAmount;

        @ApiModelProperty(value = "跟单金额最高", example = "30000.00")
        private BigDecimal maxAmount;

        @ApiModelProperty(value = "佣金比例", example = "30000.00")
        private BigDecimal salaryRate;

    }

    @Data
    @ApiModel(description = "股票信息")
    public  static class stockInfo {
        @ApiModelProperty(value = "股票code", example = "股票code")
        private String stockCode;

        @ApiModelProperty(value = "股票名称", example = "股票名称")
        private String stockName;

        @ApiModelProperty(value = "买入数量", example = "1")
        private Integer buyQuantity;

        @ApiModelProperty(value = "卖出数量", example = "5000.00")
        private Integer sellQuantity;

        @ApiModelProperty(value = "现有数量", example = "5000.00")
        private Integer currentQuantity;

        @ApiModelProperty(value = "买入单价", example = "30000.00")
        private BigDecimal buyPrice;
        @ApiModelProperty(value = "卖出单价", example = "30000.00")
        private BigDecimal sellPrice;
        @ApiModelProperty(value = "买入金额", example = "30000.00")
        private BigDecimal buyAmount;
        @ApiModelProperty(value = "卖出金额", example = "30000.00")
        private BigDecimal sellAmount;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date buyTime;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date sellTime;
        @ApiModelProperty(value = "投顾收益", example = "30000.00")
        private BigDecimal advisorProfit =BigDecimal.ZERO;
        @ApiModelProperty(value = "投顾收益%", example = "30000.00")
        private BigDecimal advisorProfitRate =BigDecimal.ZERO;
    }
} 