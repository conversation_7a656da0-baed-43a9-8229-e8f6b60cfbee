package com.nq.common.interceptor;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.nq.common.ServerResponse;
import com.nq.service.IUserService;
import com.nq.utils.redis.JsonUtil;
import jdk.nashorn.internal.ir.annotations.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.lang.annotation.Annotation;
import java.util.Map;

import cn.dev33.satoken.exception.NotLoginException;
import com.nq.common.satoken.StpUserUtil;
import org.springframework.beans.factory.annotation.Autowired;
import com.nq.pojo.User;

@Component
public class ApiUserAuthorityInterceptor implements HandlerInterceptor {
    private static final Logger log = LoggerFactory.getLogger(ApiUserAuthorityInterceptor.class);

    @Resource
    private IUserService userService;

    public ApiUserAuthorityInterceptor() {
    }

    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object handler) throws Exception {
        if (HttpMethod.OPTIONS.toString().equals(httpServletRequest.getMethod())) {
            return true;
        }
        String url = httpServletRequest.getRequestURI();
        if ("/user/upload.do".equals(url)) {
            return true;
        }
        if ("/user/pay.do".equals(url)) {
            return true;
        }
        if ("/user/newStockList.do".equals(url)) {
            return true;
        }
        if ("/user/newStockBuy.do".equals(url)) {
            return true;
        }
        if ("/user/buchahbds.do".equals(url)) {
            return true;
        }

        try {
            // 使用 Sa-Token 验证用户权限，指定账号类型为 user
            StpUserUtil.checkLogin();
            // 获取当前登录用户ID
            Long userId = StpUserUtil.getLoginIdAsLong();
            // 查询用户信息
            User user = userService.getById(userId);
            if (user != null && user.getIsLock() != null && user.getIsLock() == 1) {
                httpServletResponse.setCharacterEncoding("UTF-8");
                httpServletResponse.setContentType("application/json;charset=UTF-8");
                PrintWriter writer = httpServletResponse.getWriter();
                Map<String, Object> map = Maps.newHashMap();
                map.put("success", Boolean.FALSE);
                map.put("status", -10);
                map.put("msg", "账号已锁定，请联系管理员");
                writer.print(JsonUtil.obj2String(map));
                writer.flush();
                writer.close();
                return false;
            }
            return true;
        } catch (NotLoginException e) {
            httpServletResponse.setCharacterEncoding("UTF-8");
            httpServletResponse.setContentType("application/json;charset=UTF-8");
            PrintWriter writer = httpServletResponse.getWriter();
            Map<String, Object> map = Maps.newHashMap();
            map.put("success", Boolean.FALSE);
            map.put("status",-1);
            map.put("msg", "请先登录，无权限访问");
            writer.print(JsonUtil.obj2String(map));
            writer.flush();
            writer.close();
            return false;
        }
    }
    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object handler, ModelAndView modelAndView) throws Exception {
    }


    private Boolean responseWrite(HttpServletResponse response, String resultMSG) throws Exception {
        //throw new BaseException(resultCode);
//        定义返回类型为JSON
        response.setContentType("application/json;charset=UTF-8");
//        获取PrintWriter
        PrintWriter out = response.getWriter();
        //将异常类型写入
        ServerResponse<Object> byErrorMsg = ServerResponse.createByErrorMsg(resultMSG);
        out.write(JSON.toJSONString(byErrorMsg));
        //输出流
        out.flush();
        //关闭请求
        out.close();
        return true;
    }


    /**
     * 获取接口上的注解
     *
     * @param handler        请求方法
     * @param annotationType 想要获取的注解
     * @param <T>            想要获取的注解类型
     * @return 注解
     */
    private <T extends Annotation> T findAnnotation(HandlerMethod handler, Class<T> annotationType) {
        T annotation = handler.getBeanType().getAnnotation(annotationType);
        if (annotation != null) {
            return annotation;
        }
        return handler.getMethodAnnotation(annotationType);
    }


}
