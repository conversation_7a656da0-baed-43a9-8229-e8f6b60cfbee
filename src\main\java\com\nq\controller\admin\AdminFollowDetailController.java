package com.nq.controller.admin;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dto.common.CommonPage;
import com.nq.pojo.FollowDetail;
import com.nq.service.FollowDetailService;
import com.nq.vo.admin.FollowAdminDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "后台-跟单详情管理")
@RestController
@RequestMapping("/admin/follow/detail/")
public class AdminFollowDetailController {

    @Resource
    private FollowDetailService followDetailService;

    @ApiOperation("分页查询跟单详情列表")
    @PostMapping("list.do")
    public ServerResponse<PageInfo<FollowAdminDetailVO>> pageList(FollowDetail followDetail, CommonPage commonPage) {
        PageInfo<FollowAdminDetailVO>  voPageInfo =  followDetailService.pageList(followDetail,commonPage,false);
        return ServerResponse.createBySuccess(voPageInfo);
    }

    @ApiOperation("获取跟单详情")
    @PostMapping("detail.do")
    public ServerResponse<FollowAdminDetailVO> getDetail(@RequestBody FollowDetail followDetail) {
        FollowAdminDetailVO vo = followDetailService.getDetail(followDetail.getId());
        return ServerResponse.createBySuccess(vo);
    }

    @ApiOperation("创建跟单详情")
    @PostMapping("create.do")
    public ServerResponse<String> create(@RequestBody FollowDetail followDetail) {
        return followDetailService.create(followDetail);
    }

    @ApiOperation("更新跟单详情")
    @PostMapping("update.do")
    public ServerResponse<String> update(@RequestBody FollowDetail followDetail) {
        return followDetailService.update(followDetail);
    }

    @ApiOperation("结算跟单")
    @PostMapping("settle.do")
    public ServerResponse<String> settle(@RequestBody FollowDetail followDetail) {
        return followDetailService.settle(followDetail.getId());
    }
} 