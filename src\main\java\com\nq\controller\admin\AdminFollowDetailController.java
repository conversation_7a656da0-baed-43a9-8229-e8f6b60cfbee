package com.nq.controller.admin;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.FollowDetail;
import com.nq.service.FollowDetailService;
import com.nq.vo.admin.FollowAdminDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "后台-跟单详情管理")
@RestController
@RequestMapping("/admin/follow/detail/")
public class AdminFollowDetailController {

    @Resource
    private FollowDetailService followDetailService;

    @ApiOperation("分页查询跟单详情列表")
    @GetMapping("list.do")
    public ServerResponse<PageInfo<FollowAdminDetailVO>> pageList(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页数量") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam(value = "关键字 (用户姓名/手机号/账号)", example = "000001") @RequestParam(required = false) String keyword,
            @ApiParam(value = "状态(1-持仓中，2-已清仓)", example = "1") @RequestParam(required = false) Integer status,
            @ApiParam(value = "结算状态(0-未结算 1-已结算)", example = "1") @RequestParam(required = false) Integer settleStatus) {
        PageInfo<FollowAdminDetailVO>  voPageInfo =  followDetailService.pageList(pageNum, pageSize, keyword, status,
                settleStatus);
        return ServerResponse.createBySuccess(voPageInfo);
    }

    @ApiOperation("获取跟单详情")
    @PostMapping("detail.do")
    public ServerResponse<FollowAdminDetailVO> getDetail(@RequestBody FollowDetail followDetail) {
        FollowAdminDetailVO vo = followDetailService.getDetail(followDetail.getId());
        return ServerResponse.createBySuccess(vo);
    }

    @ApiOperation("创建跟单详情")
    @PostMapping("create.do")
    public ServerResponse<String> create(@RequestBody FollowDetail followDetail) {
        return followDetailService.create(followDetail);
    }

    @ApiOperation("更新跟单详情")
    @PostMapping("update.do")
    public ServerResponse<String> update(@RequestBody FollowDetail followDetail) {
        return followDetailService.update(followDetail);
    }

    @ApiOperation("结算跟单")
    @PostMapping("settle.do")
    public ServerResponse<String> settle(@RequestBody FollowDetail followDetail) {
        return followDetailService.settle(followDetail.getId());
    }
} 