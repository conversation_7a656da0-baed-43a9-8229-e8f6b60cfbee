<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nq.dao.ActivityMapper">
  <resultMap id="BaseResultMap" type="com.nq.pojo.Activity">
    <!--@mbg.generated-->
    <!--@Table activity-->
    <id column="activity_id" jdbcType="INTEGER" property="activityId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="award" jdbcType="DECIMAL" property="award" />
    <result column="target" jdbcType="INTEGER" property="target" />
    <result column="weight" jdbcType="INTEGER" property="weight" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    activity_id, `type`, `name`, award, target, weight, `status`, create_time, create_by, 
    update_time, update_by
  </sql>
</mapper>