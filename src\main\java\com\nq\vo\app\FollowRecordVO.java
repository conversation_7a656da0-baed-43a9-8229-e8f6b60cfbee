package com.nq.vo.app;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(description = "投顾标的记录VO")
public class FollowRecordVO {

    @ApiModelProperty(value = "id", example = "1")
    private Integer id;
    @ApiModelProperty(value = "单号", example = "GP202412290016427613")
    private String followNo;

    @ApiModelProperty(value = "投顾标的名称", example = "90日投顾标的")
    private String packageName;

    @ApiModelProperty(value = "投资者", example = "大雨1")
    private String investorName;

    @ApiModelProperty(value = "到期日期", example = "2025-05-19 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty(value = "投顾标的资金", example = "1000.00")
    private BigDecimal amount;
    @ApiModelProperty(value = "追加金额", example = "1000.00")
    private BigDecimal addAmount;
    @ApiModelProperty(value = "利润分红(%)", example = "16.00")
    private BigDecimal salaryRate;

    @ApiModelProperty(value = "申请时间", example = "2024-12-29 00:16:42")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date applyTime;

    @ApiModelProperty(value = "状态", example = "0")
    private Integer status;

    @ApiModelProperty(value = "是否可追加", example = "0")
    private Integer isAdd = 0;
} 