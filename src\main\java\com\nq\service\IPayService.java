package com.nq.service;


import com.nq.common.ServerResponse;
import com.nq.pojo.MerchantPay;

import javax.servlet.http.HttpServletRequest;

public interface IPayService {


    ServerResponse<String> recharge(MerchantPay merchantPay, String payAmt);

    ServerResponse<String> rechargeNotify(HttpServletRequest request);


    ServerResponse<String> withdraw(MerchantPay merchantPay, String payAmt);

    ServerResponse<String> withdrawNotify(HttpServletRequest request);

}
