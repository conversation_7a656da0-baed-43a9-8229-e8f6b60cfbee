<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nq.dao.PackageMapper">

    <resultMap id="BaseResultMap" type="com.nq.pojo.Package">
        <id column="id" property="id"/>
        <result column="product_name" property="productName"/>
        <result column="product_code" property="productCode"/>
        <result column="product_icon" property="productIcon"/>
        <result column="hold_days" property="holdDays"/>
        <result column="min_amount" property="minAmount"/>
        <result column="max_amount" property="maxAmount"/>
        <result column="salary_rate" property="salaryRate"/>
        <result column="sort" property="sort"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, product_name, product_code, product_icon, hold_days,
        min_amount, max_amount, salary_rate, sort, remark, create_time, update_time
    </sql>

    <!-- 可以在这里添加自定义的查询方法 -->

</mapper>