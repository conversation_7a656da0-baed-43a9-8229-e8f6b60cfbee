package com.nq.controller.admin;


import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.*;
import com.nq.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


@RestController
@RequestMapping({"/admin/log/"})
@Api(tags = "后台-日志管理")
public class AdminLogsController {
    private static final Logger log = LoggerFactory.getLogger(AdminLogsController.class);

    @Resource
    private ISiteLoginLogService iSiteLoginLogService;
    @Resource
    private ISiteSmsLogService iSiteSmsLogService;

    @Resource
    private ISiteMessageService iSiteMessageService;


    //分页查询日志管理 所有登陆日志信息
    @ApiOperation(value = "分页查询所有登陆日志信息", notes = "分页查询日志管理下所有登陆日志信息")
    @PostMapping({"loginList.do"})
    public ServerResponse<PageInfo<SiteLoginLog>> loginList(@RequestParam(value = "userId", required = false) Integer userId, @RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        PageInfo<SiteLoginLog> siteLoginLogPageInfo = this.iSiteLoginLogService.loginList(userId, pageNum, pageSize);
        return ServerResponse.createBySuccess(siteLoginLogPageInfo);
    }

    //分页查询日志管理 所有短信日志信息
    @ApiOperation(value = "分页查询所有短信日志信息", notes = "分页查询日志管理下所有短信日志信息")
    @PostMapping({"smsList.do"})
    public ServerResponse<PageInfo<SiteSmsLog>> smsList(@RequestParam(value = "phoneNum", required = false) String phoneNum, @RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        PageInfo<SiteSmsLog> siteSmsLogPageInfo = this.iSiteSmsLogService.smsList(phoneNum, pageNum, pageSize);
        return ServerResponse.createBySuccess(siteSmsLogPageInfo);
    }



    //分页查询站内消息
    @ApiOperation(value = "分页查询站内消息", notes = "分页查询站内消息")
    @PostMapping({"messageList.do"})
    public ServerResponse<PageInfo<SiteMessage>> messageList(@RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "10") int pageSize, HttpServletRequest request) {
        PageInfo<SiteMessage> siteMessageByUserIdList = this.iSiteMessageService.getSiteMessageByUserIdList(pageNum, pageSize, 999, request);
        return ServerResponse.createBySuccess(siteMessageByUserIdList);
    }

    //创建站内信
    @ApiOperation(value = "创建站内信", notes = "创建站内信")
    @PostMapping({"createSiteMessage.do"})
    public ServerResponse<String> createSiteMessage(@RequestParam(value = "userIds") String userIds, @RequestParam(value = "type") String type, @RequestParam(value = "content") String content, @RequestParam(value = "messageType") Integer messageType, HttpServletRequest request) {
         this.iSiteMessageService.createSiteMessage(userIds, type, content, messageType, request);
         return ServerResponse.createBySuccess();
    }

    //删除登陆日志
    @ApiOperation(value = "删除登陆日志", notes = "删除登陆日志")
    @PostMapping({"delLogin.do"})
    public ServerResponse<String> del(Integer id, HttpServletRequest request) {
        this.iSiteLoginLogService.del(id, request);
        return ServerResponse.createBySuccess();
    }

    //删除短信发送日志
    @ApiOperation(value = "删除短信发送日志", notes = "删除短信发送日志")
    @PostMapping({"delSms.do"})
    public ServerResponse<String> delSms(Integer id, HttpServletRequest request) {
         this.iSiteSmsLogService.del(id, request);
         return ServerResponse.createBySuccess();
    }



    //删除定时任务
    @ApiOperation(value = "删除定时任务", notes = "删除定时任务")
    @RequestMapping({"delMessageList.do"})
    public ServerResponse<String> delMessageList(Integer id, HttpServletRequest request) {
         this.iSiteMessageService.del(id, request);
         return ServerResponse.createBySuccess();
    }


}

