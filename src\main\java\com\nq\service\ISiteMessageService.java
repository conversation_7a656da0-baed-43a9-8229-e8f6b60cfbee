package com.nq.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.SiteLoginLog;
import com.nq.pojo.SiteMessage;

import javax.servlet.http.HttpServletRequest;

/**
 * 站内消息
 * <AUTHOR>
 * @date 2020/07/16
 */
public interface ISiteMessageService extends IService<SiteMessage> {
    /**
     * 新增
     */
    int insert(SiteMessage siteMessage);

    /**
     * 删除
     */
    int delete(int id);

    /**
     * 更新
     */
    int update(SiteMessage siteMessage);


    /*查询用户站内消息列表*/
    PageInfo<SiteMessage> getSiteMessageByUserIdList(int pageNum, int pageSize, int userId, HttpServletRequest request);

    /**
     * 今天该类型的站内消息是否推送过
     */
    int getIsDayCount(Integer userId, String typeName);

    /**
     * [用户站内消息状态变已读]
     *
     * <AUTHOR>
     * @date 2020/07/16
     **/
    void updateMessageStatus(HttpServletRequest request);

    /**
     * [查询用户未读消息数]
     * <AUTHOR>
     * @date 2020/07/16
     **/
    int getUnreadCount(HttpServletRequest request);

    void del(Integer id, HttpServletRequest request);

    void updateNoticeStatus(Integer noticeId, HttpServletRequest request);

    void createSiteMessage(String userIds,String type,String content,Integer messageType,HttpServletRequest request);

    void readAll();
}
