package com.nq.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.SiteBanner;

import java.util.List;

public interface ISiteBannerService  extends IService<SiteBanner> {
  void add(SiteBanner paramSiteBanner);

  PageInfo<SiteBanner> listByAdmin(int paramInt1, int paramInt2);
  
  void update(SiteBanner paramSiteBanner);
  
  void delete(Integer paramInteger);
  
  List<SiteBanner> getBannerByPlat(String paramString);
}
