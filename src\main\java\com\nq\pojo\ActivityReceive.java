package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 活动领取表
 */
@ApiModel(description = "活动领取表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "activity_receive")
public class ActivityReceive  extends BaseEntity {
    @TableId(value = "receive_id", type = IdType.AUTO)
    @ApiModelProperty(value = "")
    private Integer receiveId;

    /**
     * 订单号
     */
    @TableField(value = "order_sn")
    @ApiModelProperty(value = "订单号")
    private String orderSn;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    @ApiModelProperty(value = "用户id")
    private Integer userId;

    /**
     * 用户昵称
     */
    @TableField(value = "nick_name")
    @ApiModelProperty(value = "用户昵称")
    private String nickName;



    /**
     * 活动名称
     */
    @TableField(value = "activity_name")
    @ApiModelProperty(value = "活动名称")
    private String activityName;

    /**
     * 活动id
     */
    @TableField(value = "activity_id")
    @ApiModelProperty(value = "活动id")
    private Integer activityId;

    /**
     * 金额
     */
    @TableField(value = "amount")
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}