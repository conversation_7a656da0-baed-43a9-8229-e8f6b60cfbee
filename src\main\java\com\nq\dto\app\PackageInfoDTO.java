package com.nq.dto.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@ApiModel(description = "跟单信息")
public class PackageInfoDTO {

    @NotNull(message = "导师id不能为空")
    @ApiModelProperty(value = "导师id", required = true, example = "1")
    private Integer mentorId;
}