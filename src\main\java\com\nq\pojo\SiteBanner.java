package com.nq.pojo;

import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;
import java.beans.ConstructorProperties;
import java.util.Date;

@Data
@ApiModel(description = "网站轮播图信息")
@AllArgsConstructor
@NoArgsConstructor
public class SiteBanner extends BaseEntity {
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "轮播图URL", example = "http://example.com/banner.jpg")
    private String bannerUrl;

    @ApiModelProperty(value = "排序号", example = "1")
    private Integer isOrder;

    @ApiModelProperty(value = "是否PC端显示(0:不显示,1:显示)", example = "1")
    private Integer isPc;

    @ApiModelProperty(value = "是否移动端显示(0:不显示,1:显示)", example = "1")
    private Integer isM;

    @ApiModelProperty(value = "添加时间", example = "2024-01-01 12:00:00")
    private Date addTime;

    @ApiModelProperty(value = "轮播图标题", example = "新春特惠活动")
    private String banTitle;

    @ApiModelProperty(value = "轮播图描述", example = "参与活动赢取丰厚奖励")
    private String banDesc;

    @ApiModelProperty(value = "跳转链接", example = "http://example.com/activity")
    private String targetUrl;

}
