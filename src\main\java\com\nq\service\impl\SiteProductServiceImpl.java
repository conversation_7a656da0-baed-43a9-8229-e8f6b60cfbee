package com.nq.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nq.dao.SiteProductMapper;
import com.nq.pojo.SiteProduct;
import com.nq.service.ISiteProductService;
import com.nq.excepton.CustomException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("iSiteProductService")
public class SiteProductServiceImpl extends ServiceImpl<SiteProductMapper, SiteProduct> implements ISiteProductService {
    private static final Logger log = LoggerFactory.getLogger(SiteProductServiceImpl.class);

    @Resource
    private SiteProductMapper siteProductMapper;

    @Override
    public void update(SiteProduct siteProduct) {
        if (siteProduct.getId() == null) {
            throw new CustomException("修改id不能为空");
        }

        SiteProduct dbProduct = this.siteProductMapper.selectById(siteProduct.getId());
        if (dbProduct == null) {
            throw new CustomException("不存在产品设置记录");
        }

        int updateCount = this.siteProductMapper.updateById(siteProduct);
        if (updateCount <= 0) {
            throw new CustomException("修改失败");
        }
    }

    @Override
    public SiteProduct getProductSetting() {
        LambdaQueryWrapper<SiteProduct> q = new LambdaQueryWrapper<>();

        List<SiteProduct> list = siteProductMapper.selectList(q);
        if (list == null || list.isEmpty()) {
            throw new CustomException("未找到产品设置");
        }
        return list.get(0);
    }
}
