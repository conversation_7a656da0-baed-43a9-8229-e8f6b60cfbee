package com.nq.utils.task.follow;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nq.constant.OrderConstant;
import com.nq.enums.OrderTypeEnum;
import com.nq.enums.TypeEnum;
import com.nq.function.AmountConsumer;
import com.nq.job.task.ITask;
import com.nq.manage.UserAmountChangeManage;
import com.nq.pojo.Activity;
import com.nq.pojo.ActivityReceive;
import com.nq.pojo.User;
import com.nq.service.ActivityReceiveService;
import com.nq.service.ActivityService;
import com.nq.service.impl.UserServiceImpl;
import com.nq.utils.SnowIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component("activityTeamTask")
public class ActivityTeamTask implements ITask {

    @Resource
    private ActivityService activityService;
    @Resource
    private UserServiceImpl userService;
    @Resource
    private ActivityReceiveService activityReceiveService;
    @Resource
    private UserAmountChangeManage userAmountChangeManage;


    //    @Scheduled(cron = "0 0 12 * * ?")
    @Override
    public void run(String params) {
        log.info("开始执行日常队长任务"); // 日志记录
        //查询昨日用户达到跟单队长最小级别的用户, 然后对比用户曾经达到的最高级别, 如果现在的级别已经超过最高级别,就修改用户的最高级别,给用户发放奖励
        //查询队长任务
        List<Activity> activities = activityService.list(new LambdaQueryWrapper<Activity>().eq(Activity::getType, 1).eq(Activity::getStatus, 1));
        Map<Integer, Activity> sortedMap = activities.stream()
                .collect(Collectors.toMap(
                        Activity::getTarget,
                        Function.identity()
                ));
        //获取最小的
        Activity minActivity = activities.stream()
                .min(Comparator.comparing(Activity::getTarget))
                .orElse(null);
        List<User> users = userService.list(new LambdaQueryWrapper<User>().ge(User::getLevel, minActivity.getTarget()));

        for (User user : users) {
            if (user.getLevel() > user.getMaxLevel()) {
                Activity activity = sortedMap.get(user.getLevel());
                try {
                    if (activity != null) {
                        //添加活动领取记录,修改用户最大等级
                        ActivityReceive activityReceive = new ActivityReceive();
                        String orderNo = SnowIdUtil.getId(OrderConstant.ActivityReceiveCode);
                        activityReceive.setOrderSn(orderNo);
                        activityReceive.setUserId(user.getId());
                        activityReceive.setNickName(user.getNickName());
                        activityReceive.setActivityName(activity.getName());
                        activityReceive.setActivityId(activity.getActivityId());
                        activityReceive.setAmount(activity.getAward());

                        //修改充值订单
                        AmountConsumer amountConsumer = (oldUser, newUser) -> {
                            activityReceiveService.save(activityReceive);
                            userService.lambdaUpdate().set(User::getMaxLevel, user.getLevel()).eq(User::getId, user.getId()).update();
                        };

                        userAmountChangeManage.changeBalance(user.getId(), activity.getAward(), orderNo, OrderTypeEnum.BONUS, TypeEnum.BONUS, "", "", amountConsumer);
                    } else {
                        log.warn("日常队长任务-等级:{} 活动不存在", user.getLevel());
                    }
                } catch (Exception e) {
                    log.error("日常队长任务-用户:{}, 队长奖励发放失败", user.getId());
                }
            }
        }
    }
}

