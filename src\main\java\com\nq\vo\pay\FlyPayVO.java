package com.nq.vo.pay;

import com.nq.utils.PropertiesUtil;
import lombok.Data;

@Data
public class FlyPayVO {

    private String payUrl = PropertiesUtil.getProperty("fly.pay.payurl");

    private String orderno;

    private String orderamount;

    private String merchantid = PropertiesUtil.getProperty("fly.pay.merchantid");

    private String paytype;

    private String ordercurrency;

    private String serverbackurl = PropertiesUtil.getProperty("fly.pay.serverbackurl");

    private String callbackurl = PropertiesUtil.getProperty("fly.pay.callbackurl");

    private String signtype = "md5";

    private String sign;


}
