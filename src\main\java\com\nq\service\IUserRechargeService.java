package com.nq.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.pojo.UserRecharge;

import java.math.BigDecimal;
import java.util.List;

public interface IUserRechargeService extends IService<UserRecharge> {
    void checkInMoney(int paramInt, Integer userId);
    //amt, payType,password, request
    String inMoney(String amt, Integer payId);

    void inMoneyByAdmin(String orderNo,String amt, String payType, Integer uid);

    UserRecharge findUserRechargeByOrderSn(String paramString);

    void chargeSuccess(UserRecharge paramUserRecharge) throws Exception;

    void chargeFail(UserRecharge paramUserRecharge) throws Exception;

    void chargeCancel(UserRecharge paramUserRecharge) throws Exception;
        //payChannel, orderStatus, pageNum, pageSize
    PageInfo<UserRecharge> findUserChargeList(String payChannel, String orderStatus, int pageNum, int pageSize);

    //userId, realName, state, beginTime, endTime, pageNum, pageSize
    PageInfo<UserRecharge> listByAdmin(Integer userId, String orderSn, String nickName, Integer state, String beginTime, String endTime, int pageNum, int pageSize, Boolean isAgent);
    //chargeId, state, orderDesc
    void updateState(Long chargeId, Integer state, String orderDesc);
   //userId, state, amt, payChannel
    void createOrder(Integer userId, Integer state, Integer amt, String payChannel);

    void del(Long cId);

    int deleteByUserId(Integer userId);

    BigDecimal CountChargeSumAmt(Integer num);

    BigDecimal CountTotalRechargeAmountByTime(Integer num);

    List<UserRecharge> exportByAdmin(Integer userId,String orderSn, String nickName, Integer state, String beginTime, String endTime, Boolean isAgent);
    /**
     * 根据日期查询提现金额
     * @param type
     * @return
     */

    BigDecimal countChargeAmountByDate(Integer type);
    /**
     * 根据日期查询提现数量通过的
     * @param type
     * @return
     */
    Long countChargeNumByDate(Integer type);

    /**
     * 查询今日首冲金额
     * @param type
     * @return
     */
    BigDecimal countFirstChargeAmountByDate(Integer type);
    /**
     * 查询充值等待审核数量
     * @param
     * @return
     */
    Long countChargePendingCount();
}
