package com.nq.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.constant.OrderConstant;
import com.nq.dao.PackageMapper;
import com.nq.dto.app.MoreFollowApplyDTO;
import com.nq.dto.app.SingleFollowApplyDTO;
import com.nq.enums.OrderTypeEnum;
import com.nq.enums.TypeEnum;
import com.nq.function.AmountConsumer;
import com.nq.manage.UserAmountChangeManage;
import com.nq.pojo.*;
import com.nq.pojo.Package;
import com.nq.service.FollowService;
import com.nq.service.LevelService;
import com.nq.service.MentorService;
import com.nq.service.PackageService;
import com.nq.utils.*;
import com.nq.vo.admin.PackageVO;
import com.nq.vo.app.AppMorePackageVO;
import com.nq.vo.app.AppPackageVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class PackageServiceImpl extends ServiceImpl<PackageMapper, Package> implements PackageService {
    @Resource
    private LevelService levelService;
    @Resource
    private FollowService followService;
    @Resource
    private UserAmountChangeManage userAmountChangeManage;
    @Resource
    private MentorService mentorService;
    @Resource
    private HolidayUtil holidayUtil;

    @Override
    public PageInfo<PackageVO> pageList(Integer page, Integer size, String productName, String sortField) {
        PageHelper.startPage(page, size);

        LambdaQueryWrapper<Package> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(productName), Package::getProductName, productName);

        // 按照排序字段排序，默认按排序字段升序，排序字段相同时按ID升序
        wrapper.orderByAsc(Package::getSort)
                .orderByAsc(Package::getId);
        List<Package> packageList = this.list(wrapper);
        List<PackageVO> voList = packageList.stream().map(pack -> {
            PackageVO vo = new PackageVO();
            BeanUtils.copyProperties(pack, vo);
            return vo;
        }).collect(Collectors.toList());
        return PageUtil.buildPageDto(packageList, voList);
    }

    @Override
    public PackageVO getDetailById(Integer id) {
        Package pack = this.getById(id);
        if (pack == null) {
            return null;
        }

        PackageVO vo = new PackageVO();
        BeanUtils.copyProperties(pack, vo);
        return vo;
    }

    @Override
    public List<AppMorePackageVO> queryMorePackage() {
        LambdaQueryWrapper<Package> q = new LambdaQueryWrapper<>();
        // 按排序字段排序
        q.orderByAsc(Package::getSort).orderByAsc(Package::getId);

        List<AppMorePackageVO> arr = CollectionUtil.newArrayList();
        List<Package> packageList = this.list(q);
        for (Package aPackage : packageList) {
            AppMorePackageVO vo = new AppMorePackageVO();
            vo.setId(aPackage.getId());
            vo.setProductName(aPackage.getProductName());
            vo.setMinAmount(aPackage.getMinAmount());
            vo.setMaxAmount(aPackage.getMaxAmount());
            convertToorePackageVO(aPackage,vo);
            arr.add(vo);
        }
        return arr;
    }

    private void convertToorePackageVO(Package aPackage, AppMorePackageVO vo) {
        vo.setSalaryRate(aPackage.getSalaryRate().multiply(BigDecimal.TEN).multiply(BigDecimal.TEN));
    }

    @Override
    public ServerResponse<Void> applySingleFollow(SingleFollowApplyDTO dto, User user) {
        // 查询这个人的等级 看看是否符合金额要求
        Integer levelInt = user.getLevel();
        Level level = levelService.getById(levelInt);
        Integer mentorId = dto.getMentorId();
        Mentor mentor = mentorService.getById(mentorId);

        BigDecimal amount = dto.getAmount();
        // 2. 验证金额是否在套餐范围内
        if (amount.compareTo(level.getMinAmount()) < 0 || amount.compareTo(level.getMaxAmount()) > 0) {
            return ServerResponse
                    .createByErrorMsg("跟单金额必须在" + level.getMinAmount() + "至" + level.getMaxAmount() + "之间");
        }

        // 3. 验证金额是否为100的倍数
        BigDecimal remainder = amount.remainder(new BigDecimal("100"));
        if (remainder.compareTo(BigDecimal.ZERO) != 0) {
            return ServerResponse.createByErrorMsg("跟单金额必须是100的倍数");
        }
        String orderNo = SnowIdUtil.getId(OrderConstant.FollowOrder);
        // 这个需要扣钱
        AmountConsumer consumer = (oldUser, newUser) -> {
            Follow follow = new Follow();
            Date applyTime = new Date();
            int packageDays = 1; // Or fetch dynamically if needed

            follow.setFollowNo(orderNo);
            follow.setMentorId(dto.getMentorId());
            follow.setUserId(user.getId());
            follow.setApplyTime(applyTime);
            follow.setAmount(amount);
            follow.setPositionRate(level.getPositionRate());
            follow.setSalaryRate(mentor.getSalaryRate());
            follow.setMinAmount(level.getMinAmount());
            follow.setMaxAmount(level.getMaxAmount());
            follow.setPackageDays(packageDays);
            follow.setUsedDays(0);
            // 今天审核明天买 卖完之后 在买一天 买一天
            Date endTime = holidayUtil.saveDateRedis(packageDays);
            follow.setEndTime(endTime); // Set endTime instead of stopTime

            followService.save(follow);
        };
        userAmountChangeManage.changeBalance(user.getId(), amount, orderNo, OrderTypeEnum.INVESTMENT_ADVISOR,
                TypeEnum.ADVISOR_APPLY, "", "", consumer);
        // TODO: 在这里添加创建跟单记录的逻辑
        // 需要创建跟单记录表，并插入相关数据

        return ServerResponse.createBySuccessMsg("申请成功");
    }

    @Override
    public ServerResponse<Void> applyMoreFollow(MoreFollowApplyDTO dto, User user) {
        // 1. 查询套餐信息
        Package pack = this.getById(dto.getPackageId());
        if (pack == null) {
            return ServerResponse.createByErrorMsg("套餐不存在");
        }

        // 2. 验证金额是否符合要求
        BigDecimal amount = dto.getAmount();
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return ServerResponse.createByErrorMsg("金额必须大于0");
        }

        // 验证金额是否为100的倍数
        if (amount.remainder(new BigDecimal("100")).compareTo(BigDecimal.ZERO) != 0) {
            return ServerResponse.createByErrorMsg("金额必须是100的倍数");
        }

        // 验证金额是否在套餐范围内
        if (amount.compareTo(pack.getMinAmount()) < 0 || amount.compareTo(pack.getMaxAmount()) > 0) {
            return ServerResponse.createByErrorMsg("金额必须在" + pack.getMinAmount() + "到" + pack.getMaxAmount() + "之间");
        }
        Integer levelInt = user.getLevel();
        Level level = levelService.getById(levelInt);
        String followNo = SnowIdUtil.getId(OrderConstant.FollowOrder);
        // 3. 生成Follow记录
        AmountConsumer consumer = (oldUser, newUser) -> {
            Follow follow = new Follow();
            follow.setFollowNo(followNo);
            follow.setPackageId(dto.getPackageId());
            follow.setMentorId(dto.getMentorId());
            follow.setUserId(user.getId());
            follow.setApplyTime(new Date());
            follow.setAmount(amount);
            follow.setPositionRate(level.getPositionRate());
            follow.setSalaryRate(pack.getSalaryRate());
            follow.setIsAdd(0);
            follow.setAddAmount(BigDecimal.ZERO);
            follow.setStatus(1);
            follow.setMinAmount(pack.getMinAmount());
            follow.setMaxAmount(pack.getMaxAmount());
            follow.setPackageDays(pack.getHoldDays());
            follow.setUsedDays(0);
            Date endTime = holidayUtil.saveDateRedis(pack.getHoldDays());
            follow.setEndTime(endTime); // Set endTime instead of stopTime
            follow.setCreateTime(new Date());
            follow.setUpdateTime(new Date());
            followService.save(follow);
        };
        // 计算结束时间
        // 保存Follow记录
        userAmountChangeManage.changeBalance(user.getId(), amount, followNo, OrderTypeEnum.INVESTMENT_ADVISOR,
                TypeEnum.ADVISOR_APPLY, "", "", consumer);

        return ServerResponse.createBySuccess();
    }
}