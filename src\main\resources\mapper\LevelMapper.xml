<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nq.dao.LevelMapper">

    <resultMap id="BaseResultMap" type="com.nq.pojo.Level">
        <id column="id" property="id"/>
        <result column="level_name" property="levelName"/>
        <result column="level_code" property="levelCode"/>
        <result column="min_people" property="minPeople"/>
        <result column="max_people" property="maxPeople"/>
        <result column="min_amount" property="minAmount"/>
        <result column="max_amount" property="maxAmount"/>
        <result column="position_rate" property="positionRate"/>
        <result column="salary_rate" property="salaryRate"/>
        <result column="level_icon" property="levelIcon"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="sort" property="sort"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, level_name, level_code, min_people, max_people, min_amount, max_amount,
        position_rate, salary_rate, level_icon, remark, create_time, update_time, sort
    </sql>

</mapper>