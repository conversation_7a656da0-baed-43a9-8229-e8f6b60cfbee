package com.nq.manage;

import com.nq.pojo.User;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/8/008 20:51
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserTDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 最大id
     */
    private User oldUser;
    /**
     * 批量查询数量
     */
    private User newUser;

}
