package com.nq.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.nq.dao.StockMapper;
import com.nq.dao.StockOptionMapper;
import com.nq.pojo.Stock;
import com.nq.pojo.StockOption;
import com.nq.pojo.User;
import com.nq.service.IStockOptionService;
import com.nq.service.IUserService;
import com.nq.utils.stock.sina.SinaStockApi;
import com.nq.vo.stock.StockOptionListVO;
import com.nq.vo.stock.StockVO;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("iStockOptionService")
public class StockOptionServiceImpl extends ServiceImpl<StockOptionMapper, StockOption> implements IStockOptionService {

    private static final Logger log = LoggerFactory.getLogger(StockOptionServiceImpl.class);

    @Resource
    private StockOptionMapper stockOptionMapper;

    @Resource
    private IUserService iUserService;

    @Resource
    private StockMapper stockMapper;

    public PageInfo<StockOptionListVO> findMyStockOptions(String keyWords, HttpServletRequest request, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        User user = this.iUserService.getCurrentUser();
        if (user == null) {
            throw new com.nq.excepton.CustomException("请先登录");
        }
        List<StockOption> stockOptions = this.stockOptionMapper.findMyOptionByKeywords(user.getId(), keyWords);
        List<StockOptionListVO> stockOptionListVOS = Lists.newArrayList();
        for (StockOption option : stockOptions) {
            StockOptionListVO stockOptionListVO = assembleStockOptionListVO(option);
            stockOptionListVO.setIsOption("1");
            stockOptionListVOS.add(stockOptionListVO);
        }
        PageInfo<StockOptionListVO> pageInfo = new PageInfo<StockOptionListVO>(stockOptionListVOS);
        pageInfo.setList(stockOptionListVOS);
        return pageInfo;
    }

    public Boolean isOption(Integer uid, String code) {
        StockOption stockOption = this.stockOptionMapper.isOption(uid, code);
        if (stockOption == null) {
            return false;
        }
        return true;
    }

    public String isMyOption(Integer uid, String code) {
        StockOption stockOption = this.stockOptionMapper.isOption(uid, code);
        if (stockOption == null) {
            return "0";
        }
        return "1";

    }

    private StockOptionListVO assembleStockOptionListVO(StockOption option) {
        StockOptionListVO stockOptionListVO = new StockOptionListVO();
        stockOptionListVO.setId(option.getId());
        stockOptionListVO.setStockName(option.getStockName());
        stockOptionListVO.setStockCode(option.getStockCode());
        stockOptionListVO.setStockGid(option.getStockGid());
        StockVO stockVO = SinaStockApi.assembleStockVO(SinaStockApi.getSinaStock(option.getStockGid()));
        stockOptionListVO.setNowPrice(stockVO.getNowPrice());
        stockOptionListVO.setHcrate(stockVO.getHcrate().toString());
        stockOptionListVO.setPreclose_px(stockVO.getPreclose_px());
        stockOptionListVO.setOpen_px(stockVO.getOpen_px());
        stockOptionListVO.setType(stockVO.getType());
        Stock stock = this.stockMapper.selectById(option.getStockId());
        stockOptionListVO.setStock_plate(stock.getStockPlate() == null ? "" : stock.getStockPlate());
        stockOptionListVO.setStock_type(stock.getStockType());
        return stockOptionListVO;
    }
}
