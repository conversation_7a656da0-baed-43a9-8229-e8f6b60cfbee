package com.nq.controller.app;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.SiteMessage;
import com.nq.service.ISiteMessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping({"/user/message/"})
@Api(tags = "APP-用户消息")
public class AppUserMessageController {
    @Resource
    private  ISiteMessageService iSiteMessageService;

    @ApiOperation(value = "查询用户站内消息列表", notes = "分页查询用户的站内消息")
    @PostMapping({"getMessagelist.do"})
    public ServerResponse<PageInfo<SiteMessage>> getMessageList(
            HttpServletRequest request,
            @ApiParam(value = "页码", defaultValue = "1") @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @ApiParam(value = "每页数量", defaultValue = "10") @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @ApiParam(value = "用户ID", required = false) @RequestParam(value = "userId", required = false) Integer userId) {
        PageInfo<SiteMessage> siteMessageByUserIdList = iSiteMessageService.getSiteMessageByUserIdList(pageNum, pageSize, userId, request);
        return ServerResponse.createBySuccess(siteMessageByUserIdList);
    }

    @ApiOperation(value = "更新通知状态", notes = "将指定通知标记为已读")
    @PostMapping({"updateNoticeStatus.do"})
    public ServerResponse<Void> updateNoticeStatus(
            HttpServletRequest request,
            @ApiParam(value = "通知ID", required = true) @RequestParam(value = "noticeId") int noticeId) {
         iSiteMessageService.updateNoticeStatus(noticeId, request);
         return ServerResponse.createBySuccess();
    }

    @ApiOperation(value = "更新消息状态", notes = "将所有消息标记为已读")
    @GetMapping({"updateMessageStatus.do"})
    public ServerResponse<String> updateMessageStatus(HttpServletRequest request) {
        iSiteMessageService.updateMessageStatus(request);
        return ServerResponse.createBySuccess("查看成功");
    }

    @ApiOperation(value = "获取未读消息数量", notes = "查询用户的未读消息数量")
    @PostMapping({"getUnreadCount.do"})
    public ServerResponse<Integer> getUnreadCount(HttpServletRequest request) {
        int k = iSiteMessageService.getUnreadCount(request);
        return ServerResponse.createBySuccess(k);
    }
    @ApiOperation(value = "已读全部", notes = "已读全部")
    @GetMapping({"readAll.do"})
    public ServerResponse<String> readAll() {
        iSiteMessageService.readAll();
        return ServerResponse.createBySuccess("操作成功");
    }
}