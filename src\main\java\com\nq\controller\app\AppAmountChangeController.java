package com.nq.controller.app;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dto.app.AmountChangeParam;
import com.nq.enums.TypeEnum;
import com.nq.pojo.User;
import com.nq.pojo.UserWithdraw;
import com.nq.service.AmountChangeService;
import com.nq.service.IUserService;
import com.nq.service.IUserWithdrawService;
import com.nq.vo.app.AmountTypeGroupItemVO;
import com.nq.vo.user.AmountChangeTypeVo;
import com.nq.vo.user.AmountChangeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping({"/user/AmountChange/"})
@Api(tags = "APP-账变")
public class AppAmountChangeController {
    @Resource
    private AmountChangeService amountChangeService;
    private static final Logger log = LoggerFactory.getLogger(AppAmountChangeController.class);

    @ApiOperation(value = "获取所有的账遍类型", notes = "获取所有的账遍类型")
    @PostMapping("/getAmountChangeType")
    public ServerResponse<List<AmountTypeGroupItemVO>> getAmountChangeType() {
        List<AmountTypeGroupItemVO> amountTypeMap = getAmountTypeMap();
        return ServerResponse.createBySuccess(amountTypeMap);
    }


    @ApiOperation(value = "查询账变记录", notes = "获取所有的账遍类型")
    @PostMapping("/listOmitInfo")
    public ServerResponse<PageInfo<AmountChangeVO>> listOmitInfo(@RequestBody AmountChangeParam amountChangeParam) {
        PageInfo<AmountChangeVO> pageInfo = amountChangeService.listOmitInfo(amountChangeParam);
        return ServerResponse.createBySuccess(pageInfo);
    }


    public List<AmountTypeGroupItemVO> getAmountTypeMap() {
        List<AmountTypeGroupItemVO> result = new ArrayList<>();
        List<AmountChangeTypeVo> zeroList = new ArrayList<>();
        // 先收集 amountType=0 的类型
        for (TypeEnum type : TypeEnum.values()) {
            if (type.getAmountType() == 0) {
                zeroList.add(new AmountChangeTypeVo(type.getCode(), type.getDesc()));
            }
        }
        // 只处理 1 和 2
        for (int amtType : new int[]{1, 2}) {
            List<AmountChangeTypeVo> list = new ArrayList<>();
            for (TypeEnum type : TypeEnum.values()) {
                if (type.getAmountType() == amtType) {
                    list.add(new AmountChangeTypeVo(type.getCode(), type.getDesc()));
                }
            }
            // 追加 0 的类型
            list.addAll(zeroList);
            AmountTypeGroupItemVO item = new AmountTypeGroupItemVO();
            item.setAmountType(amtType);
            item.setTypes(list);
            result.add(item);
        }
        return result;
    }
}