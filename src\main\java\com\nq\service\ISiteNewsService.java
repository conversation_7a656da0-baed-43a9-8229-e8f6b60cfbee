package com.nq.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.SiteMessage;
import com.nq.pojo.SiteNews;

import javax.servlet.http.HttpServletRequest;

/**
 * 新闻资讯
 * <AUTHOR>
 * @date 2020/07/24
 */
public interface ISiteNewsService  extends IService<SiteNews> {

    /**
     * 新增
     */
    int insert(SiteNews model);

    /**
     * 更新
     */
    int update(SiteNews model);

    /**
     * 新闻资讯-保存
     */
    ServerResponse<String> saveOne(SiteNews model);

    /**
     * 新闻资讯-列表查询
     */
    PageInfo<SiteNews> getList(int pageNum, int pageSize, Integer type, String sort, String keyword,Integer status, HttpServletRequest request);

    /**
     * 新闻资讯-查询详情
     */
   SiteNews getDetail(int id, HttpServletRequest request);

    /**
     * 抓取新闻
     */
    int grabNews();

}
