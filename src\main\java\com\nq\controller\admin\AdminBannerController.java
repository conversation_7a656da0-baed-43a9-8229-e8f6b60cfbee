package com.nq.controller.admin;


import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.SiteBanner;
import com.nq.service.ISiteBannerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RestController
@RequestMapping({"/admin/banners/"})
@Api(tags = {"后台-banner设置"})
public class AdminBannerController {
    private static final Logger log = LoggerFactory.getLogger(AdminBannerController.class);

    @Resource
    private ISiteBannerService iSiteBannerService;

    @ApiOperation("添加banner设置信息")
    @PostMapping({"add.do"})
    public ServerResponse<String> add(SiteBanner siteBanner) {
        this.iSiteBannerService.add(siteBanner);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation("修改banner设置信息")
    @PostMapping({"update.do"})
    public ServerResponse<String> update(SiteBanner siteBanner) {
        this.iSiteBannerService.update(siteBanner);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation("删除banner设置信息")
    @PostMapping({"delete.do"})
    public ServerResponse<String> delete(Integer id) {
        this.iSiteBannerService.delete(id);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation("分页查询banner设置信息")
    @PostMapping({"list.do"})
    public ServerResponse<PageInfo<SiteBanner>> list(@RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        PageInfo<SiteBanner> siteBannerPageInfo = this.iSiteBannerService.listByAdmin(pageNum, pageSize);
        return ServerResponse.createBySuccess(siteBannerPageInfo);
    }
}
