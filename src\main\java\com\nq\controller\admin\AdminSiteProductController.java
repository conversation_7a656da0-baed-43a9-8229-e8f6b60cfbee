package com.nq.controller.admin;


import com.nq.common.ServerResponse;
import com.nq.pojo.SiteProduct;
import com.nq.service.ISiteProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@RestController
@Api(tags = "后台-产品配置")
@RequestMapping({"/admin/product/"})
public class AdminSiteProductController {
    private static final Logger log = LoggerFactory.getLogger(AdminSiteProductController.class);

    @Resource
    private ISiteProductService iSiteProductService;

    //风控设置 修改产品配置信息
    @ApiOperation(value = "风控设置-修改产品配置信息", notes = "风控设置-修改产品配置信息")
    @PostMapping({"update.do"})
    public ServerResponse<String> update(SiteProduct siteProduct) {
         this.iSiteProductService.update(siteProduct);
         return ServerResponse.createBySuccess();
    }
}
