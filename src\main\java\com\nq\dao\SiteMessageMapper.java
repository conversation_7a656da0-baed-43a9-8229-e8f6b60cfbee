package com.nq.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nq.pojo.SiteMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *  站内消息
 * <AUTHOR> 2020-07-16
 */
@Mapper
public interface SiteMessageMapper extends BaseMapper<SiteMessage> {
    /**
     * [查询] 根据主键 id 查询
     * <AUTHOR>
     * @date 2020/07/16
     **/
    SiteMessage load(int id);


    /*查询用户站内消息列表*/
    List<SiteMessage> getSiteMessageByUserIdList(@Param("userId") Integer userId);

    List<SiteMessage> getUnReadSiteMessageByType(@Param("type") String type);

    List<SiteMessage> queryByMessageType(@Param("messageType") Integer messageType);
    /**
     * [今天该类型的站内消息是否推送过]
     * <AUTHOR>
     * @date 2020/07/16
     **/
    int getIsDayCount(@Param("userId") Integer userId,@Param("typeName") String typeName);

    /**
     * [用户站内消息状态变已读]
     * <AUTHOR>
     * @date 2020/07/16
     **/
    int updateMessageStatus(@Param("userId") Integer userId);

    /**
     * [查询用户未读消息数]
     * <AUTHOR>
     * @date 2020/07/16
     **/
    int getUnreadCount(@Param("userId") Integer userId);

}
