package com.nq.controller.admin;

import com.nq.common.ServerResponse;
import com.nq.common.satoken.StpAdminUtil;
import com.nq.service.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.nq.vo.admin.SiteAdminVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
@Api(tags = "后台-登录和设置")
@RestController
@RequestMapping({"/api/admin/"})
public class AdminLoginController {
    @Resource
    private ISiteAdminService iSiteAdminService;

    @ApiOperation("管理系统登录")
    @PostMapping({"login.do"})
    public ServerResponse<SiteAdminVo> login(@RequestParam("adminPhone") String adminPhone, @RequestParam("adminPwd") String adminPwd, @RequestParam("verifyCode") String verifyCode, HttpSession httpSession, HttpServletRequest request, HttpServletResponse response) {
        SiteAdminVo siteAdmin = this.iSiteAdminService.login(adminPhone, adminPwd, verifyCode, request);
        return ServerResponse.createBySuccess(siteAdmin);
    }

    @ApiOperation("管理系统注销")
    @PostMapping({"logout.do"})
    public ServerResponse<String> logout(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
        // Sa-Token 方式退出
        StpAdminUtil.logout();
        // 可选：如果你用 cookie 存 token，也可以手动删掉
        // CookieUtils.delLoginToken(httpServletRequest, httpServletResponse, "AGENTTOKEN");
        return ServerResponse.createBySuccess();
    }

}
