package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "活动")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "activity")
public class Activity extends BaseEntity {
    /**
     * 任务id
     */
    @TableId(value = "activity_id", type = IdType.AUTO)
    @ApiModelProperty(value = "任务id")
    private Integer activityId;

    /**
     * 任务类型(1 队长成长任务, 2 日常邀请任务)
     */
    @TableField(value = "`type`")
    @ApiModelProperty(value = "任务类型(1 队长成长任务, 2 日常邀请任务)")
    private Integer type;

    /**
     * 任务名称
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value = "任务名称")
    private String name;

    /**
     * 奖励
     */
    @TableField(value = "award")
    @ApiModelProperty(value = "奖励")
    private BigDecimal award;

    /**
     * 任务目标
     */
    @TableField(value = "target")
    @ApiModelProperty(value = "任务目标")
    private Integer target;

    /**
     * 权重
     */
    @TableField(value = "weight")
    @ApiModelProperty(value = "权重")
    private Integer weight;

    /**
     * 状态(0 关闭, 1 开启)
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value = "状态(0 关闭, 1 开启)")
    private Byte status;

    /**
     * 状态(0 关闭, 1 开启)
     */
    @TableField(value = "sort")
    @ApiModelProperty(value = "排序")
    private Integer sort;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_by")
    @ApiModelProperty(value = "修改人")
    private String updateBy;
}