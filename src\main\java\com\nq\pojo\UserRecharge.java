package com.nq.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(description = "用户充值记录")
@AllArgsConstructor
@NoArgsConstructor
public class UserRecharge extends BaseEntity {

    @ApiModelProperty(value = "主键ID", example = "1")
    private Long id;

    @Excel(name = "用户id")
    @ApiModelProperty(value = "用户ID", example = "1")
    private Integer userId;

    @Excel(name = "用户名")
    @ApiModelProperty(value = "用户昵称", example = "张三")
    private String nickName;


    @Excel(name = "订单号")
    @ApiModelProperty(value = "订单编号", example = "RECH202401010001")
    private String orderSn;

    @ApiModelProperty(value = "支付流水号", example = "PAY202401010001")
    private String paySn;

    @Excel(name = "充值渠道" ,replace = { "支付宝_0", "对公打款_1" })
    @ApiModelProperty(value = "充值渠道：0-支付宝，1-对公打款", example = "0")
    private String payChannel;

    @Excel(name = "充值金额")
    @ApiModelProperty(value = "充值金额", example = "10000.00")
    private BigDecimal payAmt;

    @Excel(name = "状态" ,replace = { "审核中_0", "成功_1", "失败_2" })
    @ApiModelProperty(value = "订单状态：0-审核中，1-成功，2-失败", example = "0")
    private Integer orderStatus;

    @ApiModelProperty(value = "订单描述", example = "充值订单描述信息")
    private String orderDesc;

    @Excel(name = "申请时间", databaseFormat = "yyyyMMddHHmmss", format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "添加时间", example = "2024-01-01 09:30:00")
    private Date addTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "支付时间", databaseFormat = "yyyyMMddHHmmss", format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "支付时间", example = "2024-01-01 09:35:00")
    private Date payTime;

    @ApiModelProperty(value = "支付通道ID", example = "1")
    private Integer payId;
}