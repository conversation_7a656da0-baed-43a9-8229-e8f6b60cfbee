package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("follow_append")
@ApiModel(value = "FollowAppend对象", description = "跟单追加申请表")
public class FollowAppend extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID", example = "1")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "跟单id", example = "123")
    private Integer followId;

    @ApiModelProperty(value = "跟单单号", example = "FN2024123456")
    private String followNo;

    @ApiModelProperty(value = "金额", example = "5000.00")
    private BigDecimal amount;

    @ApiModelProperty(value = "状态(1-待审核，2-通过，3-被驳回)", example = "1") // Corrected status codes based on common patterns
    private Integer status;

    @ApiModelProperty(value = "用户ID", example = "1001")
    private Integer userId;

    @ApiModelProperty(value = "创建时间", example = "2024-07-26 10:00:00")
    private Date createTime;

    @ApiModelProperty(value = "更新时间", example = "2024-07-26 10:05:00")
    private Date updateTime;

} 