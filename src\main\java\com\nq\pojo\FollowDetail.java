package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName("follow_detail")
@ApiModel(description = "跟单详情")
public class FollowDetail extends BaseEntity {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;
    
    @ApiModelProperty(value = "跟单ID", example = "1")
    private Integer followId;
    
    @ApiModelProperty(value = "跟单单号", example = "FD202403010001")
    private String followNo;
    @ApiModelProperty(value = "followtardeId", example = "1")
    private Integer followTradeId;
    
    @ApiModelProperty(value = "用户ID", example = "1")
    private Integer userId;
    
    @ApiModelProperty(value = "导师ID", example = "1")
    private Integer mentorId;
    
    @ApiModelProperty(value = "套餐ID", example = "1")
    private Integer packageId;
    
    @ApiModelProperty(value = "股票代码", example = "603716")
    private String stockCode;
    
    @ApiModelProperty(value = "股票名称", example = "塞力医疗")
    private String stockName;
    
    @ApiModelProperty(value = "跟单金额", example = "30000.00")
    private BigDecimal amount;
    
    @ApiModelProperty(value = "仓位比例", example = "40.00")
    private BigDecimal positionRate;
    
    @ApiModelProperty(value = "买入数量", example = "100")
    private Integer buyQuantity;
    
    @ApiModelProperty(value = "卖出数量", example = "50")
    private Integer sellQuantity;
    
    @ApiModelProperty(value = "现有数量", example = "50")
    private Integer currentQuantity;
    
    @ApiModelProperty(value = "买入单价", example = "10.55")
    private BigDecimal buyPrice;
    
    @ApiModelProperty(value = "卖出单价", example = "11.25")
    private BigDecimal sellPrice;
    
    @ApiModelProperty(value = "买入金额", example = "1055.00")
    private BigDecimal buyAmount;
    
    @ApiModelProperty(value = "卖出金额", example = "562.50")
    private BigDecimal sellAmount;
    
    @ApiModelProperty(value = "盈亏金额", example = "150.50")
    private BigDecimal profit ;
    
    @ApiModelProperty(value = "建仓时间")
    private Date buyTime;
    
    @ApiModelProperty(value = "平仓时间")
    private Date sellTime;
    
    @ApiModelProperty(value = "结算时间")
    private Date settlementTime;
    
    /** 套餐类型(1-普通，2-套餐) */
    @ApiModelProperty(value = "套餐类型(1-普通，2-套餐)", example = "1")
    private Integer packageType;
    
    /**
     * 状态(0-未结算 1-已结算)
     */
    @ApiModelProperty(value = "状态(0-未结算 1-已结算)", example = "0")
    private Integer settlementStatus;
    
    /**
     * 持仓状态(1-跟单中，2-已清仓)
     */
    @ApiModelProperty(value = "持仓状态(1-跟单中，2-已清仓)", example = "1")
    private Integer status;
    
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "跟单最高", example = "50000.00")
    private BigDecimal maxAmount;

    @ApiModelProperty(value = "跟单最低", example = "1000.00")
    private BigDecimal minAmount;

    @ApiModelProperty(value = "工资比例", example = "0.03")
    private BigDecimal salaryRate;

    @ApiModelProperty(value = "佣金", example = "100.00")
    private BigDecimal salary;
} 