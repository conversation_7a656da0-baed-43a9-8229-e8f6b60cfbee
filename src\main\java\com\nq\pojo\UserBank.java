package com.nq.pojo;

import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
@Data
@ApiModel(description = "用户银行卡信息")
@AllArgsConstructor
@NoArgsConstructor
public class UserBank extends BaseEntity {
    @ApiModelProperty(value = "银行卡ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "用户ID", example = "1001")
    private Integer userId;

    @ApiModelProperty(value = "银行名称", example = "中国银行")
    private String bankName;

    @ApiModelProperty(value = "银行卡号", example = "6222021234567890123")
    private String bankNo;

    @ApiModelProperty(value = "开户行地址", example = "北京市海淀区中关村支行")
    private String bankAddress;

    @ApiModelProperty(value = "银行图标URL", example = "http://example.com/bank.png")
    private String bankImg;

    @ApiModelProperty(value = "银行预留手机号", example = "***********")
    private String bankPhone;

    @ApiModelProperty(value = "添加时间", example = "2023-01-01 12:00:00")
    private Date addTime;


}