package com.nq.dto.base;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Entity基类
 *
 * <AUTHOR>
 */
@Data
@ApiModel("基础实体类")
public class BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 搜索值
     */
    @TableField(exist = false)
    @ApiModelProperty("搜索值")
    private String searchValue;


    /**
     * 开始时间
     */
    @TableField(exist = false)
    @ApiModelProperty("开始时间")
    @JsonIgnore
    private Date beginTime;
    /**
     * 开始时间
     */
    @TableField(exist = false)
    @ApiModelProperty("开始时间")
    @JsonIgnore
    private Date endTime;

    /**
     * 请求参数
     */
    @TableField(exist = false)
    @ApiModelProperty("请求参数")
    private Map<String, Object> params;

    public Map<String, Object> getParams() {
        if (params == null) {
            params = new HashMap<>();
        }
        return params;
    }

}
