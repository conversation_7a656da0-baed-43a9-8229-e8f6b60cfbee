package com.nq.config;

import com.nq.dao.RealTimeMapper;
import com.nq.dao.StockMapper;
import com.nq.pojo.Stock;
import com.nq.utils.stock.sina.SinaStockApi;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class StockPoll {
    @Resource
    StockMapper stockMapper;

    @Resource
    RealTimeMapper realTimeMapper;


    private ThreadPoolExecutor pool;

    @PostConstruct
    public void initPool() {
        this.pool = new ThreadPoolExecutor(50, 70, 20L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(240));
        this.pool.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardOldestPolicy());
    }

    public void test(String stockType, Integer stock_num, Integer stock_nums) {
        List<Stock> stockCodes = this.stockMapper.findStockCode(stockType, stock_num, stock_nums);
        for (Stock stock : stockCodes) {
            String stockGid = stock.getStockGid();
            String sinaStock = SinaStockApi.getSinaStock("s_" + stockGid);
            String[] arrayOfString = sinaStock.split(",");
            StockTask stockTask = new StockTask();
            stockTask.splits((Object[])arrayOfString);
            stockTask.stockGid(stockGid);

            /*//处理均价
            String details = SinaStockApi.getSinaStock(stockGid);
            String averagePrice = details.split(",")[29];
            stockTask.averagePrice(Double.parseDouble(averagePrice));*/

            stockTask.averagePrice(Double.parseDouble("0"));
            stockTask.RealTimeMapper(this.realTimeMapper);

            this.pool.submit(stockTask);
        }
    }

    //0-540
    public void z1() {
        test("sz", Integer.valueOf(0), Integer.valueOf(300));
    }
    public void z11() {
        test("sz", Integer.valueOf(300), Integer.valueOf(300));
    }
    public void z12() {
        test("sz", Integer.valueOf(600), Integer.valueOf(200));
    }

    //540-540
    public void z2() {
        test("sz", Integer.valueOf(800), Integer.valueOf(300));
    }
    public void z21() {
        test("sz", Integer.valueOf(1100), Integer.valueOf(300));
    }
    public void z22() {
        test("sz", Integer.valueOf(1400), Integer.valueOf(200));
    }

    //1080 - 540
    public void z3() {
        test("sz", Integer.valueOf(1600), Integer.valueOf(300));
    }
    public void z31() {
        test("sz", Integer.valueOf(1900), Integer.valueOf(300));
    }
    public void z32() {
        test("sz", Integer.valueOf(2200), Integer.valueOf(200));
    }

    //1620 - 539
    public void z4() {
        test("sz", Integer.valueOf(2400), Integer.valueOf(200));
    }
    public void z41() {
        test("sz", Integer.valueOf(2600), Integer.valueOf(100));
    }
    public void z42() {
        test("sz", Integer.valueOf(2700), Integer.valueOf(100));
    }

    //0-484
    public void h1() {
        test("sh", Integer.valueOf(0), Integer.valueOf(300));
    }
    public void h11() {
        test("sh", Integer.valueOf(300), Integer.valueOf(300));
    }
    public void h12() {
        test("sh", Integer.valueOf(600), Integer.valueOf(100));
    }

    //484-484
    public void h2() {
        test("sh", Integer.valueOf(700), Integer.valueOf(300));
    }
    public void h21() {
        test("sh", Integer.valueOf(1000), Integer.valueOf(300));
    }
    public void h22() {
        test("sh", Integer.valueOf(1300), Integer.valueOf(100));
    }

    //968-485
    public void h3() {
        test("sh", Integer.valueOf(1400), Integer.valueOf(300));
    }
    public void h31() {
        test("sh", Integer.valueOf(1700), Integer.valueOf(300));
    }
    public void h32() {
        test("sh", Integer.valueOf(2000), Integer.valueOf(200));
    }

    //bj 0-121
    public void bj1() {
        test("bj", Integer.valueOf(0), Integer.valueOf(200));
    }





    /*指数k线分时数据抓取*/

}
