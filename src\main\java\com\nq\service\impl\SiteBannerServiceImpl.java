package com.nq.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.dao.SiteBannerMapper;
import com.nq.pojo.SiteBanner;
import com.nq.service.ISiteBannerService;
import com.nq.excepton.CustomException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service("iSiteBannerService")
public class SiteBannerServiceImpl extends ServiceImpl<SiteBannerMapper, SiteBanner> implements ISiteBannerService {
    private static final Logger log = LoggerFactory.getLogger(SiteBannerServiceImpl.class);

    @Resource
    private SiteBannerMapper siteBannerMapper;

    @Override
    public void add(SiteBanner siteBanner) {
        if (StringUtils.isBlank(siteBanner.getBannerUrl()) || 
            siteBanner.getIsOrder() == null || 
            siteBanner.getIsM() == null) {
            throw new CustomException("参数不能为空");
        }

        siteBanner.setAddTime(new Date());
        int insertCount = this.siteBannerMapper.insert(siteBanner);
        if (insertCount <= 0) {
            throw new CustomException("添加失败");
        }
    }

    @Override
    public PageInfo<SiteBanner> listByAdmin(int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<SiteBanner> siteBanners = this.siteBannerMapper.listByAdmin();
        return new PageInfo<>(siteBanners);
    }

    @Override
    public void update(SiteBanner siteBanner) {
        if (siteBanner == null) {
            throw new CustomException("id不能为空");
        }
        int updateCount = this.siteBannerMapper.updateById(siteBanner);
        if (updateCount <= 0) {
            throw new CustomException("修改失败");
        }
    }

    @Override
    public void delete(Integer id) {
        if (id == null) {
            throw new CustomException("id不能为空");
        }
        int deleteCount = this.siteBannerMapper.deleteById(id);
        if (deleteCount <= 0) {
            throw new CustomException("删除失败");
        }
    }

    @Override
    public List<SiteBanner> getBannerByPlat(String platType) {
        if ("m".equals(platType)) {
            return this.siteBannerMapper.getBannerByMobile();
        }
        if ("pc".equals(platType)) {
            return this.siteBannerMapper.getBannerByPC();
        }
        throw new CustomException("不存在的平台类型");
    }
}

