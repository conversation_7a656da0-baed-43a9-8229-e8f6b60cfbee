package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("stock")
@ApiModel(description = "股票信息")
public class Stock extends BaseEntity {
    @TableId(type = IdType.AUTO,value = "id")
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "股票名称", example = "平安银行")
    private String stockName;

    @ApiModelProperty(value = "股票代码", example = "000001")
    private String stockCode;

    @ApiModelProperty(value = "股票拼音简称", example = "PAYH")
    private String stockSpell;

    @ApiModelProperty(value = "股票类型", example = "A股")
    private String stockType;

    @ApiModelProperty(value = "股票唯一标识", example = "sz000001")
    private String stockGid;

    @ApiModelProperty(value = "所属板块", example = "银行")
    private String stockPlate;

    @ApiModelProperty(value = "是否锁定(0:未锁定 1:已锁定)", example = "0")
    private Integer isLock;

    @ApiModelProperty(value = "是否显示(0:不显示 1:显示)", example = "1")
    private Integer isShow;

    @ApiModelProperty(value = "添加时间", example = "2024-01-01 12:00:00")
    private Date addTime;

    @ApiModelProperty(value = "点差费率", example = "0.001")
    private BigDecimal spreadRate;

    @ApiModelProperty(value = "数据源(1:新浪 2:腾讯)", example = "1")
    private Integer dataBase;
}