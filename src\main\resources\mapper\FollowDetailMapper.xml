<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nq.dao.FollowDetailMapper">

    <resultMap id="BaseResultMap" type="com.nq.pojo.FollowDetail">
        <id column="id" property="id"/>
        <result column="follow_id" property="followId"/>
        <result column="follow_no" property="followNo"/>
        <result column="user_id" property="userId"/>
        <result column="mentor_id" property="mentorId"/>
        <result column="package_id" property="packageId"/>
        <result column="stock_code" property="stockCode"/>
        <result column="stock_name" property="stockName"/>
        <result column="amount" property="amount"/>
        <result column="position_rate" property="positionRate"/>
        <result column="buy_quantity" property="buyQuantity"/>
        <result column="sell_quantity" property="sellQuantity"/>
        <result column="current_quantity" property="currentQuantity"/>
        <result column="buy_price" property="buyPrice"/>
        <result column="sell_price" property="sellPrice"/>
        <result column="buy_amount" property="buyAmount"/>
        <result column="sell_amount" property="sellAmount"/>
        <result column="profit" property="profit"/>
        <result column="max_amount" property="maxAmount"/>
        <result column="min_amount" property="minAmount"/>
        <result column="salary_rate" property="salaryRate"/>
        <result column="salary" property="salary"/>
        <result column="buy_time" property="buyTime"/>
        <result column="sell_time" property="sellTime"/>
        <result column="settlement_time" property="settlementTime" />
        <result column="package_type" property="packageType"/>
        <result column="settlement_status" property="settlementStatus"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, follow_id, follow_no, user_id, mentor_id, package_id, stock_code, stock_name,
        amount, position_rate, buy_quantity, sell_quantity, current_quantity,
        buy_price, sell_price, buy_amount, sell_amount,
        profit,
        max_amount, min_amount, salary_rate, salary,
        buy_time, sell_time, settlement_time,
        package_type,
        settlement_status,
        status,
        position_status, create_time, update_time
    </sql>

    <!-- 自定义查询方法可以在这里添加 -->

</mapper> 