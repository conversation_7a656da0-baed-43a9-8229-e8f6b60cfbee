package com.nq.service.impl;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.constant.OrderConstant;
import com.nq.dao.*;
import com.nq.excepton.CustomException;
import com.nq.pojo.*;
import com.nq.service.*;
import com.nq.utils.DateTimeUtil;
import com.nq.utils.SnowIdUtil;
import com.nq.utils.stock.GeneratePosition;
import com.nq.utils.stock.sina.SinaStockApi;
import com.nq.vo.stock.StockListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 新股申购
 *
 * <AUTHOR>
 * @date 2020/07/24
 */
@Service("IUserStockSubscribeService")
@Slf4j
public class UserStockSubscribeServiceImpl extends ServiceImpl<UserStockSubscribeMapper, UserStockSubscribe> implements IUserStockSubscribeService {

    @Resource
    private UserStockSubscribeMapper userStockSubscribeMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private ISiteMessageService iSiteMessageService;
    @Resource
    private StockSubscribeMapper stockSubscribeMapper;
    @Resource
    private IUserPositionService iUserPositionService;
    @Resource
    private ISiteProductService iSiteProductService;
    @Resource
    private IUserService iUserService;
    @Resource
    private IStockService iStockService;
    @Resource
    private ISiteSettingService iSiteSettingService;

    @Resource
    private UserPositionMapper userPositionMapper;
    @Resource
    private ISiteSettingService siteSettingService;


    @Resource
    private SiteMessageMapper siteMessageMapper;

    /**
     * 用户新股申购
     *
     * @param model
     * @return
     */
    @Override
    public void insert(UserStockSubscribe model) throws Exception {
        if (model == null) {
            throw new CustomException("参数错误");
        }

        User user = this.iUserService.getCurrentUser();
        if (user == null) {
            throw new CustomException("請先登錄");
        }
        if (model.getNewCode() != null) {
            StockSubscribe stockSubscribe = stockSubscribeMapper.selectOne(new QueryWrapper<StockSubscribe>().eq("code", model.getNewCode()));
            if (stockSubscribe == null) {
                throw new CustomException("新股代码不存在");
            }
            //实名认证开关
            SiteProduct siteProduct = iSiteProductService.getProductSetting();
            if (siteProduct.getRealNameDisplay() && (StringUtils.isBlank(user.getRealName()) || StringUtils.isBlank(user.getIdCard()))) {
                throw new CustomException("下单失败，请先实名认证");
            }
            //判断休息日不能买入
            if (siteProduct.getHolidayDisplay()) {
                throw new CustomException("周末或节假日不能交易！");
            }
            //交易时段
            if (!siteSettingService.checkTradingHours()) {
                throw new CustomException("申购失败，不在交易时段内");
            }
            if (siteProduct.getRealNameDisplay() && user.getIsLock().intValue() == 1) {
                throw new CustomException("下单失败，账户已被锁定");
            }

            //时间判定当前时间是否是申购时间
            SiteSetting siteSetting = this.iSiteSettingService.getSiteSetting();
            if (siteSetting == null) {
                log.error("下单出错，网站设置表不存在");
                throw new CustomException("下单失败，系统设置错误");
            }
            if (model.getApplyNums() == null || model.getApplyNums() > stockSubscribe.getOrderNumber() * 10000) {
                throw new CustomException("购买数量异常或大于发行数量" + stockSubscribe.getOrderNumber() * 10000);
            }
            //
//            if (model.getType() == 2) {
//                //线下配资要判断是否有余额,然后直接扣除
//                if (user.getEnableAmt().compareTo(new BigDecimal(model.getApplyNums()).multiply(stockSubscribe.getPrice())) < 0) {
//                    throw new CustomException("用户可用余额不足，申购条件不满足");
//                }
//                user.setEnableAmt(user.getEnableAmt().subtract(new BigDecimal(model.getApplyNums()).multiply(stockSubscribe.getPrice())));
//                int u = userMapper.updateById(user);
//                if (u <= 0) {
//                    throw new CustomException("未知原因，申购失败");
//                }
//            }
            model.setUserId(user.getId());
            model.setNewName(stockSubscribe.getName());
            model.setNewType(stockSubscribe.getStockType());
            model.setPhone(user.getPhone());
            model.setBuyPrice(stockSubscribe.getPrice());
            model.setBond(new BigDecimal(model.getApplyNums()).multiply(stockSubscribe.getPrice()));
            model.setRealName(Objects.equals(user.getRealName(), "") || user.getRealName() == null ? "模拟用户无实名" : user.getRealName());
            model.setAddTime(new Date());
            String orderNo = SnowIdUtil.getId(OrderConstant.StockSubscribe);
            model.setOrderNo(orderNo);
            model.setType(model.getType());
        }

        int ret = userStockSubscribeMapper.insert(model);
        if (ret > 0) {
            SiteMessage siteMessage = new SiteMessage();
            siteMessage.setUserId(user.getId());
            siteMessage.setMessageType(SiteMessage.MSG_TYPE_ADMIN);
            siteMessage.setStatus(1);
            siteMessage.setUserName(user.getRealName());
            siteMessage.setAddTime(new Date());
            siteMessage.setTypeName("新股申购申请");
            siteMessage.setContent("用户【" + user.getRealName() + "】发起新股申购申请");
            this.siteMessageMapper.insert(siteMessage);
        } else {
            throw new CustomException("申购失败");
        }
    }

    @Override
    public int update(UserStockSubscribe model) {
        int ret = userStockSubscribeMapper.updateById(model);
        return Math.max(ret, 0);
    }

    /**
     * admin 新股申购-添加和修改
     */
    @Override
    public void saveOne(UserStockSubscribe model) {
        int ret = 0;
//        log.info("model"+model);
        if (model.getId() != null) {
            //是修改
            if (model.getStatus() == 3 || model.getStatus() == 2) {
                model.setEndTime(DateTimeUtil.getCurrentDate());
            }
            UserStockSubscribe userStockSubscribe = userStockSubscribeMapper.load(model.getId());
            if (userStockSubscribe.getStatus() == 5) {
                throw new CustomException("已经转持仓了");
            }
//            else if (userStockSubscribe.getStatus() == 3) {
//                return ServerResponse.createByErrorMsg("已经审核过并且中签了，无法再次更改状态");
//            }else if (userStockSubscribe.getStatus() == 2) {
//                return ServerResponse.createByErrorMsg("已经审核过并且未中签");
//            }

            if (model.getStatus() == 3 && model.getApplyNumber() != null && userStockSubscribe.getStatus() == 1) {
                //待审核 - 已中签
                if (userStockSubscribe.getApplyNums() < model.getApplyNumber()) {
                    throw new CustomException("中签数量超过申购数量");
                }
                model.setBond(userStockSubscribe.getBuyPrice().multiply(BigDecimal.valueOf(model.getApplyNumber())));

                //给用户推送消息
                SiteMessage siteMessage = new SiteMessage();
                siteMessage.setUserId(userStockSubscribe.getUserId());
                siteMessage.setUserName(userStockSubscribe.getRealName());
                siteMessage.setTypeName("新股申购");
                siteMessage.setMessageType(SiteMessage.MSG_TYPE_WAP);
                siteMessage.setStatus(1);

                siteMessage.setAddTime(DateTimeUtil.getCurrentDate());


                if (userStockSubscribe.getType() == 2) {
                    User user = userMapper.selectById(userStockSubscribe.getUserId());
                    UserStockSubscribe userStockSubscribe1 = userStockSubscribeMapper.load(model.getId());
                    int referendum = userStockSubscribe1.getApplyNums() - model.getApplyNumber();
                    int refund = referendum * userStockSubscribe.getBuyPrice().intValue();
                    user.setEnableAmt(user.getEnableAmt().add(BigDecimal.valueOf(refund)));
                    int ret1 = userMapper.updateById(user);
                    if (ret1 <= 0) {
                        throw new CustomException("未知原因，申购失败");
                    }
                    siteMessage.setContent("【新股申购中签】恭喜您，新股申购中签成功，申购金额：" + userStockSubscribe.getBond() + "退还" + refund + "，请及时关注哦。");
                } else {

                    siteMessage.setContent("【新股申购中签】恭喜您，新股申购中签成功，申购金额：" + userStockSubscribe.getBond() + "，请及时关注哦。");
                }
                iSiteMessageService.insert(siteMessage);
            } else if (model.getStatus() == 2 && userStockSubscribe.getStatus() == 1) {
                //待审核 - 未中签
                //给达到消息强平提醒用户推送消息
                if (userStockSubscribe.getType() != 2) {
                    SiteMessage siteMessage = new SiteMessage();
                    siteMessage.setUserId(userStockSubscribe.getUserId());
                    siteMessage.setUserName(userStockSubscribe.getRealName());
                    siteMessage.setTypeName("新股申购");
                    siteMessage.setMessageType(SiteMessage.MSG_TYPE_WAP);
                    siteMessage.setStatus(1);
                    siteMessage.setContent("【新股申购未中签】很遗憾，您的新股申购本次未中签，申购金额：" + userStockSubscribe.getBond() + "。");
                    siteMessage.setAddTime(DateTimeUtil.getCurrentDate());
                    iSiteMessageService.insert(siteMessage);
                } else {
                    User user = userMapper.selectById(userStockSubscribe.getUserId());
                    user.setEnableAmt(user.getEnableAmt().add(userStockSubscribe.getBond()));
                    userMapper.updateById(user);
                    SiteMessage siteMessage = new SiteMessage();
                    siteMessage.setMessageType(SiteMessage.MSG_TYPE_WAP);
                    siteMessage.setUserId(userStockSubscribe.getUserId());
                    siteMessage.setUserName(userStockSubscribe.getRealName());
                    siteMessage.setTypeName("新股申购");
                    siteMessage.setStatus(1);
                    siteMessage.setContent("【新股申购未中签】很遗憾，您的新股申购本次未中签，申购金额：" + userStockSubscribe.getBond() + "已退还。");
                    siteMessage.setAddTime(DateTimeUtil.getCurrentDate());
                    iSiteMessageService.insert(siteMessage);
                }

            } else if (model.getStatus() == 5) {
                iUserPositionService.newStockToPosition(model.getId());
            }
            ret = userStockSubscribeMapper.updateById(model);
            if (ret > 0) {
            } else {
                throw new CustomException("未知原因，审核失败");
            }


        } else {
            if (model.getPhone() != null) {
                User user = userMapper.findByPhone(model.getPhone());
                if (user == null) {
                    throw new CustomException("用户不存在");
                }
                model.setRealName(user.getRealName());
                model.setUserId(user.getId());

                StockSubscribe stockSubscribe = stockSubscribeMapper.selectOne(new QueryWrapper<>(new StockSubscribe()).eq("code", model.getNewCode()));
                if (stockSubscribe == null) {
                    throw new CustomException("失败，新股信息不存在");
                }

                model.setNewName(stockSubscribe.getName());
                model.setBuyPrice(stockSubscribe.getPrice());
                if (model.getApplyNums() > stockSubscribe.getOrderNumber() * 10000 || model.getApplyNumber() > stockSubscribe.getOrderNumber() * 10000 || model.getApplyNums() < model.getApplyNumber()) {
                    throw new CustomException("申购数量或者中签数量异常");
                }
                model.setBond(model.getBuyPrice().multiply(BigDecimal.valueOf(model.getApplyNumber())));
                model.setAddTime(DateTimeUtil.getCurrentDate());
                String orderNo = SnowIdUtil.getId(OrderConstant.StockSubscribe);
                model.setOrderNo(orderNo);
                model.setType(stockSubscribe.getType());
                ret = userStockSubscribeMapper.insert(model);
            }
        }
        if (ret > 0) {
        } else {
            throw new CustomException("操作失败");
        }
    }

    /**
     * 发送站内信
     */
    @Override
    public void sendMsg(UserStockSubscribe model) {
        int ret = 0;

        if (model != null) {
            //所有人发站内信
            if (model.getUserId() == 0) {
                LambdaQueryWrapper<User> q = new LambdaQueryWrapper<>();
                q.like(StrUtil.isNotEmpty(model.getRealName()), User::getRealName, model.getRealName());
                q.like(StrUtil.isNotEmpty(model.getPhone()), User::getPhone, model.getPhone());
                List<User> users = iUserService.list(q);
                for (int k = 0; k < users.size(); k++) {
                    User user = users.get(k);
                    SiteMessage siteMessage = new SiteMessage();
                    siteMessage.setUserId(user.getId());
                    siteMessage.setUserName(user.getRealName());
                    siteMessage.setTypeName("站内消息");
                    siteMessage.setStatus(1);
                    siteMessage.setContent("【站内消息】" + model.getRemarks());
                    siteMessage.setAddTime(DateTimeUtil.getCurrentDate());
                    ret = iSiteMessageService.insert(siteMessage);
                }
            } else {
                //指定用户发站内信
                User user = userMapper.selectById(model.getUserId());
                SiteMessage siteMessage = new SiteMessage();
                siteMessage.setUserId(user.getId());
                siteMessage.setUserName(user.getRealName());
                siteMessage.setTypeName("站内消息");
                siteMessage.setStatus(1);
                siteMessage.setContent("【站内消息】" + model.getRemarks());
                siteMessage.setAddTime(DateTimeUtil.getCurrentDate());
                ret = iSiteMessageService.insert(siteMessage);
            }
        }
        if (ret <= 0) {
            throw new CustomException("操作失败");
        }
    }


    /*新股申购-查询列表*/
    @Override
    public PageInfo<UserStockSubscribe> getList(int pageNum, int pageSize, String keyword, Integer status) {
        PageHelper.startPage(pageNum, pageSize);
        List<UserStockSubscribe> listData = this.userStockSubscribeMapper.pageList(pageNum, pageSize, keyword, status);
        return new PageInfo<>(listData);
    }

    /*新股申购-查询用户最新新股申购数据*/
    @Override
    public List<UserStockSubscribe> getOneSubscribeByUserId(String type) {
        User user = iUserService.getCurrentUser();
        if (type == null || type.equals("")) {
            return this.userStockSubscribeMapper.selectList(new LambdaQueryWrapper<>(new UserStockSubscribe())
                    .eq(UserStockSubscribe::getPhone, user.getPhone())
                    .orderByDesc(UserStockSubscribe::getAddTime));
        }
        return this.userStockSubscribeMapper.selectList(new LambdaQueryWrapper<>(new UserStockSubscribe())
                .eq(UserStockSubscribe::getPhone, user.getPhone())
                .eq(UserStockSubscribe::getType, type)
                .orderByDesc(UserStockSubscribe::getAddTime));
    }

    /**
     * 新股申购-用户提交金额
     */
    @Override
    @Transactional
    public void userSubmit(Integer id) {
        int ret = 0;
        User user = iUserService.getCurrentUser();
        UserStockSubscribe userStockSubscribe = userStockSubscribeMapper.load(id);
        log.info("userStockSubscribe:{}", userStockSubscribe);
        if (userStockSubscribe != null && userStockSubscribe.getUserId().equals(user.getId())) {
            StockSubscribe stockSubscribe = stockSubscribeMapper.selectOne(new QueryWrapper<>(new StockSubscribe()).eq("code", userStockSubscribe.getNewCode()));
            if (userStockSubscribe.getType() == 2) {
                throw new CustomException("线下配售无需支付");
            }
            //判断时间
            if (stockSubscribe.getSubscriptionTime() == null || stockSubscribe.getSubscriptionTime().getTime() < DateTimeUtil.getCurrentDate().getTime()) {
                throw new CustomException("不在认缴时间");
            }
            if (userStockSubscribe.getStatus() == 3) {
                userStockSubscribe.setSubmitTime(DateTimeUtil.getCurrentDate());
                userStockSubscribe.setStatus(4);
                User user1 = userMapper.selectById(userStockSubscribe.getUserId());
                if (user1.getEnableAmt().compareTo(userStockSubscribe.getBond()) < 0) {
                    throw new CustomException("余额不足");
                }
                BigDecimal enableAmt = user1.getEnableAmt().subtract(userStockSubscribe.getBond());
                user1.setEnableAmt(enableAmt);
                ret = userMapper.updateById(user1);
            } else {
                throw new CustomException("未中签无需缴费");
            }
        } else {
            throw new CustomException("新股申购订单不存在！");
        }

        if (ret > 0) {
            ret = userStockSubscribeMapper.updateById(userStockSubscribe);
            if (ret > 0) {
            } else {
                throw new CustomException("操作失败");
            }
        } else {
            throw new CustomException("扣款失败");
        }

    }

    /**
     * 新股申购-删除
     *
     * @param id
     * @return
     */
    @Override
    public void del(int id) {
        int ret = id > 0 ? userStockSubscribeMapper.deleteById(id) : 0;
        if (ret <= 0) {
            throw new CustomException("操作失败");
        }
    }

    /**
     * 新股抢筹 下单
     */
    @Override
    public void buyNewStockQc(String code, Integer num) {
        User user = this.iUserService.getCurrentUser();

        if (code == null || code.isEmpty() || num == null || num <= 0) {
            throw new CustomException("股票代码不能为空或者购买数量异常");
        }
        StockSubscribe stockSubscribe = stockSubscribeMapper.selectOne(new QueryWrapper<StockSubscribe>().eq("code", code));
        //实名认证开关
        SiteProduct siteProduct = iSiteProductService.getProductSetting();
        if (siteProduct.getRealNameDisplay() && (StringUtils.isBlank(user.getRealName()) || StringUtils.isBlank(user.getIdCard()))) {
            throw new CustomException("下单失败，请先实名认证");
        }
//                判断休息日不能买入
        if (siteProduct.getHolidayDisplay()) {
            throw new CustomException("周末或节假日不能交易！");
        }
        //重复申购限制
//                UserStockSubscribe userStockSubscribe = userStockSubscribeMapper.selectOne(new QueryWrapper<UserStockSubscribe>().eq("new_code", model.getNewCode()).eq("user_id", user.getId()));
//                if (userStockSubscribe != null) {
//                    return ServerResponse.createByErrorMsg("请勿重复申购");
//                }
        if (siteProduct.getRealNameDisplay() && user.getIsLock().intValue() == 1) {
            throw new CustomException("下单失败，账户已被锁定");
        }
        if (stockSubscribe == null) {
            throw new CustomException("新股代码不存在");
        }


        if ("sh".equals(stockSubscribe.getStockType()) || "sh".equals(stockSubscribe.getStockType()) && num < 1000) {
            throw new CustomException("沪市新股申购数量最小为1000股");
        } else if ("sz".equals(stockSubscribe.getStockType()) || "sz".equals(stockSubscribe.getStockType()) && num < 500) {
            throw new CustomException("深市新股申购数量最小为500股");
        }

        if (user.getEnableAmt().compareTo(new BigDecimal(num).multiply(stockSubscribe.getPrice())) < 0) {
            throw new CustomException("用户可用余额不足，申购条件不满足");
        }
        user.setEnableAmt(user.getEnableAmt().subtract(new BigDecimal(num).multiply(stockSubscribe.getPrice())));
//                if (user.getDjzj()!=null) {
//                        user.setDjzj(user.getDjzj().add(new BigDecimal(model.getApplyNums()).multiply(stockSubscribe.getPrice())));
//                }else
//                {
//                        user.setDjzj(new BigDecimal(model.getApplyNums()).multiply(stockSubscribe.getPrice()));
//                }
        int u = userMapper.updateById(user);
        if (u <= 0) {
            throw new CustomException("未知原因，申购失败");
        }
        UserStockSubscribe userStockSubscribe = new UserStockSubscribe();
        userStockSubscribe.setUserId(user.getId());
        userStockSubscribe.setNewName(stockSubscribe.getName());
        userStockSubscribe.setPhone(user.getPhone());
        userStockSubscribe.setApplyNums(num);
        userStockSubscribe.setNewType(stockSubscribe.getStockType());
        userStockSubscribe.setBuyPrice(stockSubscribe.getPrice());
        userStockSubscribe.setNewCode(stockSubscribe.getCode());
        userStockSubscribe.setBond(new BigDecimal(num).multiply(stockSubscribe.getPrice()));
        userStockSubscribe.setRealName(Objects.equals(user.getRealName(), "") || user.getRealName() == null ? "模拟用户无实名" : user.getRealName());
        userStockSubscribe.setAddTime(new Date());
        String orderNo = SnowIdUtil.getId(OrderConstant.StockSubscribe);
        userStockSubscribe.setOrderNo(orderNo);
        userStockSubscribe.setType(3);
        int ret = userStockSubscribeMapper.insert(userStockSubscribe);
        if (ret > 0) {

            SiteMessage siteMessage = new SiteMessage();
            siteMessage.setUserId(user.getId());
            siteMessage.setMessageType(SiteMessage.MSG_TYPE_ADMIN);
            siteMessage.setStatus(1);
            siteMessage.setUserName(user.getRealName());
            siteMessage.setAddTime(new Date());
            siteMessage.setTypeName("新股抢筹下单");
            siteMessage.setContent("用户【" + user.getRealName() + "】发起新股抢筹下单");
            this.siteMessageMapper.insert(siteMessage);

        } else {
            throw new CustomException("申购抢筹失败");
        }
    }

    /**
     * 新股抢筹 股票列表
     */
    @Override
    public List<StockSubscribe> getStockQcList() {
        String nowDate = DateTimeUtil.stampToDate(String.valueOf(System.currentTimeMillis()));
        return this.stockSubscribeMapper.selectList(new LambdaQueryWrapper<StockSubscribe>()
                .eq(StockSubscribe::getListDate, nowDate));
    }


    /**
     * 用户新股抢筹列表
     *
     * @param pageNum
     * @param pageSize
     * @param keyword
     * @return
     */
    @Override
    public PageInfo<UserStockSubscribe> getQcList(int pageNum, int pageSize, String keyword) {
        PageHelper.startPage(pageNum, pageSize);
        List<UserStockSubscribe> qcList;
        if (StringUtils.isNotEmpty(keyword)) {
            qcList = userStockSubscribeMapper.selectList(new QueryWrapper<UserStockSubscribe>()
                    .like("phone", keyword)
                    .or().like("new_code", keyword)
                    .or().like("new_name", keyword)
                    .or().like("status", keyword)
                    .eq("type", 3)
                    .orderByDesc("add_time"));
        } else {
            qcList = userStockSubscribeMapper.selectList(new QueryWrapper<UserStockSubscribe>()
                    .eq("type", 3)
                    .orderByDesc("add_time"));
        }
        return new PageInfo<>(qcList);
    }

    /**
     * 新股抢筹审核
     *
     * @param status
     * @return
     */
    @Override
    public void updateQcByAdmin(String id, String status, String num) {
        if (StringUtils.isEmpty(id) || StringUtils.isEmpty(status)) {
            throw new CustomException("参数错误");
        }
        UserStockSubscribe userStockSubscribe = userStockSubscribeMapper.selectById(id);
        if (userStockSubscribe == null) {
            throw new CustomException("抢筹记录不存在");
        }
        User user = userMapper.selectById(userStockSubscribe.getUserId());
        if (user == null) {
            throw new CustomException("用户不存在");
        }

        if (userStockSubscribe.getStatus() == 1) {
            if ("2".equals(status)) {
                user.setEnableAmt(user.getEnableAmt().add(userStockSubscribe.getBond()));
                int ret = userMapper.updateById(user);
                if (ret <= 0) {
                    throw new CustomException("未知原因，审核失败");
                }
                userStockSubscribe.setStatus(2);
                userStockSubscribe.setEndTime(new Date());
                int ret1 = userStockSubscribeMapper.updateById(userStockSubscribe);
                if (ret1 > 0) {
                } else {
                    throw new CustomException("审核失败");
                }
            } else if ("3".equals(status)) {
                if (StringUtils.isEmpty(num) || Integer.parseInt(num) <= 0 || Integer.parseInt(num) > userStockSubscribe.getApplyNums()) {
                    throw new CustomException("中签数量不能为空，且不能大于申购数量");
                }
                Stock stock = this.iStockService.findStockByCode(userStockSubscribe.getNewCode());
                if (stock == null) {
                    log.info("股票不存在");
                    throw new CustomException("股票不存在");
                }
                SiteSetting siteSetting = this.iSiteSettingService.getSiteSetting();
                if (siteSetting == null) {
                    log.error("下单出错，网站设置表不存在");
                    throw new CustomException("下单失败，系统设置错误");
                }
                BigDecimal buy_amt = (userStockSubscribe.getBuyPrice()).multiply(new BigDecimal(num));
                UserPosition userPosition = new UserPosition();
                String orderNo = SnowIdUtil.getId(OrderConstant.userPositionOrder);
                userPosition.setPositionSn(orderNo);
                userPosition.setUserId(user.getId());
                userPosition.setNickName(user.getRealName());
                userPosition.setStockCode(stock.getStockCode());
                userPosition.setStockName(stock.getStockName());
                userPosition.setStockGid(stock.getStockGid());
                userPosition.setStockSpell(stock.getStockSpell());
                userPosition.setBuyOrderId(GeneratePosition.getPositionId());
                userPosition.setBuyOrderTime(new Date());
                userPosition.setBuyOrderPrice(userStockSubscribe.getBuyPrice());
                userPosition.setOrderDirection("买涨");
                userPosition.setOrderNum(Integer.valueOf(num));
                userPosition.setStockPlate(stock.getStockPlate());
                userPosition.setIsLock(0);
                userPosition.setOrderTotalPrice(buy_amt);
                //递延费特殊处理
                BigDecimal buy_fee_amt = buy_amt.multiply(siteSetting.getBuyFee()).setScale(2, 4);
                userPosition.setOrderFee(buy_fee_amt);
                StockListVO stockListVO = SinaStockApi.assembleStockListVO(SinaStockApi.getSinaStock(stock.getStockGid()));
                BigDecimal now_price = stockListVO.getNowPrice();
//                if (now_price.compareTo(new BigDecimal("0")) == 0) {
//                    log.info(stock.getStockGid()+"报价0，");
//                    return ServerResponse.createByErrorMsg("报价0，请稍后再试");
//
//                }

//                double stock_crease = stockListVO.getHcrate().doubleValue();
//                SiteSpread siteSpread = iSiteSpreadService.findSpreadRateOne(new BigDecimal(stock_crease), buy_amt, stock.getStockCode(), now_price);
//                if(siteSpread != null){
//                    spread_rate_amt = buy_amt.multiply(siteSpread.getSpreadRate()).setScale(2, 4);
//                    log.info("用户购买点差费（配资后总资金 * 百分比{}） = {}", siteSpread.getSpreadRate(), spread_rate_amt);
//                } else{
//                    log.info("用户购买点差费（配资后总资金 * 百分比{}） = {}", "设置异常", spread_rate_amt);
//                }
                BigDecimal profit_and_lose = new BigDecimal("0");
                userPosition.setProfitAndLose(profit_and_lose);
                BigDecimal all_profit_and_lose = profit_and_lose.subtract(buy_fee_amt);
                userPosition.setAllProfitAndLose(all_profit_and_lose);


                int insertPositionCount = this.userPositionMapper.insert(userPosition);
                if (insertPositionCount > 0) {
                    log.info("【抢筹创建持仓】保存记录成功");
                } else {
                    log.error("【抢筹创建持仓】保存记录出错");
                }
                userStockSubscribe.setStatus(3);
                userStockSubscribe.setEndTime(new Date());
                userStockSubscribe.setFixTime(new Date());
                userStockSubscribe.setApplyNumber(Integer.valueOf(num));
                this.userStockSubscribeMapper.updateById(userStockSubscribe);
                BigDecimal reimburse = new BigDecimal(userStockSubscribe.getApplyNums() - Integer.parseInt(num)).multiply(userStockSubscribe.getBuyPrice());
                user.setEnableAmt(user.getEnableAmt().add(reimburse));
                int ret = userMapper.updateById(user);
                if (ret <= 0) {
                    throw new CustomException("未知原因，审核失败");
                }
            }
        }
        userStockSubscribe.setStatus(Integer.valueOf(status));
        userStockSubscribe.setApplyNumber(Integer.valueOf(num));
        userStockSubscribe.setEndTime(new Date());
        int res = this.userStockSubscribeMapper.updateById(userStockSubscribe);
        if (res <= 0) {
            throw new CustomException("修改状态失败");
        }
    }

    /**
     * 新股抢筹添加
     *
     * @param phone
     * @param code
     * @param num
     * @return
     */

    @Override
    public void addQcByAdmin(String phone, String code, String num) {
        if (StringUtils.isEmpty(phone) || StringUtils.isEmpty(code) || StringUtils.isEmpty(num)) {
            throw new CustomException("参数错误");
        }
        User user = userMapper.selectOne(new QueryWrapper<User>().eq("phone", phone));
        if (user == null) {
            throw new CustomException("用户不存在");
        }
        StockSubscribe stockSubscribe = stockSubscribeMapper.selectOne(new QueryWrapper<StockSubscribe>().eq("code", code));
        if (stockSubscribe == null) {
            throw new CustomException("新股代码不存在");
        }
        UserStockSubscribe userStockSubscribe = new UserStockSubscribe();
        String orderNo = SnowIdUtil.getId(OrderConstant.StockSubscribe);
        userStockSubscribe.setOrderNo(orderNo);
        userStockSubscribe.setUserId(user.getId());
        userStockSubscribe.setRealName(user.getRealName() == null ? "模拟用户无实名" : user.getRealName());
        userStockSubscribe.setPhone(user.getPhone());
        userStockSubscribe.setNewCode(stockSubscribe.getCode());
        userStockSubscribe.setNewName(stockSubscribe.getName());
        userStockSubscribe.setBond(new BigDecimal(num).multiply(stockSubscribe.getPrice()));
        userStockSubscribe.setBuyPrice(stockSubscribe.getPrice());
        userStockSubscribe.setApplyNums(Integer.valueOf(num));
        userStockSubscribe.setType(3);
        userStockSubscribe.setStatus(1);
        userStockSubscribe.setAddTime(new Date());
        userStockSubscribe.setNewType(stockSubscribe.getStockType());
        int ret = userStockSubscribeMapper.insert(userStockSubscribe);
        if (ret > 0) {
        } else {
            throw new CustomException("添加失败");
        }
    }

    @Override
    public List<UserStockSubscribe> getzqjkl() {
        User user = this.iUserService.getCurrentUser();
        if (user == null) {
            throw new CustomException("用户未登录");
        }
        return userStockSubscribeMapper.selectList(new LambdaQueryWrapper<UserStockSubscribe>()
                .eq(UserStockSubscribe::getPhone, user.getPhone())
                .ge(UserStockSubscribe::getStatus, 3)
                .orderByDesc(UserStockSubscribe::getAddTime));
    }
}