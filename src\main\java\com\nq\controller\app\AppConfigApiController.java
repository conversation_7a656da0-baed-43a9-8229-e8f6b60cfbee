package com.nq.controller.app;

import com.nq.common.ServerResponse;
import com.nq.service.*;
import com.nq.vo.app.AppConfigVO;
import com.nq.vo.app.FollowLevelVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "APP-配置说明的类")
@RestController
@RequestMapping("/api/config")
public class AppConfigApiController {

    @Resource
    private LevelService levelService;

    @Resource
    private AppConfigService appConfigService;

    @ApiOperation(value = "查询配置组", notes = "不传参数默认返回question组的配置，传入group参数则返回指定组的配置")
    @GetMapping("/queryQuestion.do")
    public ServerResponse<List<AppConfigVO>> queryQuestion(
            @RequestParam(value = "group", required = false) String group) {
        // 如果没有传参数，默认使用"question"组
        String groupName = (group != null && !group.isEmpty()) ? group : "question";
        // 获取配置列表
        List<AppConfigVO> appConfigVOS = appConfigService.queryGroupConfig(groupName);
        return ServerResponse.createBySuccess(appConfigVOS);
    }

    @ApiOperation("查询各种配置 (跟单制度,队长返佣,团队制度备注 如果是问题的话 传入备注)")
    @GetMapping("/queryConfig.do")
    public ServerResponse<AppConfigVO> queryConfig(@RequestParam(value = "key", required = false) String key) {
        // 获取当前用户
        AppConfigVO appConfigVo = appConfigService.queryConfig(key);
        return ServerResponse.createBySuccess(appConfigVo);
    }

    @ApiOperation("查询跟单制度表格 和团队中的核定表格")
    @GetMapping("/queryTradeZD.do")
    public ServerResponse<List<FollowLevelVO>> queryTradeZD() {
        List<FollowLevelVO> voList = levelService.queryTradeZD();
        return ServerResponse.createBySuccess(voList);
    }

}
