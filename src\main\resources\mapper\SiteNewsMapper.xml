<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nq.dao.SiteNewsMapper">

    <resultMap id="BaseResultMap" type="com.nq.pojo.SiteNews" >
        <result column="id" property="id" />
        <result column="type" property="type" />
        <result column="title" property="title" />
        <result column="source_id" property="sourceId" />
        <result column="source_name" property="sourceName" />
        <result column="views" property="views" />
        <result column="status" property="status" />
        <result column="show_time" property="showTime" />
        <result column="add_time" property="addTime" />
        <result column="update_time" property="updateTime" />
        <result column="imgurl" property="imgurl" />
        <result column="description" property="description" />
        <result column="content" property="content" />
    </resultMap>

    <sql id="Base_Column_List">
                id,
                type,
                title,
                source_id,
                source_name,
                views,
                status,
                show_time,
                add_time,
                update_time,
                imgurl,
                description,
                content
    </sql>



    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM site_news
        WHERE id = #{id}
    </select>


    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM site_news
    </select>

    <!--根据来源id查询新闻数-->
    <select id="getNewsBySourceIdCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM site_news
        WHERE source_id = #{sourceId}
    </select>

    <!--修改新闻浏览量-->
    <update id="updateViews" >
        UPDATE site_news
        <set>
            views = views + 1
        </set>
        WHERE id = #{id}
    </update>

    <!--top最新新闻资讯-->
    <select id="getTopNewsList" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List" />
        FROM site_news
        where status=1
        order by show_time desc
        limit #{pageSize}
    </select>

</mapper>