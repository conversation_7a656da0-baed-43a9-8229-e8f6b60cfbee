package com.nq.controller.admin;


import com.nq.common.ServerResponse;
import com.nq.service.ISiteAdminService;
import com.nq.service.IStockService;
import com.nq.vo.admin.AdminStatisticVO;
import com.nq.vo.stock.MarketVOResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@RestController
@RequestMapping({"/admin/index/"})
@Api(tags = {"后台-首页管理"})
public class AdminIndexController {
    private static final Logger log = LoggerFactory.getLogger(AdminIndexController.class);
    @Resource
    private ISiteAdminService iSiteAdminService;
    @Resource
    private IStockService iStockService;

    @ApiOperation("查询大盘指数信息")
    @GetMapping({"getMarket.do"})
    public ServerResponse<MarketVOResult> getMarket() {
        MarketVOResult marketVOResult = iStockService.getMarket();
        return ServerResponse.createBySuccess(marketVOResult);
    }

    @ApiOperation("统计")
    @GetMapping({"statisticCount.do"})
    public ServerResponse<AdminStatisticVO> statisticCount() {
        // TODO: 实现统计逻辑
        AdminStatisticVO vo = iSiteAdminService.statisticCount();
        return ServerResponse.createBySuccess(vo);
    }

}
