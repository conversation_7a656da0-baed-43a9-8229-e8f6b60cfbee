package com.nq.utils.task.follow;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.nq.function.AmountConsumer;
import com.nq.job.task.ITask;
import com.nq.manage.UserAmountChangeManage;
import com.nq.pojo.Follow;
import com.nq.pojo.FollowDetail;
import com.nq.service.FollowDetailService;
import com.nq.service.FollowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Component("followDealTask")
public class FollowDealTask implements ITask {

    //手写 嫌弃刷新下用户信息
    @Resource
    private FollowService followService;
    @Resource
    private FollowDetailService followDetailService;
    @Resource
    private UserAmountChangeManage userAmountChangeManage;


    //    @Scheduled(cron = "0 0 12 * * ?")
    @Override
    public void run(String params) {
        log.info("开始执行跟单详情任务"); // 日志记录
        //查询这里的信息 看看有没有 跟单结束的
        LambdaQueryWrapper<Follow> q = new LambdaQueryWrapper<Follow>();
        q.apply("used_days >= package_days");
        q.eq(Follow::getStatus, 2);
        List<Follow> list = followService.list(q);
        for (Follow follow : list) {
            try {
                //查询所有跟单详情
                Integer followId = follow.getId();
                LambdaQueryWrapper<FollowDetail> fd = new LambdaQueryWrapper<FollowDetail>();
                fd.eq(FollowDetail::getFollowId, followId);
                List<FollowDetail> followDetails = followDetailService.list(fd);
                BigDecimal total = BigDecimal.ZERO;
                BigDecimal benji = follow.getAddAmount().add(follow.getAmount());
                Integer userId = follow.getUserId();
                //需要执行两次  一次本金退回 一次利润退回然后 保存记录
                for (FollowDetail followDetail : followDetails) {
                    BigDecimal profit = followDetail.getProfit();
                    BigDecimal salary = followDetail.getSalary();
                    BigDecimal earn = profit.subtract(salary);
                    total = total.add(earn);
                }
                String followNo = follow.getFollowNo();

                AmountConsumer consumer = (oldUser, newUser) -> {
                    follow.setStatus(5);
                    LambdaUpdateWrapper<FollowDetail> wrapper = new LambdaUpdateWrapper<FollowDetail>();
                    wrapper.eq(FollowDetail::getFollowId, followId);
                    wrapper.set(FollowDetail::getSettlementStatus, 1);
                    // 3. 更新状态为已撤销
                    followService.updateById(follow);
                    followDetailService.update(wrapper);
                };
                userAmountChangeManage.changeFollowBalance(userId, followNo, benji, total, consumer);
            } catch (Exception e) {
                log.error("跟单结算处理异常，followId={}，异常信息：{}", follow.getId(), e.getMessage(), e);
                // 跳过本次，继续下一个
            }
        }
    }

}