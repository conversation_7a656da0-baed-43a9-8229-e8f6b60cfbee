<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nq.dao.AppConfigMapper">

    <resultMap id="BaseResultMap" type="com.nq.pojo.AppConfig">
        <id column="id" property="id"/>
        <result column="group_name" property="groupName"/>
        <result column="config_name" property="configName"/>
        <result column="config_value" property="configValue"/>
        <result column="type" property="type"/>
        <result column="remark" property="remark"/>
        <result column="created_time" property="createdTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, group_name, config_name, config_value, type, remark, created_time
    </sql>

</mapper> 