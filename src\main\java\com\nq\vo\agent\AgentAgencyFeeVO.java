package com.nq.vo.agent;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
@Data
public class AgentAgencyFeeVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 代理id
     */
    private Integer agentId;

    /**
     * 状态：1、可用，0、停用
     */
    private Integer status;

    /**
     * 业务主键id
     */
    private Integer businessId;

    /**
     * 费用类型：1、入仓手续费，2、平仓手续费，3、延递费(留仓费)，4、分红
     */
    private Integer feeType;

    /**
     * 收支类型：1、收，2、支
     */
    private Integer aymentType;

    /**
     * 总金额
     */
    private BigDecimal totalMoney;

    /**
     * 利润金额
     */
    private BigDecimal profitMoney;

    /**
     * 添加时间
     */
    private Date addTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remarks;


}
