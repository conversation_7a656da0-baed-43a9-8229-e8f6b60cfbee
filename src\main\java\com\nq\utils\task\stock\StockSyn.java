package com.nq.utils.task.stock;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.nq.dao.StockMapper;
import com.nq.job.task.ITask;
import com.nq.pojo.Stock;
import com.nq.service.IStockService;
import com.nq.utils.DateTimeUtil;
import com.nq.utils.HttpClientRequest;
import com.nq.utils.stock.pinyin.GetPyByChinese;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Component("stockSyn")
public class StockSyn implements ITask {

    @Resource
    private StockMapper stockMapper;
    //股票同步
    @Override
    public void run(String params) throws InterruptedException {
        /**
         * sh_a https://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeData?page=1&num=80&sort=symbol&asc=1&node=sh_a&symbol=&_s_r_a=init
         * sz_a https://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeData?page=1&num=80&sort=symbol&asc=1&node=sz_a&symbol=&_s_r_a=init
         * hs_bjs https://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeData?page=1&num=80&sort=symbol&asc=1&node=hs_bjs&symbol=&_s_r_a=init
         * cyb https://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeData?page=1&num=80&sort=symbol&asc=1&node=cyb&symbol=&_s_r_a=init
         * kcb https://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeData?page=1&num=80&sort=symbol&asc=1&node=kcb&symbol=&_s_r_a=init
         * 总条数 https://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeStockCount?node=hs_bjs
         */
        log.info("====={股票同步任务开启} =====", DateTimeUtil.dateToStr(new Date()));
        String[] keys = {"sh_a", "sz_a", "hs_bjs", "cyb", "kcb"};
        for (String key : keys) {
            StringBuilder sb = new StringBuilder();
            int pageNumber = 34;
            String num = null;
            try {
                num = HttpClientRequest.doGet("https://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeStockCount?node=" + key);
            } catch (Exception e) {
                log.error("计算页码请求出错", e);
                Thread.sleep(30000L);
                num = HttpClientRequest.doGet("https://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeStockCount?node=" + key);
            }
            if (num != null) {
                num = num.substring(1, num.length() - 1);
                int total = Integer.parseInt(num);
                if (total % 80 == 0) {
                    pageNumber = total / 80;
                } else {
                    pageNumber = total / 80 + 1;
                }
            }
            for (int i = 0; i < pageNumber; i++) {
                try {
                    String result = null;
                    try {
                        result = HttpClientRequest.doGet("https://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeData?page=" + i + "&num=80&sort=symbol&asc=1&node=" + key + "&symbol=&_s_r_a=init");
                    } catch (Exception e) {
                        log.error("请求失败" + e);
                        Thread.sleep(30000L);
                        result = HttpClientRequest.doGet("https://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeData?page=" + i + "&num=80&sort=symbol&asc=1&node=" + key + "&symbol=&_s_r_a=init");
                    }
                    JSONArray hsArray = JSONObject.parseArray(result);
                    for (Object o : hsArray) {
                        JSONObject hsjson = JSONObject.parseObject(o.toString());
                        Stock stock = stockMapper.findStockByCode(hsjson.getString("code"));
                        if (stock == null) {
                            stock = new Stock();
                            String type = hsjson.getString("symbol").substring(0, 2);
                            String spell = GetPyByChinese.converterToFirstSpell(hsjson.getString("name"));
                            stock.setStockCode(hsjson.getString("code"));
                            stock.setStockName(hsjson.getString("name"));
                            stock.setStockType(type);
                            stock.setStockGid(hsjson.getString("symbol"));
                            stock.setStockSpell(spell);
                            stock.setIsLock(0);
                            stock.setIsShow(0);
                            stock.setDataBase(0);
                            stock.setAddTime(new Date());
                            stockMapper.insert(stock);
                            sb.append("新增股票：").append(stock.getStockName()).append(stock.getStockCode()).append("/n");
                        }

                        if ("cyb".equals(key) && stock.getStockPlate() == null) {
                            stock.setStockPlate("创业");
                            stockMapper.updateById(stock);
                        } else if ("kcb".equals(key) && stock.getStockPlate() == null) {
                            stock.setStockPlate("科创");
                            stockMapper.updateById(stock);
                        }

                    }

                } catch (Exception e) {
                    log.error("同步出错", e);
                }
            }

            log.info(key + "股新增股票：{}", sb.toString());
        }
        log.info("====={股票同步任务结束} =====", DateTimeUtil.dateToStr(new Date()));
    }
}
