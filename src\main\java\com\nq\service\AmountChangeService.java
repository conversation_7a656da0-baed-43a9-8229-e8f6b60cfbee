package com.nq.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.dto.app.AmountChangeParam;
import com.nq.pojo.AmountChange;
import com.nq.vo.user.AmountChangeVO;

public interface AmountChangeService extends IService<AmountChange> {
    
    /**
     * 分页查询账变记录
     * @param page 页码
     * @param size 每页大小
     * @param userId 用户ID
     * @param amountType 账户类型
     * @param accountType 收支类型
     * @param isAgent
     * @return 分页数据
     */
    // amountType, type, accountType
    PageInfo<AmountChangeVO> pageList(Integer page, Integer size, Long userId, String nickName, Integer amountType, Integer type, Integer accountType, boolean isAgent);
    
    /**
     * 获取账变详情
     * @param id 账变ID
     * @return 账变详情
     */
    AmountChangeVO getDetailById(Long id);

    /**
     * 账变记录列表
     * @param amountChangeParam
     * @return
     */
    PageInfo<AmountChangeVO> listOmitInfo(AmountChangeParam amountChangeParam);
}