package com.nq.controller.agent;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dto.common.CommonPage;
import com.nq.pojo.FollowDetail;
import com.nq.service.FollowDetailService;
import com.nq.vo.admin.FollowAdminDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "代理后台-跟单详情管理")
@RestController
@RequestMapping("/agent/follow/detail/")
public class AgentFollowDetailController {

    @Resource
    private FollowDetailService followDetailService;

    @ApiOperation("分页查询跟单详情列表")
    @GetMapping("list.do")
    public ServerResponse<PageInfo<FollowAdminDetailVO>> pageList(FollowDetail followDetail, CommonPage commonPag) {
        PageInfo<FollowAdminDetailVO>  voPageInfo =  followDetailService.pageList(followDetail,
                commonPag, true);
        return ServerResponse.createBySuccess(voPageInfo);
    }

    @ApiOperation("获取跟单详情")
    @PostMapping("detail.do")
    public ServerResponse<FollowAdminDetailVO> getDetail(@RequestBody FollowDetail followDetail) {
        FollowAdminDetailVO vo = followDetailService.getDetail(followDetail.getId());
        return ServerResponse.createBySuccess(vo);
    }


} 