package com.nq.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.dao.AmountChangeMapper;
import com.nq.dto.app.AmountChangeParam;
import com.nq.enums.AccountTypeEnum;
import com.nq.enums.OrderTypeEnum;
import com.nq.enums.TypeEnum;
import com.nq.pojo.AmountChange;
import com.nq.pojo.User;
import com.nq.service.AmountChangeService;
import com.nq.service.IUserService;
import com.nq.utils.PageUtil;
import com.nq.vo.user.AmountChangeVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class AmountChangeServiceImpl extends ServiceImpl<AmountChangeMapper, AmountChange> implements AmountChangeService {

    private final IUserService iUserService;

    public AmountChangeServiceImpl(IUserService iUserService) {
        this.iUserService = iUserService;
    }

    @Override     // amountType, type, accountType
    public PageInfo<AmountChangeVO> pageList(Integer page, Integer size, Long userId, String nickName, Integer amountType, Integer type, Integer accountType, boolean isAgent) {
        PageHelper.startPage(page, size);
        
        LambdaQueryWrapper<AmountChange> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(userId != null, AmountChange::getUserId, userId)
                .eq(type != null, AmountChange::getType, type)
                .eq(amountType != null, AmountChange::getAmountType, amountType)
                .eq(accountType != null, AmountChange::getAccountType, accountType)
                .eq(StrUtil.isNotEmpty(nickName), AmountChange::getNickName, nickName)
                .in(isAgent, AmountChange::getUserId, iUserService.getUserIdByAgent())
                .orderByDesc(AmountChange::getCreateTime);
        List<AmountChange> list = this.list(wrapper);
        List<AmountChangeVO> voList = list.stream().map(this::convertToVO).collect(Collectors.toList());
        PageInfo<AmountChangeVO> voPageInfo = PageUtil.buildPageDto(list, voList);

        return voPageInfo;
    }

    @Override
    public AmountChangeVO getDetailById(Long id) {
        AmountChange amountChange = this.getById(id);
        if (amountChange == null) {
            return null;
        }
        return convertToVO(amountChange);
    }

    @Override
    public PageInfo<AmountChangeVO> listOmitInfo(AmountChangeParam amountChangeParam) {
        User currentUser = iUserService.getCurrentUser();
        PageHelper.startPage(amountChangeParam.getPageNum(), amountChangeParam.getPageSize());
        LambdaQueryWrapper<AmountChange> q = new LambdaQueryWrapper<>();
        q.eq(AmountChange::getUserId, currentUser.getId());
        q.eq(AmountChange::getAmountType, amountChangeParam.getAmountType());
        q.eq(amountChangeParam.getAccountType()!=null,AmountChange::getAccountType, amountChangeParam.getAccountType());
        q.orderByDesc(AmountChange::getId);
        List<AmountChange> list = this.list(q);
        List<AmountChangeVO> voList = list.stream().map(this::convertToVO).collect(Collectors.toList());
        PageInfo<AmountChangeVO> pageInfo = PageUtil.buildPageDto(list, voList);
        return pageInfo;
    }

    private AmountChangeVO convertToVO(AmountChange amountChange) {
        AmountChangeVO vo = new AmountChangeVO();
        BeanUtils.copyProperties(amountChange, vo);
        
        // 设置订单类型描述
        OrderTypeEnum orderTypeEnum = OrderTypeEnum.getByCode(amountChange.getOrderType());
        if (orderTypeEnum != null) {
            vo.setOrderTypeDesc(orderTypeEnum.getDesc());
        }
        
        // 设置收支类型描述
        AccountTypeEnum accountTypeEnum = AccountTypeEnum.getByCode(amountChange.getAccountType());
        if (accountTypeEnum != null) {
            vo.setAccountTypeDesc(accountTypeEnum.getDesc());
        }

        // 设置账变类型描述
        TypeEnum typeEnum = TypeEnum.getByCode(amountChange.getType());
        if (typeEnum != null) {
            vo.setTypeDesc(typeEnum.getDesc());
        }
        
        return vo;
    }
} 