package com.nq.controller.admin;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.AppConfig;
import com.nq.service.AppConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "后台-APP配置管理")
@RestController
@RequestMapping("/admin/app/config")
public class AdminAppConfigController {

    @Resource
    private AppConfigService appConfigService;

    @ApiOperation("分页查询配置列表")
    @GetMapping("/list.do")
    public ServerResponse<PageInfo<AppConfig>> list(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("组名") @RequestParam(required = false) String groupName,
            @ApiParam("项") @RequestParam(required = false) String configName,
            @ApiParam("类型") @RequestParam(required = false) String type
    ) {
        PageInfo<AppConfig> pageInfo = appConfigService.pageList(pageNum, pageSize, groupName, configName, type);
        return ServerResponse.createBySuccess(pageInfo);
    }

    @ApiOperation("新增配置")
    @PostMapping("/add.do")
    public ServerResponse<String> add(@RequestBody AppConfig appConfig) {
        appConfigService.add(appConfig);
        return ServerResponse.createBySuccessMsg("添加成功");
    }

    @ApiOperation("修改配置")
    @PostMapping("/update.do")
    public ServerResponse<String> update(@RequestBody AppConfig appConfig) {
        appConfigService.update(appConfig);
        return ServerResponse.createBySuccessMsg("修改成功");
    }

    @ApiOperation("删除配置")
    @GetMapping("/delete.do")
    public ServerResponse<String> delete(@RequestParam Integer id) {
        appConfigService.delete(id);
        return ServerResponse.createBySuccessMsg("删除成功");
    }

    @ApiOperation("获取配置详情")
    @PostMapping("/detail.do")
    public ServerResponse<AppConfig> detail(@RequestParam String groupName,
                                          @RequestParam String configName) {
        AppConfig appConfig = appConfigService.getByGroupAndName(groupName, configName);
        return ServerResponse.createBySuccess(appConfig);
    }
} 