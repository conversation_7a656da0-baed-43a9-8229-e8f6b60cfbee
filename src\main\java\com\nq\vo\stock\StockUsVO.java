
package com.nq.vo.stock;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class StockUsVO {
    private int id;

    private String name;

    private String code;

    private String spell;

    private String gid;

    private String nowPrice;

    private BigDecimal hcrate;

    private String today_max;

    private String today_min;

    private String business_balance;

    private String business_amount;

    private String preclose_px;

    private String open_px;

    private String type;

    private String timeDate;

    private Integer depositAmt;


}

