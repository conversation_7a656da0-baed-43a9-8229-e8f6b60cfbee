package com.nq.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dto.app.MoreFollowApplyDTO;
import com.nq.dto.app.SingleFollowApplyDTO;
import com.nq.pojo.Package;
import com.nq.pojo.User;
import com.nq.vo.admin.PackageVO;
import com.nq.vo.app.AppMorePackageVO;

import java.util.List;

public interface PackageService extends IService<Package> {

    /**
     * 分页查询套餐列表
     * @param page 页码
     * @param size 每页大小
     * @param productName 产品名称
     * @param sortField 排序字段
     * @return 分页结果
     */
    PageInfo<PackageVO> pageList(Integer page, Integer size, String productName, String sortField);

    /**
     * 获取套餐详情
     */
    PackageVO getDetailById(Integer id);

    /**
     * 多个
     * @param
     * @return
     */
    List<AppMorePackageVO> queryMorePackage();

    /**
     * 申请单日跟单
     * @param dto 申请参数
     * @param user 当前用户
     * @return 处理结果
     */
    ServerResponse<Void> applySingleFollow(SingleFollowApplyDTO dto, User user);
    /**
     * 申请多日跟单
     * @param dto 申请参数
     * @param user 当前用户
     * @return 处理结果
     */
    ServerResponse<Void> applyMoreFollow(MoreFollowApplyDTO dto, User user);
}