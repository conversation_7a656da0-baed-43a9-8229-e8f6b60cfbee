package com.nq.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "套餐")
public class AppDayPackageVO {
    @ApiModelProperty(value = "等级名称", example = "跟单队长")
    private String levelName;

    @ApiModelProperty(value = "跟单金额最小值", example = "1000.00")
    private BigDecimal minAmount;

    @ApiModelProperty(value = "跟单金额最大值", example = "100000.00")
    private BigDecimal maxAmount;

    @ApiModelProperty(value = "仓位比例(%)", example = "0.1")
    private BigDecimal positionRate;

    @ApiModelProperty(value = "佣金(%)", example = "0")
    private BigDecimal salaryRate;

}
