package com.nq.utils.task.follow;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nq.job.task.ITask;
import com.nq.pojo.User;
import com.nq.service.IUserService;
import com.nq.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
//import org.springframework.scheduling.annotation.Scheduled;

@Slf4j
@Component("userEveryRefreshTask")
public class UserEveryRefreshTask implements ITask {
    @Resource
    private IUserService userService;

    //    @Scheduled(cron = "0 50 23 * * ?")
    @Override
    public void run(String params) {
        List<User> list = userService.list(new LambdaQueryWrapper<User>().select(
                User::getId, User::getPhone, User::getPid,
                User::getTotalFollowIncome,User::getTodayFollowIncome,
                User::getTotalFollowNum,User::getTodayFollowNum,
                User::getTotalLeadIncome,User::getTodayLeadIncome
                ,User::getIsTodayFollow,User::getIsTodayRecharge));
        for (User user : list) {
            User updateUser = new User();
            updateUser.setId(user.getId());
            updateUser.setTotalFollowIncome(user.getTotalFollowIncome().add(user.getTodayFollowIncome()));
            updateUser.setTodayFollowIncome(BigDecimal.ZERO);
            updateUser.setTotalFollowNum(user.getTotalFollowNum() + user.getTodayFollowNum());
            updateUser.setTodayFollowNum(0);
            updateUser.setTotalLeadIncome(user.getTotalLeadIncome().add(user.getTodayLeadIncome()));
            updateUser.setTodayLeadIncome(BigDecimal.ZERO);
            updateUser.setIsTodayFollow(0);
            updateUser.setIsTodayRecharge(0);
            userService.updateById(updateUser);
        }
        if (DateUtils.isSunday()) {
            // 今天是周日
            for (User user : list) {
                User updateUser = new User();
                updateUser.setId(user.getId());
                updateUser.setIsWeekFollow(0);
                userService.updateById(updateUser);
            }
        }

    }


}