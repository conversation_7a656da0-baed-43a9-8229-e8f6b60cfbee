package com.nq.utils.redis;


import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import redis.clients.jedis.JedisShardInfo;
import redis.clients.jedis.ShardedJedis;
import redis.clients.jedis.ShardedJedisPool;

import java.util.List;

import static redis.clients.jedis.util.Hashing.MURMUR_HASH;
import static redis.clients.jedis.util.Sharded.DEFAULT_KEY_TAG_PATTERN;


public class RedisShardedPool {

    private static ShardedJedisPool pool;

    private static int maxTotal = 100;


    private static int maxIdle = 50;


    private static int minIdle = 2;
    private static int setMaxWait = 10000;

    private static boolean testOnBorrow = true;


    private static boolean testOnReturn = true;


    private static void initPool() {

        GenericObjectPoolConfig config = new GenericObjectPoolConfig();


        config.setMaxTotal(maxTotal);

        config.setMaxIdle(maxIdle);

        config.setMinIdle(minIdle);
//        config.setMaxWaitMillis(setMaxWait.intValue());

        config.setTestOnBorrow(testOnBorrow);

        config.setTestOnReturn(testOnReturn);


        config.setBlockWhenExhausted(true);


        List<JedisShardInfo> jedisShardInfos = Lists.newArrayList();

        RedisProperties redisProperties = SpringUtil.getBean(RedisProperties.class);

        JedisShardInfo info1 = new JedisShardInfo(redisProperties.getHost(), redisProperties.getPort(), 20000,   redisProperties.isSsl());

        String password = redisProperties.getPassword();
        if(StrUtil.isNotEmpty(password)){
            info1.setPassword(redisProperties.getPassword());
        }


        jedisShardInfos.add(info1);


        pool = new ShardedJedisPool(config, jedisShardInfos, MURMUR_HASH, DEFAULT_KEY_TAG_PATTERN);

    }


    static {

        initPool();

    }


    public static ShardedJedis getJedis() {
        return pool.getResource();
    }


    public static void returnResouce(ShardedJedis jedis) {
        pool.returnResource(jedis);
    }


    public static void returnBrokenResouce(ShardedJedis jedis) {
        pool.returnBrokenResource(jedis);
    }


    public static void main(String[] args) {
    }

}
