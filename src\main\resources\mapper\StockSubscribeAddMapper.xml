<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nq.dao.StockSubscribeAddMapper">
  <resultMap id="BaseResultMap" type="com.nq.pojo.StockSubscribeAdd">
    <!--@mbg.generated-->
    <!--@Table stock_subscribe_add-->
    <id column="newlist_id" jdbcType="INTEGER" property="newlistId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="order_number" jdbcType="BIGINT" property="orderNumber" />
    <result column="stock_type" jdbcType="VARCHAR" property="stockType" />
    <result column="pe" jdbcType="VARCHAR" property="pe" />
    <result column="zt" jdbcType="INTEGER" property="zt" />
    <result column="is_lock" jdbcType="INTEGER" property="isLock" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="list_date" jdbcType="TIMESTAMP" property="listDate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    newlist_id, `name`, code, price, order_number, stock_type, pe, zt, is_lock, start_time, 
    end_time, list_date, create_time, create_by, update_time, update_by
  </sql>
</mapper>