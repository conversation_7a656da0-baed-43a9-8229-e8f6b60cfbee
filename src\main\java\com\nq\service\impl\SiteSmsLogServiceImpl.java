package com.nq.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.dao.SiteSmsLogMapper;
import com.nq.excepton.CustomException;
import com.nq.pojo.SiteSmsLog;
import com.nq.service.ISiteSmsLogService;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Service;

@Service("iSiteSmsLogService")
public class SiteSmsLogServiceImpl extends ServiceImpl<SiteSmsLogMapper, SiteSmsLog> implements ISiteSmsLogService {
    @Resource
    private SiteSmsLogMapper siteSmsLogMapper;

    public PageInfo<SiteSmsLog> smsList(String phoneNum, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<SiteSmsLog> smslist = this.siteSmsLogMapper.smsList(phoneNum);
        return new PageInfo<>(smslist);
    }

    @Override
    public void addData(SiteSmsLog siteSmsLog) {
        siteSmsLogMapper.insert(siteSmsLog);
    }

    public void del(Integer id, HttpServletRequest request) {
        if (id == null) {
            throw new CustomException("id不能为空");
        }

        int updateCount = this.siteSmsLogMapper.deleteById(id);
        if (updateCount <= 0) {
            throw new CustomException("删除失败");
        }
    }
}