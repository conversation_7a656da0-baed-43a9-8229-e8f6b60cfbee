<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nq.dao.UserBankMapper" >

  <resultMap id="BaseResultMap" type="com.nq.pojo.UserBank">
    <id column="id" property="id" jdbcType="INTEGER"/>
    <result column="user_id" property="userId" jdbcType="INTEGER"/>
    <result column="bank_name" property="bankName" jdbcType="VARCHAR"/>
    <result column="bank_no" property="bankNo" jdbcType="VARCHAR"/>
    <result column="bank_address" property="bankAddress" jdbcType="VARCHAR"/>
    <result column="bank_img" property="bankImg" jdbcType="VARCHAR"/>
    <result column="bank_phone" property="bankPhone" jdbcType="VARCHAR"/>
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
  </resultMap>

  <sql id="Base_Column_List" >
    id, user_id, bank_name, bank_no, bank_address, bank_img, bank_phone, add_time
  </sql>

  <select id="findUserBankByUserId" parameterType="integer" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM user_bank
    WHERE user_id = #{userId}
    limit 1
  </select>



</mapper>




