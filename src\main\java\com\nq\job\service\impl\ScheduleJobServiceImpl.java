/**
 * Copyright (c) 2016-2019 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package com.nq.job.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.dao.ScheduleJobDao;
import com.nq.job.entity.ScheduleJobEntity;
import com.nq.job.enums.Constant;
import com.nq.job.service.ScheduleJobService;
import com.nq.job.utils.ScheduleUtils;
import org.apache.commons.lang.StringUtils;
import org.quartz.CronTrigger;
import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

@Service("scheduleJobService")
public class ScheduleJobServiceImpl extends ServiceImpl<ScheduleJobDao, ScheduleJobEntity>
		implements ScheduleJobService {
	@Resource
	private Scheduler scheduler;

	/**
	 * 项目启动时，初始化定时器
	 */
	@PostConstruct
	public void init() {
		List<ScheduleJobEntity> scheduleJobList = this.list();
		for (ScheduleJobEntity scheduleJob : scheduleJobList) {
			CronTrigger cronTrigger = ScheduleUtils.getCronTrigger(scheduler, scheduleJob.getJobId());
			// 如果不存在，则创建
			if (cronTrigger == null) {
				ScheduleUtils.createScheduleJob(scheduler, scheduleJob);
			} else {
				ScheduleUtils.updateScheduleJob(scheduler, scheduleJob);
			}
		}
	}

	@Override
	public PageInfo<ScheduleJobEntity> queryPage(Map<String, Object> params) {
		String beanName = (String) params.get("beanName");

		// 获取分页参数
		int pageNum = 1;
		int pageSize = 10;

		if (params.get("page") != null) {
			pageNum = Integer.parseInt((String) params.get("page"));
		}
		if (params.get("limit") != null) {
			pageSize = Integer.parseInt((String) params.get("limit"));
		}

		// 使用PageHelper进行分页
		PageHelper.startPage(pageNum, pageSize);

		// 构建查询条件
		QueryWrapper<ScheduleJobEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.like(StringUtils.isNotBlank(beanName), "bean_name", beanName);
		queryWrapper.orderByDesc("create_time");

		// 执行查询
		List<ScheduleJobEntity> list = this.list(queryWrapper);

		// 返回分页结果
		return new PageInfo<>(list);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveJob(ScheduleJobEntity scheduleJob) {
		scheduleJob.setCreateTime(new Date());
		scheduleJob.setStatus(Constant.ScheduleStatus.NORMAL.getValue());
		this.save(scheduleJob);

		ScheduleUtils.createScheduleJob(scheduler, scheduleJob);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void update(ScheduleJobEntity scheduleJob) {
		ScheduleUtils.updateScheduleJob(scheduler, scheduleJob);

		this.updateById(scheduleJob);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteBatch(String[] jobIds) {
		for (String jobId : jobIds) {
			ScheduleUtils.deleteScheduleJob(scheduler, jobId);
		}

		// 删除数据
		baseMapper.deleteBatch(jobIds);
	}

	@Override
	public int updateBatch(String[] jobIds, int status) {
		Map<String, Object> map = new HashMap<>(2);
		map.put("list", Arrays.asList(jobIds));
		map.put("status", status);
		return baseMapper.updateBatch(map);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void run(String[] jobIds) {
		for (String jobId : jobIds) {
			ScheduleJobEntity scheduleJob = this.getById(jobId);
			if (scheduleJob != null) {
				ScheduleUtils.run(scheduler, scheduleJob);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void pause(String[] jobIds) {
		for (String jobId : jobIds) {
			ScheduleUtils.pauseJob(scheduler, jobId);
		}

		updateBatch(jobIds, Constant.ScheduleStatus.PAUSE.getValue());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void resume(String[] jobIds) {
		for (String jobId : jobIds) {
			ScheduleUtils.resumeJob(scheduler, jobId);
		}

		updateBatch(jobIds, Constant.ScheduleStatus.NORMAL.getValue());
	}

}
