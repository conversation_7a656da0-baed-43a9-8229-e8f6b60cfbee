package com.nq.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.dto.common.CommonPage;
import com.nq.pojo.Activity;
import com.nq.pojo.User;
import com.nq.vo.app.AppActivityVO;

public interface ActivityService extends IService<Activity> {

    AppActivityVO queryActivityTeam(User user);

    AppActivityVO queryActivityInvite(User user);

    PageInfo<Activity> listByAdmin(Activity activity, CommonPage commonPage);

    Activity detail(Integer id);

    /**
     * 物理删除活动
     * 
     * @param id 活动ID
     * @return 是否成功
     */
    boolean deleteActivity(Integer id);
}
