package com.nq.dto.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@ApiModel(description = "多日跟单申请DTO")
public class MoreFollowApplyDTO {

    @NotNull(message = "跟单金额不能为空")
    @DecimalMin(value = "100", message = "跟单金额必须是100的倍数")
    @ApiModelProperty(value = "跟单金额", required = true, example = "5000.00")
    private BigDecimal amount;
    @NotNull(message = "导师id不能为空")
    @ApiModelProperty(value = "导师id", required = true, example = "1")
    private Integer mentorId;

    @NotNull(message = "套餐id")
    @ApiModelProperty(value = "套餐id", required = true, example = "1")
    private Integer packageId;
}