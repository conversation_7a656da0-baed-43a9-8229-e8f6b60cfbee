package com.nq.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nq.dao.SiteSettingMapper;
import com.nq.excepton.CustomException;
import com.nq.pojo.SiteSetting;
import com.nq.service.ISiteSettingService;
import com.nq.utils.stock.BuyAndSellUtils;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("iSiteSettingService")
public class SiteSettingServiceImpl extends ServiceImpl<SiteSettingMapper, SiteSetting> implements ISiteSettingService {
    @Resource
    private SiteSettingMapper siteSettingMapper;

    @Override
    public SiteSetting getSiteSetting() {
        LambdaQueryWrapper<SiteSetting> q = new LambdaQueryWrapper<>();
        List<SiteSetting> list = this.siteSettingMapper.selectList(q);
        if (list == null || list.isEmpty()) {
            throw new CustomException("未找到系统设置");
        }
        return list.get(0);
    }

    @SneakyThrows
    @Override
    public Boolean checkTradingHours() {
        SiteSetting siteSetting = this.getSiteSetting();
        if (siteSetting == null) {
            log.error("网站设置表不存在");
            throw new CustomException("下单失败，系统设置错误");
        }
        String am_begin = siteSetting.getTransAmBegin();
        String am_end = siteSetting.getTransAmEnd();
        String pm_begin = siteSetting.getTransPmBegin();
        String pm_end = siteSetting.getTransPmEnd();
        boolean am_flag = BuyAndSellUtils.isTransTime(am_begin, am_end);
        boolean pm_flag = BuyAndSellUtils.isTransTime(pm_begin, pm_end);
        if (!am_flag && !pm_flag) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public void update(SiteSetting setting) {
        if (setting.getId() == null) {
            throw new CustomException("ID 不能为空");
        }

        SiteSetting siteSetting = this.siteSettingMapper.selectById(setting.getId());
        if (siteSetting == null) {
            throw new CustomException("查不到设置记录");
        }

        int updateCount = this.siteSettingMapper.updateById(setting);
        if (updateCount <= 0) {
            throw new CustomException("修改失败");
        }
    }
}
