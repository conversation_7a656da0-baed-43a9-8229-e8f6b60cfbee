package com.nq.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.pojo.LoadEarn;
import com.nq.pojo.User;
import com.nq.vo.user.LoadEarnVO;

public interface LoadEarnService extends IService<LoadEarn> {
    /**
     * 分页查询 代理收益
     * @param user
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageInfo<LoadEarnVO> getAgentSalaryPage(User user, Integer pageNum, Integer pageSize);
}