package com.nq.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.dao.StockSubscribeMapper;
import com.nq.excepton.CustomException;
import com.nq.pojo.StockSubscribe;
import com.nq.service.IStockSubscribeService;
import com.nq.utils.DateTimeUtil;
import com.nq.utils.stock.sina.SinaStockApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【stock_subscribe(新股)】的数据库操作Service实现
 * @createDate 2022-10-24 23:27:27
 */
@Service
@Slf4j
public class StockSubscribeServiceImpl extends ServiceImpl<StockSubscribeMapper, StockSubscribe>
        implements IStockSubscribeService {
    @Resource
    private StockSubscribeMapper stockSubscribeMapper;

    /**
     * @Description: 用户新股列表
     * @Param:
     * @return:
     * @Author: tf
     * @Date: 2022/10/25
     */
    @Override
    public PageInfo<StockSubscribe> list(int pageNum, int pageSize, String name, String code, Integer zt, Integer isLock, Integer type, Integer status) {
        PageHelper.startPage(pageNum, pageSize);
        QueryWrapper<StockSubscribe> queryWrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(name)) {
            queryWrapper.like("name", name);
        }
        if (StrUtil.isNotBlank(code)) {
            queryWrapper.like("code", code);
        }
//        if (zt != null) {
            queryWrapper.eq("zt", 1);
//        }
        if (isLock != null) {
            queryWrapper.eq("is_lock", isLock);
        }
//        if (type != null) {
            queryWrapper.eq("type", 1);
//        }
        if (status == null || status == 1) {
            //可申购
            queryWrapper.le("subscribe_time", DateUtil.formatDateTime(new Date()))
                    .ge("subscription_time", DateUtil.formatDateTime(new Date()))
                    .orderByDesc("subscribe_time");
            List<StockSubscribe> stockSubscribeList = this.stockSubscribeMapper.selectList(queryWrapper);
            return new PageInfo<>(stockSubscribeList);
        }else {
            //待申购
            queryWrapper.ge("subscribe_time", DateUtil.formatDateTime(new Date()))
                    .orderByDesc("subscribe_time");
            List<StockSubscribe> stockSubscribeList = this.stockSubscribeMapper.selectList(queryWrapper);
            return new PageInfo<>(stockSubscribeList);
        }

    }

    @Override
    public PageInfo<StockSubscribe> listByAdmin(int pageNum, int pageSize, String name, String code, Integer zt, Integer isLock, Integer type) {
        PageHelper.startPage(pageNum, pageSize);
        QueryWrapper<StockSubscribe> queryWrapper = new QueryWrapper<>();
        if (name != null && !name.isEmpty()) {
            queryWrapper.like("name", name);
        }
        if (code != null && !code.isEmpty()) {
            queryWrapper.like("code", code);
        }
        if (zt != null) {
            queryWrapper.eq("zt", zt);
        }
        if (isLock != null) {
            queryWrapper.eq("is_lock", isLock);
        }
        if (type != null) {
            queryWrapper.eq("type", type);
        }
        queryWrapper.orderByAsc("subscribe_time");
        List<StockSubscribe> stockSubscribeList = this.stockSubscribeMapper.selectList(queryWrapper);
        return new PageInfo<>(stockSubscribeList);
    }

    /**
     * @Description: 新增新股
     * @Param:
     * @return:
     * @Author: tf
     * @Date: 2022/10/25
     */
    @Override
    public void add(StockSubscribe model) {
        List<StockSubscribe> stockSubscribeList = this.stockSubscribeMapper.selectList(
            new QueryWrapper<StockSubscribe>().eq("code", model.getCode()));
        if (!stockSubscribeList.isEmpty()) {
            throw new CustomException("新股已经存在，不要重复添加");
        }
        String sinaStock = SinaStockApi.getSinaStock(model.getStockType() + model.getCode());
        String[] arrayOfString = sinaStock.split(",");

        int resultCount = this.stockSubscribeMapper.insert(model);
        if (resultCount <= 0) {
            throw new CustomException("添加新股失败");
        }
    }

    /**
     * @Description: 修改新股
     * @Param:
     * @return:
     * @Author: tf
     * @Date: 2022/10/25
     */
    @Override
    public void update(StockSubscribe model) {
        StockSubscribe stockSubscribe = this.stockSubscribeMapper.selectById(model.getNewlistId());
        if (stockSubscribe == null) {
            throw new CustomException("新股不存在");
        }
        int resultCount = this.stockSubscribeMapper.updateById(model);
        if (resultCount <= 0) {
            throw new CustomException("修改新股失败");
        }
    }

    /**
     * @Description: 删除新股
     * @Param:
     * @return:
     * @Author: tf
     * @Date: 2022/10/25
     */
    @Override
    public void del(Integer id) {
        StockSubscribe stockSubscribe = this.stockSubscribeMapper.selectById(id);
        if (stockSubscribe == null) {
            throw new CustomException("新股不存在");
        }
        int resultCount = this.stockSubscribeMapper.deleteById(id);
        if (resultCount <= 0) {
            throw new CustomException("删除新股失败");
        }
    }

    /**
     * @Description: 新股抢筹列表
     */
    @Override
    public List<StockSubscribe> newStockQc() {
        String nowDate = DateTimeUtil.stampToDate(String.valueOf(System.currentTimeMillis()));
        return this.stockSubscribeMapper.selectList(
            new QueryWrapper<StockSubscribe>().eq("list_date", nowDate));
    }
}




