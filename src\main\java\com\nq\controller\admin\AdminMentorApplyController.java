package com.nq.controller.admin;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.service.MentorApplyService;
import com.nq.vo.app.MentorApplyVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "后台-导师申请管理接口")
@RestController
@RequestMapping("/admin/mentor/apply")
public class AdminMentorApplyController {
    
    @Resource
    private MentorApplyService mentorApplyService;
    
    @ApiOperation("分页查询导师申请列表")
    @GetMapping("/list.do")
    public ServerResponse<PageInfo<MentorApplyVO>> list(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("姓名") @RequestParam(required = false) String name,
            @ApiParam("状态") @RequestParam(required = false) Integer status) {
        return ServerResponse.createBySuccess(mentorApplyService.pageList(pageNum, pageSize, name,status));
    }
    
    @ApiOperation("审核导师申请")
    @GetMapping("/audit.do")
    public ServerResponse<Boolean> audit(
            @ApiParam("申请ID") @RequestParam Integer id,
            @ApiParam("审核状态") @RequestParam Integer status,
            @ApiParam("审核备注") @RequestParam(required = false) String auditRemark) {
        return ServerResponse.createBySuccess(mentorApplyService.audit(id, status, auditRemark));
    }

} 