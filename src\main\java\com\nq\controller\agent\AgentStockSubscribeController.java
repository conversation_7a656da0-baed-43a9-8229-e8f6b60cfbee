package com.nq.controller.agent;


import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.StockSubscribe;
import com.nq.pojo.UserStockSubscribe;
import com.nq.service.IStockSubscribeService;
import com.nq.service.IUserService;
import com.nq.service.IUserStockSubscribeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping({"/agent/subscribe/"})
@Api(tags = {"代理后台-新股申购管理"})
public class AgentStockSubscribeController {
    private static final Logger log = LoggerFactory.getLogger(AgentStockSubscribeController.class);
    @Resource
    private IUserStockSubscribeService iUserStockSubscribeService;
    @Resource
    private IStockSubscribeService iStockSubscribeService;
    @Resource
    private IUserService iUserService;
    /**
     * @param pageNum
     * @param pageSize
     * @return
     */
    @PostMapping({"list.do"})
    @ApiOperation("分页查询新股信息")
    public ServerResponse<PageInfo<StockSubscribe>> list(@RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "10") int pageSize, @RequestParam(value = "name", required = false) String name, @RequestParam(value = "code", required = false) String code, @RequestParam(value = "zt", required = false) Integer zt, @RequestParam(value = "isLock", required = false) Integer isLock, @RequestParam(value = "type", required = false) Integer type) {
        PageInfo<StockSubscribe> EasyAdmin = this.iStockSubscribeService.listByAdmin(pageNum, pageSize, name, code, zt, isLock, type);
        return ServerResponse.createBySuccess(EasyAdmin);
    }



    //申购信息列表查询
    @PostMapping({"getStockSubscribeList.do"})
    @ApiOperation("分页查询新股申购信息")
    public ServerResponse<PageInfo<UserStockSubscribe>> getStockSubscribeList(@RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "12") int pageSize, @RequestParam(value = "keyword", defaultValue = "") String keyword, @RequestParam(value = "status", required = false) Integer status) {
        //查询代理下面所有用户id
        PageInfo<UserStockSubscribe> pageInfo = this.iUserStockSubscribeService.getList(pageNum, pageSize, keyword, status, true);
        return ServerResponse.createBySuccess(pageInfo);
    }



}
