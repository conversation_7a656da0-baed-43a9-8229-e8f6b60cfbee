package com.nq.controller.admin;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.SitePopup;
import com.nq.service.SitePopupService;
import com.nq.vo.admin.SitePopupVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "后台-弹窗管理")
@RestController
@RequestMapping("/admin/popup/")
public class AdminSitePopupController {

    @Resource
    private SitePopupService sitePopupService;

    @ApiOperation("首页弹窗列表")
    @GetMapping("list.do")
    public ServerResponse<PageInfo<SitePopupVO>> list(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页数量") @RequestParam(defaultValue = "10") Integer pageSize) {
        return ServerResponse.createBySuccess(sitePopupService.selectAllVO(pageNum, pageSize));
    }

    @ApiOperation("新增首页弹窗")
    @PostMapping("create.do")
    public ServerResponse<String> create(@RequestBody SitePopup popup) {
        sitePopupService.insert(popup);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation("更新首页弹窗")
    @PostMapping("update.do")
    public ServerResponse<String> update(@RequestBody SitePopup popup) {
        sitePopupService.update(popup);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation("删除首页弹窗")
    @PostMapping("delete.do")
    public ServerResponse<String> delete(@RequestParam Integer id) {
        sitePopupService.deleteById(id);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation("获取首页弹窗详情")
    @GetMapping("detail.do")
    public ServerResponse<SitePopupVO> detail(@RequestParam Integer id) {
        return ServerResponse.createBySuccess(sitePopupService.selectVOById(id));
    }
} 