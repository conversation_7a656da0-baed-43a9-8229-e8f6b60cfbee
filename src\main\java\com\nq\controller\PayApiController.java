package com.nq.controller;

import com.nq.service.IPayService;
import com.nq.service.IUserRechargeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping({"/api/pay/"})
public class PayApiController {
    private static final Logger log = LoggerFactory.getLogger(PayApiController.class);

    @Resource
    private IPayService iPayService;
    @Resource
    private IUserRechargeService iUserRechargeService;

}
