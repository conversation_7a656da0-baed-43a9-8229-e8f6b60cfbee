package com.nq.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.nq.config.StockPoll;
import com.nq.dao.StockMapper;
import com.nq.excepton.CustomException;
import com.nq.pojo.Stock;
import com.nq.pojo.User;
import com.nq.service.IStockMarketsDayService;
import com.nq.service.IStockOptionService;
import com.nq.service.IStockService;
import com.nq.service.IUserService;
import com.nq.utils.HttpClientRequest;
import com.nq.utils.PageUtil;
import com.nq.utils.PropertiesUtil;
import com.nq.utils.CacheUtil;
import com.nq.utils.stock.pinyin.GetPyByChinese;
import com.nq.utils.stock.qq.QqStockApi;
import com.nq.utils.stock.sina.SinaStockApi;
import com.nq.vo.stock.*;
import com.nq.vo.stock.k.MinDataVO;
import com.nq.vo.stock.k.echarts.EchartsDataVO;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import static com.nq.utils.DateTimeUtil.*;

@Service("iStockService")
public class StockServiceImpl extends ServiceImpl<StockMapper, Stock> implements IStockService {
    private static final Logger log = LoggerFactory.getLogger(StockServiceImpl.class);

    @Resource
    private StockMapper stockMapper;
    @Resource
    private StockPoll stockPoll;
    @Resource
    private IStockMarketsDayService iStockMarketsDayService;
    @Resource
    private IUserService iUserService;
    @Resource
    private IStockOptionService iStockOptionService;

    // 常量提取
    private static final String CHARSET_GBK = "gbk";
    private static final String ACCESS_CONTROL_ALLOW_ORIGIN = "Access-Control-Allow-Origin";
    private static final String ALLOW_ORIGIN_ALL = "*";

    public MarketVOResult getMarket() {
        String market_url = PropertiesUtil.getProperty("sina.market.url");
        String result = null;
        try {
            result = HttpClientRequest.doGet(market_url);
        } catch (Exception e) {
            log.error("e = {}", e);
        }
        String[] marketArray = result.split(";");
        List<MarketVO> marketVOS = Lists.newArrayList();
        for (int i = 0; i < marketArray.length; i++) {
            String hqstr = marketArray[i];
            try {
                if (StringUtils.isNotBlank(hqstr)) {
                    hqstr = hqstr.substring(hqstr.indexOf("\"") + 1, hqstr.lastIndexOf("\""));
                    MarketVO marketVO = new MarketVO();
                    String[] sh01_arr = hqstr.split(",");
                    marketVO.setName(sh01_arr[0]);
                    marketVO.setNowPrice(new BigDecimal(sh01_arr[1]));
                    marketVO.setIncrease(sh01_arr[2]);
                    marketVO.setIncreaseRate(sh01_arr[3]);
                    marketVOS.add(marketVO);
                }
            } catch (Exception e) {
                log.error("str = {} ,  e = {}", hqstr, e);
            }
        }
        MarketVOResult marketVOResult = new MarketVOResult();
        marketVOResult.setMarket(marketVOS);
        return marketVOResult;
    }

    public PageInfo<StockListVO> getStock(int pageNum, int pageSize, String keyWords, String stockPlate, String stockType) {
        PageHelper.startPage(pageNum, pageSize);
        User user = iUserService.getCurrentUser();
        List<Stock> stockList = this.stockMapper.findStockListByKeyWords(keyWords, stockPlate, stockType, Integer.valueOf(0));

        List<StockListVO> stockListVOS = Lists.newArrayList();
        if (!stockList.isEmpty())
            for (Stock stock : stockList) {
                StockListVO stockListVO = new StockListVO();
                stockListVO = SinaStockApi.assembleStockListVO(SinaStockApi.getSinaStock(stock.getStockGid()));
                stockListVO.setCode(stock.getStockCode());
                stockListVO.setSpell(stock.getStockSpell());
                stockListVO.setGid(stock.getStockGid());
                BigDecimal day3Rate = (BigDecimal) selectRateByDaysAndStockCode(stock.getStockCode(), 3);
                stockListVO.setDay3Rate(day3Rate);
                stockListVO.setStock_plate(stock.getStockPlate());
                stockListVO.setStock_type(stock.getStockType());
                //是否添加自选
                if (user == null) {
                    stockListVO.setIsOption("0");
                } else {
                    stockListVO.setIsOption(iStockOptionService.isMyOption(user.getId(), stock.getStockCode()));
                }
                stockListVOS.add(stockListVO);
            }
        PageInfo<StockListVO> pageInfo = new PageInfo<StockListVO>(stockListVOS);
        pageInfo.setList(stockListVOS);
        return pageInfo;
    }

    public EchartsDataVO getMinK_Echarts(String code, Integer time, Integer ma, Integer size) {
        if (StringUtils.isBlank(code) || time == null || ma == null || size == null) {
            return new EchartsDataVO();
        }
        MinDataVO vo = SinaStockApi.getStockMinK(time, ma, size);

        EchartsDataVO echartsDataVO = SinaStockApi.assembleEchartsDataVO(vo);
        return echartsDataVO;
    }

    public EchartsDataVO getDateline(HttpServletResponse response, String code) {
        if (StringUtils.isBlank(code)) {
            log.warn("getDateline: code is blank");
            throw new CustomException("股票代码不能为空");
        }
        Stock stock = this.stockMapper.findStockByCode(code);
        if (stock == null) {
            log.warn("getDateline: stock not found for code={}", code);
            throw new CustomException("股票不存在");
        }
        response.setHeader(ACCESS_CONTROL_ALLOW_ORIGIN, ALLOW_ORIGIN_ALL);
        String end = new SimpleDateFormat("yyyyMMdd").format(new Date());
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.MONTH, -3);
        String mon = new SimpleDateFormat("yyyyMMdd").format(c.getTime());
        String methodUrl = String.format("http://q.stock.sohu.com/hisHq?code=cn_%s+&start=%s&end=%s&stat=1&order=D", code, mon, end);
        EchartsDataVO echartsDataVO = new EchartsDataVO();
        try (BufferedReader reader = createBufferedReader(methodUrl)) {
            if (reader != null) {
                StringBuilder result = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    result.append(line).append(System.getProperty("line.separator"));
                }
                parseEchartsData(result.toString(), stock, echartsDataVO);
            }
        } catch (IOException e) {
            log.error("getDateline: IO error", e);
            throw new CustomException("获取数据失败");
        }
        return echartsDataVO;
    }

    /**
     * 创建BufferedReader，自动处理连接
     */
    private BufferedReader createBufferedReader(String urlStr) {
        try {
            URL url = new URL(urlStr);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.connect();
            if (connection.getResponseCode() == 200) {
                return new BufferedReader(new InputStreamReader(connection.getInputStream(), CHARSET_GBK));
            }
        } catch (IOException e) {
            log.error("createBufferedReader: IO error", e);
        }
        return null;
    }

    /**
     * 解析EchartsDataVO
     */
    private void parseEchartsData(String jsonStr, Stock stock, EchartsDataVO echartsDataVO) {
        try {
            JSONArray jsonArray = JSON.parseArray(jsonStr);
            JSONObject json = jsonArray.getJSONObject(0);
            JSONArray jsonArray1 = JSONArray.parseArray(json.getString("hq"));
            Collections.reverse((List<?>) jsonArray1);
            double[][] values = new double[jsonArray1.size()][5];
            Object[][] volumes = new Object[jsonArray1.size()][3];
            String[] date = new String[jsonArray1.size()];
            for (int i = 0; i < jsonArray1.size(); i++) {
                JSONArray js = JSONArray.parseArray(jsonArray1.get(i).toString());
                date[i] = js.get(0).toString();
                values[i][0] = Double.parseDouble(js.get(1).toString());
                values[i][1] = Double.parseDouble(js.get(2).toString());
                values[i][2] = Double.parseDouble(js.get(5).toString());
                values[i][3] = Double.parseDouble(js.get(6).toString());
                values[i][4] = Double.parseDouble(js.get(7).toString());
                volumes[i][0] = i;
                volumes[i][1] = Double.parseDouble(js.get(7).toString());
                volumes[i][2] = Double.parseDouble(js.get(3).toString()) > 0.0D ? 1 : -1;
            }
            echartsDataVO.setDate(date);
            echartsDataVO.setValues(values);
            echartsDataVO.setVolumes(volumes);
            echartsDataVO.setStockCode(stock.getStockCode());
            echartsDataVO.setStockName(stock.getStockName());
        } catch (Exception e) {
            log.error("parseEchartsData: parse error", e);
        }
    }

    public void z1() {
        this.stockPoll.z1();
    }

    public void z11() {
        this.stockPoll.z11();
    }

    public void z12() {
        this.stockPoll.z12();
    }

    public void z2() {
        this.stockPoll.z2();
    }

    public void z21() {
        this.stockPoll.z21();
    }

    public void z22() {
        this.stockPoll.z22();
    }

    public void z3() {
        this.stockPoll.z3();
    }

    public void z31() {
        this.stockPoll.z31();
    }

    public void z32() {
        this.stockPoll.z32();
    }

    public void z4() {
        this.stockPoll.z4();
    }

    public void z41() {
        this.stockPoll.z41();
    }

    public void z42() {
        this.stockPoll.z42();
    }

    public void h1() {
        this.stockPoll.h1();
    }

    public void h11() {
        this.stockPoll.h11();
    }

    public void h12() {
        this.stockPoll.h12();
    }

    public void h2() {
        this.stockPoll.h2();
    }

    public void h21() {
        this.stockPoll.h21();
    }

    public void h22() {
        this.stockPoll.h22();
    }

    public void h3() {
        this.stockPoll.h3();
    }

    public void h31() {
        this.stockPoll.h31();
    }

    public void h32() {
        this.stockPoll.h32();
    }


    public void bj1() {
        this.stockPoll.bj1();
    }

    public Map getSingleStock(String code) {
        if (StringUtils.isBlank(code)) {
            throw new RuntimeException("股票不存在");
        }
        Stock stock = new Stock();
        Integer depositAmt = 0;
        String introduction = null;
        //期货
        String url = null;
        stock = this.stockMapper.findStockByCode(code);
        if (stock == null) {
            throw new RuntimeException("股票不存在");
        }
        String gid = stock.getStockGid();
        //股票数据
        StockVO stockVO = new StockVO();
        String sinaResult = SinaStockApi.getSinaStock(gid);
        stockVO = SinaStockApi.assembleStockVO(sinaResult);
        stockVO.setDepositAmt(depositAmt);
        stockVO.setId(stock.getId());
        stockVO.setCode(stock.getStockCode());
        stockVO.setSpell(stock.getStockSpell());
        stockVO.setGid(stock.getStockGid());
        stockVO.setMinImg(PropertiesUtil.getProperty("sina.single.stock.min.url") + stock.getStockGid() + ".jpg");
        stockVO.setDayImg(PropertiesUtil.getProperty("sina.single.stock.day.url") + stock.getStockGid() + ".jpg");
        stockVO.setWeekImg(PropertiesUtil.getProperty("sina.single.stock.week.url") + stock.getStockGid() + ".jpg");
        stockVO.setMonthImg(PropertiesUtil.getProperty("sina.single.stock.month.url") + stock.getStockGid() + ".jpg");
        Map map = Maps.newHashMap();
        map.put("introduction", introduction);
        map.put("stock", stockVO);
        return map;
    }


    /*股票日线-K线*/
    public EchartsDataVO getDayK_Echarts(String code) {
        if (StringUtils.isBlank(code)) {
            throw new CustomException("股票不存在");
        }
        Stock stock = this.stockMapper.findStockByCode(code);
        if (stock == null) {
            throw new CustomException("股票不存在");
        }
        MinDataVO minDataVO = QqStockApi.getGpStockMonthK(stock, "day");
        EchartsDataVO echartsDataVO = SinaStockApi.assembleEchartsDataVO(minDataVO);
        return echartsDataVO;
    }


    public Stock findStockByName(String name) {
        return this.stockMapper.findStockByName(name);
    }

    public Stock findStockByCode(String code) {
        return this.stockMapper.findStockByCode(code);
    }

    public Stock findStockById(Integer stockId) {
        return this.stockMapper.selectById(stockId);
    }

    public PageInfo<StockAdminListVO> listByAdmin(Integer showState, Integer lockState, String code, String name, String stockPlate, String stockType, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Stock> stockList = this.stockMapper.listByAdmin(showState, lockState, code, name, stockPlate, stockType);
        List<StockAdminListVO> stockAdminListVOS = Lists.newArrayList();
        for (Stock stock : stockList) {
            StockAdminListVO stockAdminListVO = assembleStockAdminListVO(stock);
            stockAdminListVOS.add(stockAdminListVO);
        }
        PageInfo<StockAdminListVO> pageInfo = PageUtil.buildPageDto(stockList, stockAdminListVOS);
        return pageInfo;
    }



    private StockAdminListVO assembleStockAdminListVO(Stock stock) {
        StockAdminListVO stockAdminListVO = new StockAdminListVO();
        stockAdminListVO.setId(stock.getId());
        stockAdminListVO.setStockName(stock.getStockName());
        stockAdminListVO.setStockCode(stock.getStockCode());
        stockAdminListVO.setStockSpell(stock.getStockSpell());
        stockAdminListVO.setStockType(stock.getStockType());
        stockAdminListVO.setStockGid(stock.getStockGid());
        stockAdminListVO.setStockPlate(stock.getStockPlate());
        stockAdminListVO.setIsLock(stock.getIsLock());
        stockAdminListVO.setIsShow(stock.getIsShow());
        stockAdminListVO.setAddTime(stock.getAddTime());
        stockAdminListVO.setDataBase(stock.getDataBase());
        StockListVO stockListVO;
        stockListVO = SinaStockApi.assembleStockListVO(SinaStockApi.getSinaStock(stock.getStockGid()));
        stockAdminListVO.setNowPrice(stockListVO.getNowPrice());
        stockAdminListVO.setHcrate(stockListVO.getHcrate());
        stockAdminListVO.setSpreadRate(stock.getSpreadRate());
        BigDecimal day3Rate = selectRateByDaysAndStockCode(stock.getStockCode(), 3);
        stockAdminListVO.setDay3Rate(day3Rate);
        return stockAdminListVO;
    }

    public void updateLock(Integer stockId) {
        Stock stock = this.stockMapper.selectById(stockId);
        if (stock == null)
            return;
        if (stock.getIsLock() == 1) {
            stock.setIsLock(0);
        } else {
            stock.setIsLock(1);
        }
        int updateCount = this.stockMapper.updateById(stock);
        // 可根据需要抛异常或记录日志
        return;
    }

    public void updateShow(Integer stockId) {
        Stock stock = this.stockMapper.selectById(stockId);
        if (stock == null)
            return;
        if (stock.getIsShow() == 0) {
            stock.setIsShow(1);
        } else {
            stock.setIsShow(0);
        }
        int updateCount = this.stockMapper.updateById(stock);
        // 可根据需要抛异常或记录日志
        return;
    }

    public void addStock(String stockName, String stockCode, String stockType, String stockPlate, Integer isLock, Integer isShow) {
        if (StringUtils.isBlank(stockName) || StringUtils.isBlank(stockCode) || StringUtils.isBlank(stockType) || isLock == null || isShow == null)
            return;
        Stock cstock = findStockByCode(stockCode);
        if (cstock != null)
            return;
        Stock nstock = findStockByName(stockName);
        if (nstock != null)
            return;
        String sinaStock = SinaStockApi.getSinaStock(stockType + stockCode);
        String[] arrayOfString = sinaStock.split(",");
        Stock stock = new Stock();
        stock.setStockName(stockName);
        stock.setStockCode(stockCode);
        stock.setStockSpell(GetPyByChinese.converterToFirstSpell(stockName));
        stock.setStockType(stockType);
        stock.setStockGid(stockType + stockCode);
        stock.setIsLock(isLock);
        stock.setIsShow(isShow);
        stock.setAddTime(new Date());
        if (stockPlate != null)
            stock.setStockPlate(stockPlate);
        if (StringUtils.isNotEmpty(stockPlate) && stockCode.startsWith("300"))
            stock.setStockPlate("创业");
        else if (StringUtils.isNotEmpty(stockPlate) && stockCode.startsWith("688"))
            stock.setStockPlate("科创");
        else
            stock.setStockPlate(null);
        int insertCount = this.stockMapper.insert(stock);
        // 可根据需要抛异常或记录日志
        return;
    }

    public int CountStockNum() {
        return this.stockMapper.CountStockNum();
    }

    public int CountShowNum(Integer showState) {
        return this.stockMapper.CountShowNum(showState);
    }

    public int CountUnLockNum(Integer lockState) {
        return this.stockMapper.CountUnLockNum(lockState);
    }

    public List<Stock> findStockList() {
        return this.stockMapper.findStockList();
    }

    public BigDecimal selectRateByDaysAndStockCode(String stockCode, int days) {
        Stock stock = this.stockMapper.findStockByCode(stockCode);
        if (stock == null) {
            throw new RuntimeException("没有该股票");
        }
        return this.iStockMarketsDayService.selectRateByDaysAndStockCode(stock.getId(), days);
    }

    public void updateStock(Stock model) {
        if (StringUtils.isBlank(model.getId().toString()) || StringUtils.isBlank(model.getStockName()))
            return;
        Stock stock = this.stockMapper.selectById(model.getId());
        if (stock == null)
            return;
        stock.setStockName(model.getStockName());
        if (model.getSpreadRate() != null)
            stock.setSpreadRate(model.getSpreadRate());
        int updateCount = this.stockMapper.updateById(stock);
        // 可根据需要抛异常或记录日志
        return;
    }

    public void deleteByPrimaryKey(Integer id) {
        int updateCount = this.stockMapper.deleteById(id);
    }

    public List<Stock> stockDataBase() {
        //mybatisplus查询所有database 不是0的
        List<Stock> stockList = this.stockMapper.selectList(new QueryWrapper<Stock>().ne("data_base", 0));
        return stockList;
    }


    /**
     * 龙虎榜
     */
    @Override
    public JSONArray lhb() {
        String day = getWeekDay();
        String url = PropertiesUtil.getProperty("longhu.url");
        url = url.replace("2022-12-02", day);
        JSONArray data = null;
        try {
            String res = HttpClientRequest.doGet(url);
            JSONObject json = JSONObject.parseObject(res);
            if (json.getJSONObject("result") != null) {
                data = json.getJSONObject("result").getJSONArray("data");
            } else {
                String date = new SimpleDateFormat("yyyy-MM-dd").format(addDay(new Date(), -1));
                url = url.replace(day, date);
                String res1 = HttpClientRequest.doGet(url);
                JSONObject json1 = JSONObject.parseObject(res1);
                data = json1.getJSONObject("result").getJSONArray("data");
            }
        } catch (Exception e) {
            log.error("龙虎榜出错", e);
        }
        return data;
    }

    /**
     * 十大成交股
     * content
     */
    @Override
    public JSONArray top(Integer content) {

        String url = PropertiesUtil.getProperty("top10.url") + content + "%22)";
        JSONArray data = null;
        try {
            String res = HttpClientRequest.doGet(url);
            JSONObject json = JSONObject.parseObject(res);
            data = json.getJSONObject("result").getJSONArray("data");
        } catch (Exception e) {
            log.error("十大成交股出错", e);
        }
        return data;
    }

    /**
     * 每日停牌
     */
    @Override
    public JSONArray stop() {
        String day = getWeekDay();
        String url = PropertiesUtil.getProperty("stop.url");
        url = url.replace("2022-12-02", day);
        JSONArray data = null;
        try {
            String res = HttpClientRequest.doGet(url);
            JSONObject json = JSONObject.parseObject(res);
            data = json.getJSONObject("result").getJSONArray("data");
        } catch (Exception e) {
            log.error("每日停牌出错", e);
        }
        return data;
    }


    /**
     * 获取同花顺涨跌幅榜
     *
     * @return
     */
    @Override
    public List<ChartCellVO> getZdfb() {
        Object zdfb = CacheUtil.get("zdfb");
        if (!ObjectUtils.isEmpty(zdfb)) {
            ArrayList<ChartCellVO> zdfbList = (ArrayList<ChartCellVO>) zdfb;
            log.info("涨跌幅榜命中缓存");
            return zdfbList;
        }
        String market_url = PropertiesUtil.getProperty("dfcf.market.zdfb");
        String result = null;
        try {
            result = HttpClientRequest.doGet(market_url);
            JSONObject jsonObject = JSON.parseObject(result);
            JSONObject data = jsonObject.getJSONObject("data");
            JSONArray diff = data.getJSONArray("diff");
            JSONObject sh = diff.getJSONObject(0);
            JSONObject sz = diff.getJSONObject(1);
            ArrayList<ChartCellVO> objects = new ArrayList<>();
            String f104 = sh.getString("f104");
            String f105 = sh.getString("f105");
            String f106 = sh.getString("f106");
            ChartCellVO v1 = new ChartCellVO(f104, "沪涨");
            ChartCellVO v2 = new ChartCellVO(f106, "沪平");
            ChartCellVO v3 = new ChartCellVO(f105, "沪跌");
            String sf104 = sz.getString("f104");
            String sf105 = sz.getString("f105");
            String sf106 = sz.getString("f106");
            ChartCellVO v4 = new ChartCellVO(sf104, "深涨");
            ChartCellVO v5 = new ChartCellVO(sf106, "深平");
            ChartCellVO v6 = new ChartCellVO(sf105, "深跌");

            objects.add(v1);
            objects.add(v2);
            objects.add(v3);
            objects.add(v4);
            objects.add(v5);
            objects.add(v6);
            CacheUtil.set("zdfb", objects, 60000);
            return objects;
        } catch (Exception e) {
            log.error("涨跌比异常", e);
        }

        return null;
    }

    /**
     * @param pageNo
     * @param pageSize
     * @param sort     根据什么排序   changepercent 涨跌幅  pricechange 涨跌额  volume 成交量 amount 成交额
     * @param asc      是否升序 0否 1是
     * @param node     排序的主板类型 科创板  创业板 a股  北交所等   hs_bjs 北交所  cyb 创业板  kcb 科创板   hs_a 沪深a股
     * @return
     */
    @Override
    public JSONArray getStockSort(Integer pageNo, Integer pageSize, String sort, Integer asc, String node) {
        String market_url = PropertiesUtil.getProperty("sina.market.zdf.url");
        String result = null;
        String param = "page=" + pageNo + "&num=" + pageSize + "&sort=" + sort + "&asc=" + asc + "&node=" + node + "&symbol=&_s_r_a=sort";
        String url = market_url + param;
        result = HttpClientRequest.doGet(url);
        JSONArray jsonArray = JSONArray.parseArray(result);
        return jsonArray;
    }

    /**
     * 涨停板
     *
     * @return
     */
    @Override
    public JSONArray ztb() {
        String day = getWeekDay();
        String url = PropertiesUtil.getProperty("ztb.url");
        //当前时间戳
        //day去掉-
        String day1 = day.replace("-", "");
        long time = System.currentTimeMillis();
        url = url.replace("20221202", day1) + time;
        String res = HttpClientRequest.doGet(url);
        JSONObject json = JSONObject.parseObject(res);
        JSONArray pool = null;
        if (json.getJSONObject("data") != null) {
            pool = json.getJSONObject("data").getJSONArray("pool");
        }
        return pool;
    }

    @Override
    public JSONArray getVipByCode(String code) {
        String day = getWeekDay();
        String url = PropertiesUtil.getProperty("ztb.url");
        String day1 = day.replace("-", "");
        long time = System.currentTimeMillis();
        url = url.replace("20221202", day1) + time;
        String res = HttpClientRequest.doGet(url);
        JSONObject json = JSONObject.parseObject(res);
        JSONArray pool = null;
        if (json.getJSONObject("data") != null) {
            pool = json.getJSONObject("data").getJSONArray("pool");
            if (pool != null) {
                for (int i = 0; i < pool.size(); i++) {
                    JSONObject jsonObject = pool.getJSONObject(i);
                    String c = jsonObject.getString("c");
                    if (c.equals(code)) {
                        pool.removeAll(pool);
                        pool.add(jsonObject);
                        return pool;
                    }
                }
            }
        }
        return pool;
    }
}
