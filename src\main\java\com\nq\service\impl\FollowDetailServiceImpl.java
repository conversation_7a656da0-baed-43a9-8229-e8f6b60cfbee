package com.nq.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dao.FollowDetailMapper;
import com.nq.pojo.*;
import com.nq.service.FollowDetailService;
import com.nq.service.IUserService;
import com.nq.service.MentorService;
import com.nq.service.PackageService;
import com.nq.utils.DateUtils;
import com.nq.utils.PageUtil;
import com.nq.vo.admin.FollowAdminDetailVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class FollowDetailServiceImpl extends ServiceImpl<FollowDetailMapper, FollowDetail> implements FollowDetailService {
    @Resource
    private IUserService userService;

    @Resource
    private MentorService mentorService;

    @Override
    public PageInfo<FollowAdminDetailVO> pageList(Integer page, Integer size, String keyword, Integer status,
                                                  Integer settleStatus) {
        List<Integer> userIdsToFilter = Collections.emptyList();
        if (StrUtil.isNotBlank(keyword)) {
            LambdaQueryWrapper<User> userWrapper = new LambdaQueryWrapper<>();
            userWrapper.like(User::getPhone, keyword)
                    .or().like(User::getRealName, keyword)
                    .or().like(User::getNickName, keyword);
            List<User> matchingUsers = userService.list(userWrapper);
            userIdsToFilter = matchingUsers.stream().map(User::getId).collect(Collectors.toList());
        }
        PageHelper.startPage(page, size);
        
        LambdaQueryWrapper<FollowDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(status != null, FollowDetail::getStatus, status)
                .eq(settleStatus != null, FollowDetail::getSettlementStatus, settleStatus)
                .in(!userIdsToFilter.isEmpty(), FollowDetail::getUserId, userIdsToFilter)
                .orderByDesc(FollowDetail::getCreateTime);
        
        List<FollowDetail> list = this.list(wrapper);
        List<FollowAdminDetailVO> voList = list.stream().map(this::convertToVO).collect(Collectors.toList());
        return PageUtil.buildPageDto(list, voList);
    }

    @Override
    public FollowAdminDetailVO getDetail(Integer id) {
        FollowDetail detail = this.getById(id);
        if (detail == null) {
            return null;
        }
        return convertToVO(detail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServerResponse<String> create(FollowDetail followDetail) {
        followDetail.setStatus(0);
        followDetail.setCreateTime(new Date());
        followDetail.setUpdateTime(new Date());
        
        if (this.save(followDetail)) {
            return ServerResponse.createBySuccessMsg("创建成功");
        }
        return ServerResponse.createByErrorMsg("创建失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServerResponse<String> update(FollowDetail followDetail) {
        Integer id = followDetail.getId();
        FollowDetail detail = this.getById(id);

        followDetail.setUpdateTime(new Date());
        BigDecimal buyPrice = followDetail.getBuyPrice();
        BigDecimal sellPrice = followDetail.getSellPrice();
        //仓位
        followDetail.setBuyAmount(buyPrice.multiply(new BigDecimal(detail.getBuyQuantity())));
        followDetail.setSellAmount(sellPrice.multiply(new BigDecimal(detail.getSellQuantity())));
        followDetail.setSalary((followDetail.getSellAmount().subtract(followDetail.getBuyAmount())).multiply(detail.getSalaryRate()).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP));
        followDetail.setProfit(followDetail.getSellAmount().subtract(followDetail.getBuyAmount()));
        if (this.updateById(followDetail)) {
            return ServerResponse.createBySuccessMsg("更新成功");
        }
        return ServerResponse.createByErrorMsg("更新失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServerResponse<String> settle(Integer id) {
        FollowDetail detail = this.getById(id);
        if (detail == null) {
            return ServerResponse.createByErrorMsg("跟单详情不存在");
        }
        
        if (detail.getStatus() == 1) {
            return ServerResponse.createByErrorMsg("该跟单已结算");
        }
        
        detail.setStatus(1);
        detail.setSettlementTime(new Date());
        detail.setUpdateTime(new Date());
        
        if (this.updateById(detail)) {
            return ServerResponse.createBySuccessMsg("结算成功");
        }
        return ServerResponse.createByErrorMsg("结算失败");
    }


    /**
     * 将Follow实体转换为VO
     */
    private FollowAdminDetailVO convertToVO(FollowDetail followDetail) {
        FollowAdminDetailVO vo = new FollowAdminDetailVO();
        BeanUtils.copyProperties(followDetail, vo);

        // 设置会员信息
        User user = userService.getById(followDetail.getUserId());
        if (user != null) {
            FollowAdminDetailVO.UserInfo userInfo = new FollowAdminDetailVO.UserInfo();
            userInfo.setUserAccount(user.getNickName()); // 使用昵称作为账号
            userInfo.setUserName(user.getRealName());
            userInfo.setUserPhone(user.getPhone());
            userInfo.setAmount(followDetail.getAmount());
            vo.setUserInfo(userInfo);
        }

        //还有套餐

        // 设置导师信息
        Mentor mentor = mentorService.getById(followDetail.getMentorId());
        if (mentor != null) {
            Integer userId = mentor.getUserId();
            User userMentor = userService.getById(userId);
            FollowAdminDetailVO.MentorInfo mentorInfo = new FollowAdminDetailVO.MentorInfo();
            //查询这个导师的 userid
            if(userMentor!=null){
                mentorInfo.setMentorAccount(userMentor.getNickName());
                mentorInfo.setMentorPhone(userMentor.getPhone());
            }
            mentorInfo.setMinAmount(followDetail.getMinAmount());
            mentorInfo.setMaxAmount(followDetail.getMaxAmount());
            mentorInfo.setSalaryRate(followDetail.getSalaryRate());
            mentorInfo.setMentorName(mentor.getMentorName());
            vo.setMentorInfo(mentorInfo);
        }
        FollowAdminDetailVO.stockInfo stockInfo = new FollowAdminDetailVO.stockInfo();
        stockInfo.setStockCode(followDetail.getStockCode());
        stockInfo.setStockName(followDetail.getStockName());
        stockInfo.setBuyQuantity(followDetail.getBuyQuantity());
        stockInfo.setSellQuantity(followDetail.getSellQuantity());
        stockInfo.setCurrentQuantity(followDetail.getCurrentQuantity());
        stockInfo.setBuyPrice(followDetail.getBuyPrice());
        stockInfo.setSellPrice(followDetail.getSellPrice());
        stockInfo.setBuyAmount(followDetail.getBuyAmount());
        stockInfo.setSellAmount(followDetail.getSellAmount());
        stockInfo.setBuyTime(followDetail.getBuyTime());
        stockInfo.setSellTime(followDetail.getSellTime());
        if(followDetail.getStatus()==2){
            stockInfo.setAdvisorProfit(followDetail.getProfit().subtract(followDetail.getSalary()));
            stockInfo.setAdvisorProfitRate(stockInfo.getAdvisorProfit().divide(stockInfo.getBuyAmount(), 4, BigDecimal.ROUND_HALF_UP));
        }

        vo.setStockInfo(stockInfo);


        return vo;
    }
} 