package com.nq.utils.task.follow;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nq.job.task.ITask;
import com.nq.pojo.Level;
import com.nq.pojo.User;
import com.nq.service.IUserService;
import com.nq.service.LevelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
//先刷新等级
//再执行用队长分佣
//再去空用户身上的信息
@Slf4j
@Component("userLevelTask")
public class UserLevelTask implements ITask {
    //手写 嫌弃刷新下用户信息
    @Resource
    private IUserService userService;
    @Resource
    private LevelService levelService;
    //刷新用户等级
//    @Scheduled(cron = "0 20 23 * * ?")
    @Override
    public void run(String params) {
        List<User> list = userService.list(new LambdaQueryWrapper<User>().select(User::getId, User::getPhone, User::getPid));
        for (User user : list) {
            List<User> userEntityList = userService.queryAllLowerLevelTask(user.getId());
            List<User> efficientUserList = userService.queryAllLowerEfficientTask(user.getId());
            if (CollectionUtil.isNotEmpty(userEntityList)) {
                user.setLevel(1);
                user.setLowerNum(Integer.parseInt(userEntityList.size() + ""));
            } else {
                user.setLevel(1);
                user.setLowerNum(0);
            }
            if (CollectionUtil.isNotEmpty(efficientUserList)) {
                user.setLevel(1);
                user.setLowerEfficientNum(Integer.parseInt(efficientUserList.size() + ""));
            } else {
                user.setLevel(1);
                user.setLowerEfficientNum(0);
            }
            userService.updateById(user);
        }
        LambdaQueryWrapper<Level> q = new LambdaQueryWrapper<>();
        q.orderByAsc(Level::getSort);
        List<Level> levelList = levelService.list(q);
        for (Level level : levelList) {
            for (User user : list) {
                resetVL(user,level);
            }
        }




    }
    private void resetVL(User user, Level level) {
        Integer lowerEfficientNum = user.getLowerEfficientNum();
        Integer minPeople = level.getMinPeople();
        Integer maxPeople = level.getMaxPeople();
        if(lowerEfficientNum>= minPeople){
            user.setLevel(level.getId());
        }
        userService.updateById(user);
    }


}