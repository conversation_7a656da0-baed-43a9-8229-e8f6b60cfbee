package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("package")
@ApiModel(value = "套餐实体类", description = "套餐信息")
public class Package extends BaseEntity {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "套餐ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "套餐名称", example = "基础套餐")
    private String productName;

    @ApiModelProperty(value = "套餐编码", example = "BASIC_001")
    private String productCode;

    @ApiModelProperty(value = "持仓天数", example = "30")
    private Integer holdDays;

    @ApiModelProperty(value = "最小金额", example = "1000.00")
    private BigDecimal minAmount;

    @ApiModelProperty(value = "最大金额", example = "100000.00")
    private BigDecimal maxAmount;

    @ApiModelProperty(value = "佣金比例", example = "0.10")
    private BigDecimal salaryRate;

    @ApiModelProperty(value = "排序", example = "0")
    private Integer sort;

    @ApiModelProperty(value = "产品图标", example = "https://example.com/images/package1.jpg")
    private String productIcon;

    @ApiModelProperty(value = "备注", example = "基础套餐，适合新手用户")
    private String remark;

    @ApiModelProperty(value = "创建时间", example = "2024-01-01 12:00:00")
    private Date createTime;

    @ApiModelProperty(value = "更新时间", example = "2024-01-01 12:00:00")
    private Date updateTime;
}