package com.nq.vo.app;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(description = "投顾标的详情VO")
public class FollowDetailVO {

    @ApiModelProperty(value = "单号", example = "GP202412290016427613")
    private String followNo;

    @ApiModelProperty(value = "初始资金", example = "1000.00")
    private BigDecimal amount = BigDecimal.ZERO;

    @ApiModelProperty(value = "追加金额", example = "0.00")
    private BigDecimal addAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "总收益比例", example = "506.34")
    private BigDecimal totalProfitRate = BigDecimal.ZERO;

    @ApiModelProperty(value = "总盈亏", example = "1663.25")
    private BigDecimal totalProfit = BigDecimal.ZERO;


    @ApiModelProperty(value = "投资标的列表")
    PageInfo<FollowPositionVO> followPositionVO;

} 