package com.nq.utils.task.news;

import com.nq.job.task.ITask;
import com.nq.service.ISiteNewsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
//import org.springframework.scheduling.annotation.Scheduled;


@Component("newsInfoTask")
public class NewsInfoTask implements ITask {
    private static final Logger log = LoggerFactory.getLogger(NewsInfoTask.class);

    @Resource
    private ISiteNewsService iSiteNewsService;

    /*
     * 新聞資訊抓取
     * */
//    @Scheduled(cron = "0 0/30 9-20 * * ?")
    @Override
    public void run(String params) {
        this.iSiteNewsService.grabNews();
        log.info("新聞資訊抓取完成");
    }

}
