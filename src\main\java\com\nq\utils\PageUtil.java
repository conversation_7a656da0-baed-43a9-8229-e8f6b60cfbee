package com.nq.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.dto.common.CommonPage;
import com.nq.dto.common.PageDomain;

import java.io.Serializable;
import java.util.List;

/**
 * 分页工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/1/001 12:54
 */
public class PageUtil implements Serializable {

    private static final long serialVersionUID = 1L;

    private static final String emptyStr = " ";


    public static <T> void startPage(Integer pageNum, Integer pageSize) {
        if (pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize);
        }
    }
    public static <T> void startPage(CommonPage commonPage) {
        if (commonPage.getPageNum() != null && commonPage.getPageSize() != null) {
            PageHelper.startPage(commonPage.getPageNum(), commonPage.getPageSize());
        }
    }

    public static <T> PageInfo<T> buildPageDto(List one, List<T> two) {
        PageInfo pageInfo = new PageInfo<>(one);
        PageInfo<T> tPageDTO = new PageInfo<>();
        tPageDTO.setPageNum(pageInfo.getPageNum());
        tPageDTO.setPageSize(pageInfo.getPageSize());
        tPageDTO.setSize(pageInfo.getSize());
        tPageDTO.setStartRow(pageInfo.getStartRow());
        tPageDTO.setEndRow(pageInfo.getEndRow());
        tPageDTO.setPages(pageInfo.getPages());
        tPageDTO.setPrePage(pageInfo.getPrePage());
        tPageDTO.setNextPage(pageInfo.getNextPage());
        tPageDTO.setIsFirstPage(pageInfo.isIsFirstPage());
        tPageDTO.setIsLastPage(pageInfo.isIsLastPage());
        tPageDTO.setHasPreviousPage(pageInfo.isHasPreviousPage());
        tPageDTO.setHasNextPage(pageInfo.isHasNextPage());
        tPageDTO.setNavigatePages(pageInfo.getNavigatePages());
        tPageDTO.setNavigatepageNums(pageInfo.getNavigatepageNums());
        tPageDTO.setNavigateFirstPage(pageInfo.getNavigateFirstPage());
        tPageDTO.setNavigateLastPage(pageInfo.getNavigateLastPage());
        tPageDTO.setTotal(pageInfo.getTotal());
        tPageDTO.setList(two);
        return tPageDTO;
    }
}
