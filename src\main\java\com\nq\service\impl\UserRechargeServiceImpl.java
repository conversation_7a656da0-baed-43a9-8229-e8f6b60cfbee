package com.nq.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.constant.OrderConstant;
import com.nq.dao.SiteMessageMapper;
import com.nq.dao.UserMapper;
import com.nq.dao.UserRechargeMapper;
import com.nq.enums.OrderTypeEnum;
import com.nq.enums.TypeEnum;
import com.nq.excepton.CustomException;
import com.nq.function.AmountConsumer;
import com.nq.manage.UserAmountChangeManage;
import com.nq.pojo.*;
import com.nq.service.*;
import com.nq.utils.DateTimeUtil;
import com.nq.utils.DateUtils;
import com.nq.utils.SnowIdUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Service("iUserRechargeService")
public class UserRechargeServiceImpl extends ServiceImpl<UserRechargeMapper, UserRecharge> implements IUserRechargeService {
    private static final Logger log = LoggerFactory.getLogger(UserRechargeServiceImpl.class);

    @Resource
    private UserRechargeMapper userRechargeMapper;
    @Resource
    private IUserService iUserService;
    @Resource
    private UserMapper userMapper;
    @Resource
    private IPayService payService;
    @Resource
    private ISiteSettingService iSiteSettingService;
    @Resource
    private SiteMessageMapper siteMessageMapper;
    @Resource
    private UserAmountChangeManage userAmountChangeManage;
    @Resource
    private MerchantPayService merchantPayService;


    public void checkInMoney(int maxOrder, Integer userId) {
        int count = this.userRechargeMapper.checkInMoney(0, userId);
        if (count > maxOrder) {
            throw new CustomException("一小时内只能发起" + maxOrder + "次入金");
        }
    }


    public String inMoney(String amt, Integer payId) {
        if (StringUtils.isBlank(amt)) {
            throw new CustomException("参数不能为空");
        }
        if (payId == null){
            throw new CustomException("参数不能为空");
        }
        SiteSetting siteSetting = this.iSiteSettingService.getSiteSetting();
        if (siteSetting == null) {
            throw new CustomException("设置set未初始化");
        }
        if ((new BigDecimal(siteSetting.getChargeMinAmt() + "")).compareTo(new BigDecimal(amt)) == 1) {
            throw new CustomException("充值金额不得低于" + siteSetting.getChargeMinAmt() + "元");
        }
        User user = this.iUserService.getCurrentUser();
        if (user.getIsActive() != 2) {
            throw new CustomException("未实名认证不能发起充值");
        }
        MerchantPay merchantPay = merchantPayService.getById(payId);
        if (merchantPay == null) {
            throw new CustomException("支付通道不存在");
        }
        checkInMoney(10, user.getId());
        payService.recharge(merchantPay, amt);
        return "充值成功";
    }

    @Override
    public void inMoneyByAdmin(String orderNo, String amt, String payType, Integer uid) {
        if (StringUtils.isBlank(amt) || StringUtils.isBlank(payType)) {
            throw new CustomException("参数不能为空");
        }
        User user = iUserService.findByUserId(uid);
        UserRecharge userRecharge = new UserRecharge();
        userRecharge.setUserId(user.getId());
        userRecharge.setNickName(user.getNickName());
        userRecharge.setOrderSn(orderNo);
        userRecharge.setPayChannel(payType);
        userRecharge.setPayAmt(new BigDecimal(amt));
        userRecharge.setOrderStatus(1);
        userRecharge.setAddTime(new Date());
        userRecharge.setPayTime(new Date());
        int insertCount = this.userRechargeMapper.insert(userRecharge);
        if (insertCount > 0) {
            return;
        }
        throw new CustomException("创建支付订单失败");
    }


    public UserRecharge findUserRechargeByOrderSn(String orderSn) {
        LambdaQueryWrapper<UserRecharge> q = new LambdaQueryWrapper<>();
        q.eq(UserRecharge::getOrderSn, orderSn);
        UserRecharge userRecharge = this.getOne(q);
        if (userRecharge != null) {
            return userRecharge;
        }
        throw new CustomException("找不到充值订单");
    }


    @Transactional
    public void chargeSuccess(UserRecharge userRecharge) throws Exception {
        if (userRecharge.getOrderStatus() != 0) {
            throw new CustomException("订单状态不能重复修改");
        }
        User user = this.userMapper.selectById(userRecharge.getUserId());
        if (user == null) {
            throw new CustomException("用户不存在");
        }
        BigDecimal enableAmt_before = user.getEnableAmt();
        user.setEnableAmt(enableAmt_before.add(userRecharge.getPayAmt()));
        int updateCount = this.userMapper.updateById(user);
        if (updateCount > 0) {
            log.info("1.修改用户资金成功");
        } else {
            throw new CustomException("失败，修改用户资金失败");
        }

        userRecharge.setOrderStatus(1);
        userRecharge.setPayTime(new Date());
        int updateCCount = this.userRechargeMapper.updateById(userRecharge);
        if (updateCCount > 0) {
            log.info("2.修改订单状态成功");
        } else {
            throw new Exception("2. 修改订单状态失败!");
        }
    }


    public void chargeFail(UserRecharge userRecharge) throws Exception {
        if (userRecharge.getOrderStatus() != 0) {
            throw new CustomException("订单状态不能重复修改");
        }

        userRecharge.setOrderStatus(2);
        int updateCCount = this.userRechargeMapper.updateById(userRecharge);
        if (updateCCount > 0) {
            return;
        }
        throw new CustomException("修改出现异常");
    }


    public void chargeCancel(UserRecharge userRecharge) throws Exception {
        if (userRecharge.getOrderStatus() != 0) {
            throw new CustomException("订单状态不能重复修改");
        }

        userRecharge.setOrderStatus(3);
        int updateCCount = this.userRechargeMapper.updateById(userRecharge);
        if (updateCCount > 0) {
            return;
        }
        throw new CustomException("订单取消出现异常");
    }


    public PageInfo<UserRecharge> findUserChargeList(String payChannel, String orderStatus, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        User user = this.iUserService.getCurrentUser();
        LambdaQueryWrapper<UserRecharge> q = new LambdaQueryWrapper<>();
        q.eq(StrUtil.isNotEmpty(payChannel), UserRecharge::getPayChannel, payChannel);
        q.eq(StrUtil.isNotEmpty(orderStatus), UserRecharge::getOrderStatus, orderStatus);
        q.eq(UserRecharge::getUserId, user.getId());
        q.orderByDesc(UserRecharge::getId);
        List<UserRecharge> userRecharges = this.userRechargeMapper.selectList(q);
        log.info("充值列表，增加用户 {} ，payChannel = {} , orderStatus = {}， 数量 = {}", new Object[]{user.getId(), payChannel, orderStatus, userRecharges.size()});
        return new PageInfo<UserRecharge>(userRecharges);
    }


    public PageInfo<UserRecharge> listByAdmin(Integer userId, String orderSn, String nickName, Integer state, String beginTime, String endTime, int pageNum, int pageSize, Boolean isAgent) {
        PageHelper.startPage(pageNum, pageSize);
        Timestamp begin_time = null;
        if (StringUtils.isNotBlank(beginTime)) {
            begin_time = DateTimeUtil.searchStrToTimestamp(beginTime);
        }
        Timestamp end_time = null;
        if (StringUtils.isNotBlank(endTime)) {
            end_time = DateTimeUtil.searchStrToTimestamp(endTime);
        }
        LambdaQueryWrapper<UserRecharge> q = new LambdaQueryWrapper<>();
        q.eq(userId != null, UserRecharge::getUserId, userId);
        q.like(StringUtils.isNotBlank(nickName), UserRecharge::getNickName, nickName);
        q.like(StringUtils.isNotBlank(orderSn), UserRecharge::getOrderSn, orderSn);
        q.eq(state != null, UserRecharge::getOrderStatus, state);
        q.ge(begin_time != null, UserRecharge::getPayTime, begin_time);
        q.le(end_time != null, UserRecharge::getPayTime, end_time);
        q.in(isAgent, UserRecharge::getUserId, iUserService.getUserIdByAgent());
        q.orderByDesc(UserRecharge::getId);
        List<UserRecharge> userRecharges = this.userRechargeMapper.selectList(q);

        return new PageInfo<>(userRecharges);
    }


    @Transactional
    public void updateState(Long chargeId, Integer state, String orderDesc) {
        UserRecharge userRecharge = this.userRechargeMapper.selectById(chargeId);
        if (userRecharge == null) {
            throw new CustomException("充值订单不存在");
        }
        if (userRecharge.getOrderStatus() != 0) {
            throw new CustomException("订单状态不是下单状态不能更改");
        }
        if (state == 1) {
            User user = this.userMapper.selectById(userRecharge.getUserId());
            if (user == null) {
                throw new CustomException("用户不存在");
            }
            BigDecimal amount = userRecharge.getPayAmt();
            String orderSn = userRecharge.getOrderSn();
            AmountConsumer consumer = (oldUser, newUser) -> {
                userRecharge.setOrderStatus(1);
                userRecharge.setOrderDesc(orderDesc);
                userRecharge.setPayTime(new Date());
                this.userRechargeMapper.updateById(userRecharge);
                SiteMessage siteMessage = new SiteMessage();
                siteMessage.setUserId(user.getId());
                siteMessage.setMessageType(SiteMessage.MSG_TYPE_WAP);
                siteMessage.setStatus(1);
                siteMessage.setUserName(user.getRealName());
                siteMessage.setAddTime(new Date());
                siteMessage.setTypeName("充值成功");
                siteMessage.setContent("您的账户成功充值 " + userRecharge.getPayAmt());
                this.siteMessageMapper.insert(siteMessage);
            };
            userAmountChangeManage.changeBalance(user.getId(), amount, orderSn, OrderTypeEnum.BACK,
                    TypeEnum.RECHARGE, "", "", consumer);
            return;
        }
        userRecharge.setOrderStatus(2);
        userRecharge.setOrderDesc(orderDesc);
        userRecharge.setPayTime(new Date());
        int updateCount = this.userRechargeMapper.updateById(userRecharge);
        if (updateCount > 0) {
            User user = this.userMapper.selectById(userRecharge.getUserId());
            if (user != null) {
                SiteMessage siteMessage = new SiteMessage();
                siteMessage.setUserId(user.getId());
                siteMessage.setMessageType(SiteMessage.MSG_TYPE_WAP);
                siteMessage.setStatus(1);
                siteMessage.setUserName(user.getRealName());
                siteMessage.setAddTime(new Date());
                siteMessage.setTypeName("充值失败");
                siteMessage.setContent("您的账户充值 " + userRecharge.getPayAmt() + " 失败，请联系客服");
                this.siteMessageMapper.insert(siteMessage);
            }
            return;
        }
        throw new CustomException("修改订单状态失败！");
    }


    public void createOrder(Integer userId, Integer state, Integer amt, String payChannel) {
        if (userId == null || state == null || amt == null) {
            throw new CustomException("参数不能为空");
        }

        User user = this.userMapper.selectById(userId);
        if (user == null) {
            throw new CustomException("找不到用户");
        }

        UserRecharge userRecharge = new UserRecharge();
        userRecharge.setUserId(user.getId());
        userRecharge.setNickName(user.getNickName());
        String orderNo = SnowIdUtil.getId(OrderConstant.rechargeOrder);
        userRecharge.setOrderSn(orderNo);

        userRecharge.setPayChannel(payChannel);
        userRecharge.setPayAmt(new BigDecimal(amt));
        userRecharge.setAddTime(new Date());
        userRecharge.setPayTime(new Date());
        if (state == 0) {
            userRecharge.setOrderStatus(0);
        } else if (state == 1) {
            userRecharge.setPayChannel("2");
            userRecharge.setOrderStatus(1);
            AmountConsumer consumer = (oldUser, newUser) -> {
                this.userRechargeMapper.insert(userRecharge);
            };
            userAmountChangeManage.changeBalance(user.getId(), userRecharge.getPayAmt(), orderNo, OrderTypeEnum.BACK,
                    TypeEnum.RECHARGE, "", "", consumer);
            return;
        } else if (state == 2) {
            userRecharge.setOrderStatus(2);
        } else {
            throw new CustomException("订单状态不正确");
        }
        int insertCount = this.userRechargeMapper.insert(userRecharge);
        if (insertCount > 0) {
            return;
        }
        throw new CustomException("生成订单失败，请重试");
    }


    public void del(Long cId) {
        if (cId == null) {
            throw new CustomException("id不能为空");
        }
        int updateCount = this.userRechargeMapper.deleteById(cId);
        if (updateCount > 0) {
            return;
        }
        throw new CustomException("删除失败");
    }


    public int deleteByUserId(Integer userId) {
        return this.userRechargeMapper.deleteByUserId(userId);
    }


    public BigDecimal CountChargeSumAmt(Integer chargeState) {
        return this.userRechargeMapper.CountChargeSumAmt(chargeState);
    }

    public BigDecimal CountTotalRechargeAmountByTime(Integer chargeState) {
        return this.userRechargeMapper.CountTotalRechargeAmountByTime(chargeState);
    }

    @Override
    public List<UserRecharge> exportByAdmin(Integer userId, String orderSn, String nickName, Integer state, String beginTime, String endTime, Boolean isAgent) {
        Timestamp begin_time = null;
        if (StringUtils.isNotBlank(beginTime)) {
            begin_time = DateTimeUtil.searchStrToTimestamp(beginTime);
        }
        Timestamp end_time = null;
        if (StringUtils.isNotBlank(endTime)) {
            end_time = DateTimeUtil.searchStrToTimestamp(endTime);
        }
        LambdaQueryWrapper<UserRecharge> q = new LambdaQueryWrapper<>();
        q.eq(userId != null, UserRecharge::getUserId, userId);
        q.like(StringUtils.isNotBlank(nickName), UserRecharge::getNickName, nickName);
        q.like(StringUtils.isNotBlank(orderSn), UserRecharge::getOrderSn, orderSn);
        q.eq(state != null, UserRecharge::getOrderStatus, state);
        q.ge(begin_time != null, UserRecharge::getPayTime, begin_time);
        q.le(end_time != null, UserRecharge::getPayTime, end_time);
        q.in(isAgent, UserRecharge::getUserId, iUserService.getUserIdByAgent());
        q.orderByDesc(UserRecharge::getId);
        return this.list(q);
    }

    @Override
    public BigDecimal countChargeAmountByDate(Integer type) {
        HashMap<String, Date> todayMap = DateUtils.changeTimeSection(type);
        Date startTime = todayMap.get("startTime");
        Date endTime = todayMap.get("endTime");
        LambdaQueryWrapper<UserRecharge> q = new LambdaQueryWrapper<>();
        q.orderByDesc(UserRecharge::getId);
        q.ge(UserRecharge::getAddTime, startTime);
        q.le(UserRecharge::getAddTime, endTime);
        q.eq(UserRecharge::getOrderStatus, 1);
        List<UserRecharge> arr = this.list(q);
        return arr.stream().map(UserRecharge::getPayAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public Long countChargeNumByDate(Integer type) {
        HashMap<String, Date> todayMap = DateUtils.changeTimeSection(type);
        Date startTime = todayMap.get("startTime");
        Date endTime = todayMap.get("endTime");
        LambdaQueryWrapper<UserRecharge> q = new LambdaQueryWrapper<>();
        q.orderByDesc(UserRecharge::getId);
        q.ge(UserRecharge::getPayTime, startTime);
        q.le(UserRecharge::getPayTime, endTime);
        q.eq(UserRecharge::getOrderStatus, 1);
        return this.count(q);
    }

    @Override
    public BigDecimal countFirstChargeAmountByDate(Integer type) {
        HashMap<String, Date> todayMap = DateUtils.changeTimeSection(type);
        Date startTime = todayMap.get("startTime");
        Date endTime = todayMap.get("endTime");
        LambdaQueryWrapper<User> userQuery = new LambdaQueryWrapper<>();
        userQuery.ge(startTime != null, User::getFirstRechargeTime, startTime);
        userQuery.le(endTime != null, User::getFirstRechargeTime, endTime);
        List<User> usersWithFirstRechargeToday = this.userMapper.selectList(userQuery);
        if (usersWithFirstRechargeToday.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal totalAmountOfFirstRecharges = BigDecimal.ZERO;

        for (User user : usersWithFirstRechargeToday) {
            LambdaQueryWrapper<UserRecharge> firstRechargeQuery = new LambdaQueryWrapper<>();
            firstRechargeQuery
                    .eq(UserRecharge::getUserId, user.getId())
                    .eq(UserRecharge::getOrderStatus, 1) // Successful recharges
                    // Query for recharges at or after their recorded firstRechargeTime, within today
                    .ge(UserRecharge::getPayTime, startTime)
                    .le(UserRecharge::getPayTime, endTime)
                    .orderByAsc(UserRecharge::getPayTime)  // Get the earliest by payTime
                    .orderByAsc(UserRecharge::getId)       // Tie-break with the smallest ID
                    .last("LIMIT 1");                      // Fetch only one record

            // Assuming 'this' is UserRechargeServiceImpl which can access the UserRechargeMapper
            UserRecharge theVeryFirstRechargeRecord = this.getOne(firstRechargeQuery);

            if (theVeryFirstRechargeRecord != null) {
                totalAmountOfFirstRecharges = totalAmountOfFirstRecharges.add(theVeryFirstRechargeRecord.getPayAmt());
            }
        }
        return totalAmountOfFirstRecharges;
    }

    @Override
    public Long countChargePendingCount() {
        LambdaQueryWrapper<UserRecharge> q = new LambdaQueryWrapper<>();
        q.eq(UserRecharge::getOrderStatus, 0);
        return this.count(q);
    }
}
