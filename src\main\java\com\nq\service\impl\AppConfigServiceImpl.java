package com.nq.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.dao.AppConfigMapper;
import com.nq.pojo.AppConfig;
import com.nq.service.AppConfigService;
import com.nq.utils.BeanCopyUtil;
import com.nq.vo.app.AppConfigVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

@Service
public class AppConfigServiceImpl extends ServiceImpl<AppConfigMapper, AppConfig> implements AppConfigService {

    @Override
    public PageInfo<AppConfig> pageList(Integer pageNum, Integer pageSize, String groupName, String configName, String type) {
        PageHelper.startPage(pageNum, pageSize);

        LambdaQueryWrapper<AppConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(groupName), AppConfig::getGroupName, groupName)
              .like(StringUtils.hasText(configName), AppConfig::getConfigName, configName)
              .like(StringUtils.hasText(type), AppConfig::getType, type)
              .orderByDesc(AppConfig::getCreatedTime);

        List<AppConfig> list = this.list(wrapper);
        return new PageInfo<>(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(AppConfig appConfig) {
        // 检查是否已存在相同的组名和配置名
        AppConfig exists = getByGroupAndName(appConfig.getGroupName(), appConfig.getConfigName());
        if (exists != null) {
            throw new RuntimeException("该配置已存在");
        }

        appConfig.setCreatedTime(new Date());
        this.save(appConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(AppConfig appConfig) {
        // 检查是否存在
        AppConfig exists = this.getById(appConfig.getId());
        if (exists == null) {
            throw new RuntimeException("配置不存在");
        }

        // 如果修改了组名或配置名，需要检查是否与其他配置冲突
        if (!exists.getGroupName().equals(appConfig.getGroupName())
            || !exists.getConfigName().equals(appConfig.getConfigName())) {
            AppConfig conflict = getByGroupAndName(appConfig.getGroupName(), appConfig.getConfigName());
            if (conflict != null && !conflict.getId().equals(appConfig.getId())) {
                throw new RuntimeException("该配置已存在");
            }
        }

        this.updateById(appConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer id) {
        this.removeById(id);
    }

    @Override
    public AppConfig getByGroupAndName(String groupName, String configName) {
        LambdaQueryWrapper<AppConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppConfig::getGroupName, groupName)
              .eq(AppConfig::getConfigName, configName);
        return this.getOne(wrapper);
    }

    @Override
    public AppConfigVO queryConfig(String key) {
        LambdaQueryWrapper<AppConfig> q = new LambdaQueryWrapper<>();
        q.eq(AppConfig::getConfigName, key);
        q.last("limit 1");
        AppConfig one = this.getOne(q);
        AppConfigVO appConfigVo = BeanCopyUtil.copyProperties(one, AppConfigVO.class);
        return appConfigVo;
    }

    @Override
    public List<AppConfigVO> queryGroupConfig(String groupName) {
        LambdaQueryWrapper<AppConfig> q = new LambdaQueryWrapper<>();
        q.eq(AppConfig::getGroupName, groupName);
        List<AppConfig> arr = this.list(q);
        List<AppConfigVO> appConfigVOS = BeanCopyUtil.copyToList(arr, AppConfigVO.class);
        return appConfigVOS;
    }
}