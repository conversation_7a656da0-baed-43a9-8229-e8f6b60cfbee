package com.nq.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.dto.app.TradeRecordQueryDTO;
import com.nq.pojo.FollowTrade;
import com.nq.pojo.Stock;
import com.nq.vo.admin.FollowTradeVO;
import com.nq.vo.app.TradeRecordVO;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

public interface FollowTradeService extends IService<FollowTrade> {
    /**
     * 获取跟单交易列表
     *
     * @param page      页码
     * @param size      每页数量
     * @param mentorId  导师ID
     * @param stockCode 股票代码
     * @param status    状态
     * @return 分页数据
     */
    PageInfo<FollowTradeVO> getFollowTradeList(Integer page, Integer size, Integer mentorId, String stockCode,
            Integer status);

    /**
     * 获取跟单交易详情
     *
     * @param id 交易ID
     * @return 交易详情
     */
    FollowTradeVO getFollowTradeById(Integer id);

    /**
     * 创建跟单交易
     *
     * @param followTrade 交易信息
     * @return 创建结果
     */
    boolean createFollowTrade(FollowTrade followTrade);

    /**
     * 更新跟单交易
     *
     * @param followTrade 交易信息
     * @return 更新结果
     */
    boolean updateFollowTrade(FollowTrade followTrade);

    /**
     * 处理跟单详情
     *
     * @param followTrade 交易信息
     */
    void processFollowDetails(FollowTrade followTrade);

    /**
     * 删除跟单交易
     *
     * @param id 交易ID
     * @return 删除结果
     */
    boolean deleteFollowTrade(Integer id);

    /**
     * 发布跟单交易
     *
     * @param id 交易ID
     * @return 发布结果
     */
    boolean publishFollowTrade(Integer id);

    /**
     * 清仓跟单交易
     *
     * @param id        交易ID
     * @param sellPrice 卖出价格
     * @return 清仓结果
     */
    boolean closeFollowTrade(Integer id, BigDecimal sellPrice);

    /**
     * 获取导师跟单交易记录
     *
     * @param dto
     * @return
     */
    TradeRecordVO getTradingRecords(@Valid TradeRecordQueryDTO dto);

    /**
     * 获取导师持仓中的股票列表（去重）
     *
     * @param mentorId 导师ID
     * @return 股票列表
     */
    List<String> getMentorHoldingStocks(Integer mentorId);

    /**
     * 根据关键字查询股票
     *
     * @param keyword 关键字（股票代码或股票名称）
     * @param limit   限制返回数量
     * @return 股票列表
     */
    List<Stock> searchStocks(String keyword, Integer limit);
}