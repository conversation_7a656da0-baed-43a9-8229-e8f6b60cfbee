package com.nq.vo.app;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
@Data
@ApiModel(description = "配置")
public class AppConfigVO {

    @ApiModelProperty(value = "配置组信息", example = "系统配置")
    private String groupName;

    @ApiModelProperty(value = "配置每一项key", example = "site.name")
    private String configName;

    @ApiModelProperty(value = "配置内容", example = "交易系统")
    private String configValue;

    @ApiModelProperty(value = "类型(text,mp3,mp4,jpg)", example = "text")
    private String type;

    @ApiModelProperty(value = "备注", example = "系统名称配置")
    private String remark;

    @ApiModelProperty(value = "创建时间", example = "2024-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;
}
