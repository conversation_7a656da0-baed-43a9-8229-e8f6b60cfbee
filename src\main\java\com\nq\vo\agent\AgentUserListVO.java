
package com.nq.vo.agent;

import lombok.Data;

import java.math.BigDecimal;
@Data
public class AgentUserListVO {
    private Integer id;

    private Integer agentId;

    private String agentName;

    private String phone;

    private String realName;

    private String idCard;


    private Integer isLock;

    private Integer isLogin;

    private String regAddress;

    private Integer isActive;

    private String bankName;

    private String bankNo;

    private String bankAddress;

    private BigDecimal userAmt;

    private BigDecimal enableAmt;

    private BigDecimal forceLine;

    private BigDecimal allProfitAndLose;

    private BigDecimal allFreezAmt;


    private BigDecimal indexForceLine;







}
