package com.nq.dto.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "根据跟单记录获取跟进记录详情")
public class MerchantPayDTO {

    /**
     * id
     */
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 商户名称
     */
    @ApiModelProperty(value="商户名称")
    private String name;

    /**
     * 商户头像
     */
    @ApiModelProperty(value="商户头像")
    private String avatar;

    /**
     * 充值区间
     */
    @ApiModelProperty(value="充值区间")
    private String rechargeRange;

    /**
     * 提现区间
     */
    @ApiModelProperty(value="提现区间")
    private String withdrawRange;

    /**
     * 币种
     */
    @ApiModelProperty(value="币种")
    private String currency;

    /**
     * 支付code
     */
    @ApiModelProperty(value="支付code")
    private String code;

    /**
     * 汇率
     */
    @ApiModelProperty(value="汇率")
    private BigDecimal rate;

} 