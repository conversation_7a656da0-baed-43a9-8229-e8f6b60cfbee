package com.nq.controller.app;


import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.service.IStockOptionService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.nq.vo.stock.StockOptionListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(tags = "APP-自选股")
@RequestMapping({"/user/option/"})
public class AppUserOptionController {
    private static final Logger log = LoggerFactory.getLogger(AppUserOptionController.class);

    @Resource
    private IStockOptionService iStockOptionService;

    //查询所有自选股票信息及模糊查询
    @ApiOperation("查询所有自选股票信息及模糊查询")
    @PostMapping({"list.do"})
    public ServerResponse<PageInfo<StockOptionListVO>> list(HttpServletRequest request, @RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "10") int pageSize, @RequestParam(value = "keyWords", required = false) String keyWords) {
        PageInfo<StockOptionListVO> myStockOptions = iStockOptionService.findMyStockOptions(keyWords, request, pageNum, pageSize);
        return ServerResponse.createBySuccess(myStockOptions);
    }
}
