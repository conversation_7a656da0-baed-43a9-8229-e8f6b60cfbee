<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nq.dao.LoadEarnMapper">
    <resultMap id="BaseResultMap" type="com.nq.pojo.LoadEarn">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="real_name" property="realName" jdbcType="VARCHAR"/>
        <result column="level" property="level" jdbcType="INTEGER"/>
        <result column="salary_rate" property="salaryRate" jdbcType="DECIMAL"/>
        <result column="total_follow_earn_amount" property="totalFollowEarnAmount" jdbcType="DECIMAL"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="amount" property="amount" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, order_no, user_id, user_name, real_name, level, salary_rate, total_follow_earn_amount, start_time, end_time, amount, create_time
    </sql>
</mapper> 