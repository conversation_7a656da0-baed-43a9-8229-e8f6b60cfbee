package com.nq.utils.task.stock;


import com.nq.service.IStockMarketsDayService;
import com.nq.utils.HolidayUtil;
import com.nq.utils.RedisKey;
import com.nq.utils.RedisUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;


@Component
public class SaveDayMarketsTask {
    private static final Logger log = LoggerFactory.getLogger(SaveDayMarketsTask.class);
    @Resource
    private HolidayUtil holidayUtil;
    @Resource
    private IStockMarketsDayService iStockMarketsDayService;
    @Resource
    private RedisUtil redisUtil;

    /*每天16點股票數據定時存入數據庫（股票走勢圖數據存儲）*/
    @Scheduled(cron = "0 0 16 ? * MON-FRI")
    public void banlanceUserPositionTaskV1() {
        dotask();
    }

    public void dotask() {
        this.iStockMarketsDayService.saveStockMarketDay();
    }

    /*每天1點同步節假日開關 8*/
    @Scheduled(cron = "0 0 1 * * ?")
    public void holidayTask() {
        this.iStockMarketsDayService.saveHoliday();
    }


    /*我希望定时 存入reids 工作日 list  */
    @Scheduled(cron = "0 0 1 * * ?")// Run every hour at minute 0
    public void saveDateRedis() {
        String redisKey = RedisKey.WORKING_DAYS_LIST_KEY;
        redisUtil.del(redisKey);
        holidayUtil.saveDateRedis(6);
    }
}
