package com.nq.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.nq.dto.common.CommonPage;
import com.nq.pojo.Activity;
import com.nq.utils.PageUtil;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nq.dao.ActivityReceiveMapper;
import com.nq.pojo.ActivityReceive;
import com.nq.service.ActivityReceiveService;
@Service
public class ActivityReceiveServiceImpl extends ServiceImpl<ActivityReceiveMapper, ActivityReceive> implements ActivityReceiveService{

    @Override
    public PageInfo<ActivityReceive> listByAdmin(ActivityReceive activityReceive, CommonPage commonPage) {
        PageUtil.startPage(commonPage);
        LambdaQueryWrapper<ActivityReceive> q = new LambdaQueryWrapper<>();
        this.buildQuery(q, activityReceive);
        return null;
    }

    private void buildQuery(LambdaQueryWrapper<ActivityReceive> q, ActivityReceive i) {
        q.like(StrUtil.isNotEmpty(i.getActivityName()), ActivityReceive::getActivityName, i.getActivityName());
        q.eq(StrUtil.isNotEmpty(i.getNickName()), ActivityReceive::getNickName, i.getNickName());
        q.orderByDesc(ActivityReceive::getReceiveId);
    }
}
