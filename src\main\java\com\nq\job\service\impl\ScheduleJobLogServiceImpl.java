/**
 * Copyright (c) 2016-2019 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package com.nq.job.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.dao.ScheduleJobLogDao;
import com.nq.job.entity.ScheduleJobLogEntity;
import com.nq.job.service.ScheduleJobLogService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("scheduleJobLogService")
public class ScheduleJobLogServiceImpl extends ServiceImpl<ScheduleJobLogDao, ScheduleJobLogEntity>
		implements ScheduleJobLogService {

	@Override
	public PageInfo<ScheduleJobLogEntity> queryPage(Map<String, Object> params) {
		String jobId = (String) params.get("jobId");

		// 获取分页参数
		int pageNum = 1;
		int pageSize = 10;

		if (params.get("page") != null) {
			pageNum = Integer.parseInt((String) params.get("page"));
		}
		if (params.get("limit") != null) {
			pageSize = Integer.parseInt((String) params.get("limit"));
		}

		// 使用PageHelper进行分页
		PageHelper.startPage(pageNum, pageSize);

		// 构建查询条件
		QueryWrapper<ScheduleJobLogEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.like(StringUtils.isNotBlank(jobId), "job_id", jobId);
		queryWrapper.orderByDesc("create_time");

		// 执行查询
		List<ScheduleJobLogEntity> list = this.list(queryWrapper);

		// 返回分页结果
		return new PageInfo<>(list);
	}

}
