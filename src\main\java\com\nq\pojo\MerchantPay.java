package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商户表
 */
@ApiModel(description="商户表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "merchant_pay")
public class MerchantPay {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 商户名称
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value="商户名称")
    private String name;

    /**
     * 商户头像
     */
    @TableField(value = "avatar")
    @ApiModelProperty(value="商户头像")
    private String avatar;

    /**
     * 充值区间
     */
    @TableField(value = "recharge_range")
    @ApiModelProperty(value="充值区间")
    private String rechargeRange;

    /**
     * 提现区间
     */
    @TableField(value = "withdraw_range")
    @ApiModelProperty(value="提现区间")
    private String withdrawRange;

    /**
     * 币种
     */
    @TableField(value = "currency")
    @ApiModelProperty(value="币种")
    private String currency;

    /**
     * 汇率
     */
    @TableField(value = "rate")
    @ApiModelProperty(value="汇率")
    private BigDecimal rate;

    /**
     * 支付code
     */
    @TableField(value = "code")
    @ApiModelProperty(value="支付code")
    private String code;

    /**
     * 商户状态 0 冻结 1 正常
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value="商户状态 0 冻结 1 正常")
    private Integer status;

    /**
     * 用于添加支付参数
     */
    @TableField(value = "param_str")
    @ApiModelProperty(value="用于添加支付参数")
    private String paramStr;

    /**
     * 回调地址
     */
    @TableField(value = "call_back_ip")
    @ApiModelProperty(value="回调地址")
    private String callBackIp;

    /**
     * 0 不显示 1 显示
     */
    @TableField(value = "is_rechange")
    @ApiModelProperty(value="0 不显示 1 显示")
    private Integer isRechange;

    /**
     * 0 不显示 1显示
     */
    @TableField(value = "is_withdraw")
    @ApiModelProperty(value="0 不显示 1显示")
    private Integer isWithdraw;

    /**
     * 排序  
     */
    @TableField(value = "sort")
    @ApiModelProperty(value="排序  ")
    private Integer sort;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value="更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    private Date createTime;
}