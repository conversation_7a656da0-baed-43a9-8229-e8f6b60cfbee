package com.nq.controller.admin;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.Package;
import com.nq.service.PackageService;
import com.nq.vo.admin.PackageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;

@Api(tags = "后台-套餐管理")
@RestController
@RequestMapping("/admin/package")
public class AdminPackageController {

    @Resource
    private PackageService packageService;

    @ApiOperation("分页查询套餐列表")
    @GetMapping("/list.do")
    public ServerResponse<PageInfo<PackageVO>> list(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页数量") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("套餐名称") @RequestParam(required = false) String productName
    ) {
        PageInfo<PackageVO> pageInfo = packageService.pageList(pageNum, pageSize, productName, null);
        return ServerResponse.createBySuccess(pageInfo);
    }

    @ApiOperation("添加套餐")
    @PostMapping("/add.do")
    public ServerResponse<String> add(@RequestBody Package pack) {
        pack.setCreateTime(new Date());
        pack.setUpdateTime(new Date());
        // 如果没有设置排序值，默认为0
        if (pack.getSort() == null) {
            pack.setSort(0);
        }
        packageService.save(pack);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation("修改套餐信息")
    @PostMapping("/update.do")
    public ServerResponse<String> update(@RequestBody Package pack) {
        pack.setUpdateTime(new Date());
        // 如果没有设置排序值，默认为0
        if (pack.getSort() == null) {
            pack.setSort(0);
        }
        packageService.updateById(pack);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation("删除套餐")
    @GetMapping("/delete.do")
    public ServerResponse<String> delete(@RequestParam Integer id) {
        packageService.removeById(id);
        return ServerResponse.createBySuccess();
    }

    @ApiOperation("获取套餐详情")
    @GetMapping("/info.do")
    public ServerResponse<PackageVO> info(@RequestParam Integer id) {
        PackageVO packageVO = packageService.getDetailById(id);
        return ServerResponse.createBySuccess(packageVO);
    }
}