package com.nq.utils.task.stock;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nq.dao.StockSubscribeMapper;
import com.nq.service.IStockService;
import com.nq.common.ServerResponse;
import com.nq.dao.UserMapper;
import com.nq.dao.UserPositionMapper;
import com.nq.pojo.*;
import com.nq.service.*;
import com.nq.utils.*;
import com.nq.utils.stock.GeneratePosition;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 新股申购接口抓取任务
 */

@Component
public class NewStockTask {

    private static final Logger log = LoggerFactory.getLogger(NewStockTask.class);
    @Resource
    private IStockService iStockService;
    @Resource
    private StockSubscribeMapper stockSubscribeMapper;
    /**
     * 每天工作日 下午4点执行一次
     */
    @Scheduled(cron = "0 0 16 * * MON-FRI")
    public void getNewStockTask() {
        log.info("----------每天工作日下午4点抓取新股日历数据开始--------------");
        Integer count = 0;
        try {
            String url = PropertiesUtil.getProperty("dfcf.new.stock.url");
            String s = HttpRequest.doGet(url, null);
            JSONObject jsonObject = JSON.parseObject(s);
            JSONObject result = jsonObject.getJSONObject("result");
            JSONArray data = result.getJSONArray("data");
            for (int i = 0; i < data.size(); i++) {
                JSONObject o = data.getJSONObject(i);
                //先判断当前新股是否插入
                StockSubscribe newStock = stockSubscribeMapper.selectOne(new QueryWrapper<StockSubscribe>().eq("code", o.getString("SECURITY_CODE")));
                StockSubscribe StockSubscribe = new StockSubscribe();
                StockSubscribe.setCode(o.getString("SECURITY_CODE"));
                StockSubscribe.setSubscribeTime(o.getDate("APPLY_DATE"));
                StockSubscribe.setListDate(o.getDate("LISTING_DATE") == null ? null : o.getDate("LISTING_DATE"));
                StockSubscribe.setSubscriptionTime(o.getDate("BALLOT_NUM_DATE") == null ? null : o.getDate("BALLOT_NUM_DATE"));
                StockSubscribe.setName(o.getString("SECURITY_NAME"));
//                StockSubscribe.setPe(o.getString("AFTER_ISSUE_PE") == null ? null : o.getString("AFTER_ISSUE_PE"));
                StockSubscribe.setPrice(o.getBigDecimal("ISSUE_PRICE"));
                StockSubscribe.setOrderNumber(o.getLong("ISSUE_NUM"));
                StockSubscribe.setZt(1);
                StockSubscribe.setStockType(setType(o.getString("SECURITY_CODE")));

                if (null == newStock) {
                    stockSubscribeMapper.insert(StockSubscribe);
                    count++;
                } else {
                    StockSubscribe.setNewlistId(newStock.getNewlistId());
                    stockSubscribeMapper.updateById(StockSubscribe);
                }
            }
            log.info("----------每天工作日下午4点抓取新股日历数据结束 新增新股数量：{}--------------", count);
        } catch (Exception e) {
            log.error("抓取新股日历数据", e);
        }

    }

    /**
     * 类型
     */
    private String setType(String code){
        String type = "";
                if(code.startsWith("00")){
                    type = "深";
                }else if(code.startsWith("60")){
                   type = "沪";
                }else if(code.startsWith("30")){
                   type = "创";
                }else if(code.startsWith("68")){
                   type = "科";
                }else {
                   type = "北";
                }
              return type;
    }


    /**
     * 每天工作日 上午8点20执行一次 保存新股票 (刷新价格)
     */
    @Scheduled(cron = "0 20 8  * * MON-FRI")
    public void saveNewStockTask() {
        String nowDate = DateTimeUtil.stampToDate(String.valueOf(System.currentTimeMillis()));
        List<StockSubscribe> newShowStockList = stockSubscribeMapper.selectList(new QueryWrapper<StockSubscribe>().le("list_date",nowDate));
        log.info("----------每天工作日上午8点20抓取新股日历数据结束 新增新股数量：{}--------------", newShowStockList.size());
        if (!CollectionUtils.isEmpty(newShowStockList)) {
            for (StockSubscribe StockSubscribe : newShowStockList) {
                String codes = StockSubscribe.getCode();
                String plateType = "sh";
                if (codes.startsWith("00") || codes.startsWith("30")) {
                    plateType = "sz";
                } else if (codes.startsWith("8") || codes.startsWith("4")) {
                    plateType = "bj";
                }
                Stock stock = this.iStockService.findStockByCode(StockSubscribe.getCode());
                if (stock == null) {
                    this.iStockService.addStock(StockSubscribe.getName(), StockSubscribe.getCode()
                            , plateType, "cc", 0, 0);
//                    log.info("新增新股：{}，返回结果：{}", StockSubscribe.getName(), response.getMsg());
                }

            }
        }

    }



}
