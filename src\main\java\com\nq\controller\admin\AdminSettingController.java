package com.nq.controller.admin;

import com.nq.common.ServerResponse;
import com.nq.pojo.SiteProduct;
import com.nq.pojo.SiteSetting;
import com.nq.service.ISiteProductService;
import com.nq.service.ISiteSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "后台-登录和设置")
@RestController
@RequestMapping({"/api/admin/"})
public class AdminSettingController {


    @Resource
    private ISiteSettingService iSiteSettingService;
    @Resource
    private ISiteProductService iSiteProductService;



    //查询风控设置 股票分控信息
    @ApiOperation(value = "查询风控设置-股票分控信息", notes = "查询风控设置-股票分控信息")
    @PostMapping({"getSetting.do"})
    public ServerResponse<SiteSetting> getSetting() {
        return ServerResponse.createBySuccess(this.iSiteSettingService.getSiteSetting());
    }


    //风控设置 显示产品配置信息
    @ApiOperation(value = "风控设置-显示产品配置信息", notes = "风控设置-显示产品配置信息")
    @PostMapping({"getProductSetting.do"})
    public ServerResponse<SiteProduct> getProductSetting() {
        return ServerResponse.createBySuccess(this.iSiteProductService.getProductSetting());
    }


}
