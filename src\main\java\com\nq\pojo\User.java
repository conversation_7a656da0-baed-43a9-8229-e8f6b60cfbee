package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName("user")
@Data
@ApiModel(description = "用户信息")
@AllArgsConstructor
@NoArgsConstructor
public class User extends BaseEntity {
    @TableId(type = IdType.AUTO,value = "id")
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "手机号", example = "13800138000")
    private String phone;

    @ApiModelProperty(value = "登录密码", example = "123456")
    private String userPwd;

    @ApiModelProperty(value = "提现密码", example = "123456")
    private String withPwd;

    @ApiModelProperty(value = "昵称", example = "测试用户")
    private String nickName;

    @ApiModelProperty(value = "真实姓名", example = "张三")
    private String realName;

    @ApiModelProperty(value = "身份证号", example = "110101199001011234")
    private String idCard;

    @ApiModelProperty(value = "总资产", example = "10000.00")
    private BigDecimal userAmt;

    @ApiModelProperty(value = "这里是沪深", example = "8000.00")
    private BigDecimal enableAmt;

    @ApiModelProperty(value = "保证金", example = "1000.00")
    private BigDecimal bzjAmt;

    @ApiModelProperty(value = "累计充值金额", example = "10000.00")
    private BigDecimal sumChargeAmt;

    @ApiModelProperty(value = "总提现", example = "5000.00")
    private BigDecimal sumWithdrawAmt;

    @ApiModelProperty(value = "是否锁定(0:未锁定 1:已锁定)", example = "0")
    private Integer isLock;

    @ApiModelProperty(value = "是否登录(0:未登录 1:已登录)", example = "0")
    private Integer isLogin;

    @ApiModelProperty(value = "注册时间", example = "2024-01-01 12:00:00")
    private Date regTime;

    @ApiModelProperty(value = "注册IP", example = "127.0.0.1")
    private String regIp;

    @ApiModelProperty(value = "注册地址", example = "北京市")
    private String regAddress;

    @ApiModelProperty(value = "身份证正面照片", example = "img1.jpg")
    private String img1Key;

    @ApiModelProperty(value = "身份证反面照片", example = "img2.jpg")
    private String img2Key;

    @ApiModelProperty(value = "是否激活(0:未实名 1 提交申请 2:已实名 3 实名失败)", example = "0")
    private Integer isActive;


    @ApiModelProperty(value = "认证信息", example = "认证通过")
    private String authMsg;

    @ApiModelProperty(value = "今日跟单收益", example = "0.00")
    private BigDecimal todayFollowIncome;

    @ApiModelProperty(value = "跟单总收益", example = "0.00")
    private BigDecimal totalFollowIncome;

    @ApiModelProperty(value = "今日队长收益", example = "0.00")
    private BigDecimal todayLeadIncome;

    @ApiModelProperty(value = "队长总收益", example = "0.00")
    private BigDecimal totalLeadIncome;

    @ApiModelProperty(value = "今日跟单数", example = "0")
    private Integer todayFollowNum;

    @ApiModelProperty(value = "总跟单数", example = "0")
    private Integer totalFollowNum;

    @ApiModelProperty(value = "邀请码", example = "ABC123")
    private String yqm;

    @ApiModelProperty(value = "团队总人数", example = "0")
    private Integer lowerNum;

    @ApiModelProperty(value = "团队有效人数", example = "0")
    private Integer lowerEfficientNum;

    @ApiModelProperty(value = "上级id", example = "1")
    private Integer pid;

    @ApiModelProperty(value = "上级ID排列")
    private String pPath;

    @ApiModelProperty(value = "今日是否跟单(0-未跟单，1-跟单)", example = "0")
    private Integer isTodayFollow;

    @ApiModelProperty(value = "今日是否充值(0-未充值，1-充值)", example = "0")
    private Integer isTodayRecharge;

    @ApiModelProperty(value = "是否首冲", example = "0")
    private Integer isRecharge;

    @ApiModelProperty(value = "本周是否跟单", example = "0")
    private Integer isWeekFollow;

    @ApiModelProperty(value = "用户等级", example = "1")
    private Integer level;

    @ApiModelProperty(value = "用户曾最大等级", example = "1")
    private Integer maxLevel;

    @ApiModelProperty(value = "跟单时间", example = "2024-01-01 12:00:00")
    private Date followTime;

    @ApiModelProperty(value = "首冲时间", example = "2024-01-01 12:00:00")
    private Date firstRechargeTime;

    @ApiModelProperty(value = "第一次跟单的结束时间")
    private Date firstFollowStopTime;
}
