<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
	http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
	http://www.springframework.org/schema/mvc
	http://www.springframework.org/schema/mvc/spring-mvc.xsd">

    <!--这里只扫描controller的bean，并关掉默认扫描 use-default-filters="false"-->
    <context:component-scan base-package="com.nq.controller" annotation-config="true" use-default-filters="false">
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
    </context:component-scan>

    <mvc:annotation-driven>
        <mvc:message-converters>
            <bean class="org.springframework.http.converter.StringHttpMessageConverter">
                <property name="supportedMediaTypes">
                    <list>
                        <value>text/plain;charset=UTF-8</value>
                        <value>text/html;charset=UTF-8</value>
                    </list>
                </property>
            </bean>
            <!--springmvc进行自动反序列化时采用的类，jackson-->
            <bean class="org.springframework.http.converter.json.MappingJacksonHttpMessageConverter">
                <!--此处可以配置，例如返回对象时，空值不返回-->
                <property name="supportedMediaTypes">
                    <list>
                        <value>application/json;charset=UTF-8</value>
                    </list>
                </property>
                <!--前端传入对象中不存在的值，忽略-->
                <!--<property name="objectMapper" ref="MyJsonMapper" />-->
            </bean>
        </mvc:message-converters>
    </mvc:annotation-driven>

    <bean id="MyJsonMapper" class="com.nq.common.converter.MyJsonMapper"></bean>


    <!-- 文件上传 -->
    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="maxUploadSize" value="10485760"/> <!-- 10m -->
        <property name="maxInMemorySize" value="4096" />
        <property name="defaultEncoding" value="UTF-8"></property>
    </bean>

    <!--spring mvc拦截器 - 拦截为登陆     -->


    <!-- user interceptor-->
    <mvc:interceptors>
        <mvc:interceptor>
            <mvc:mapping path="/user/**"/>
            <bean class="com.nq.common.interceptor.ApiUserAuthorityInterceptor" />
        </mvc:interceptor>
    </mvc:interceptors>

    <!-- 配置验证码 -->
    <bean id="captchaProducer" class="com.google.code.kaptcha.impl.DefaultKaptcha">
        <property name="config">
            <bean class="com.google.code.kaptcha.util.Config">
                <constructor-arg>
                    <props> <!-- 图片边框 -->
                        <prop key="kaptcha.border">no</prop>
                        <!-- 图片宽度 -->
                        <prop key="kaptcha.image.width">100</prop>
                        <!-- 图片高度 -->
                        <prop key="kaptcha.image.height">45</prop>
                        <!-- 验证码背景颜色渐变，开始颜色 -->
                        <!--<prop key="kaptcha.background.clear.from">248,248,248</prop>-->
                        <!-- 验证码背景颜色渐变，结束颜色 -->
                        <!--<prop key="kaptcha.background.clear.to">248,248,248</prop>-->
                        <!-- 验证码的字符 -->
                        <prop key="kaptcha.textproducer.char.string">0123456789abcdefg</prop>
                        <!-- 验证码字体颜色 -->
                        <prop key="kaptcha.textproducer.font.color">red</prop>
                        <!-- 验证码的效果，水纹 -->
                        <!--<prop key="kaptcha.obscurificator.impl">com.google.code.kaptcha.impl.WaterRipple</prop>-->
                        <!-- 验证码字体大小 -->
                        <prop key="kaptcha.textproducer.font.size">33</prop>
                        <!-- 验证码字数 -->
                        <prop key="kaptcha.textproducer.char.length">4</prop>
                        <!-- 验证码文字间距 -->
                        <prop key="kaptcha.textproducer.char.space">5</prop>
                        <!-- 验证码字体 -->
                        <prop key="kaptcha.textproducer.font.names">
                            宋体,楷体,微软雅黑
                        </prop>
                        <!-- 不加噪声 -->
                        <prop key="kaptcha.noise.impl">com.google.code.kaptcha.impl.NoNoise</prop>
                    </props>
                </constructor-arg>
            </bean>
        </property>
    </bean>

</beans>
