package com.nq.service;


import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.pojo.User;
import com.nq.pojo.UserWithdraw;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;

public interface IUserWithdrawService extends IService<UserWithdraw> {
    String outMoney(String paramString, String with_Pwd, User user, HttpServletRequest paramHttpServletRequest) throws Exception;

    PageInfo<UserWithdraw> findUserWithList(String paramString, int paramInt1, int paramInt2);


    // userId, realName, state, beginTime, endTime, pageNum, pageSiz
    PageInfo<UserWithdraw> listByAdmin(Integer userId, String orderSn, String realName, Integer state, String beginTime, String endTime, int pageNum, int pageSiz);

    //withId, state, authMsg
    void updateState(Integer withId, Integer state, String authMsg);

    //withId, state, authMsg
    void check(Integer withId, Integer state, String authMsg);

    int deleteByUserId(Integer userId);



    JSONObject hasWithdrawMessage();

    List<UserWithdraw> exportByAdmin(Integer userId,String orderSn, String realName, Integer state, String beginTime, String endTime);

    void outMoneyByAdmin(String finalOrderNo, String amt, String type, Integer userId);

    /**
     * 根据日期查询提现金额
     * @param type
     * @return
     */

    BigDecimal countWithdrawAmountByDate(Integer type);
    /**
     * 根据日期查询提现数量通过的
     * @param type
     * @return
     */
    Long countWithdrawNumByDate(Integer type);

    /**
     * 查询提现等待审核
     * @return
     */
    Long countWithdrawPendingCount();
}
