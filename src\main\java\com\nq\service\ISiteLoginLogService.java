package com.nq.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.SiteLoginLog;
import com.nq.pojo.User;

import javax.servlet.http.HttpServletRequest;

public interface ISiteLoginLogService  extends IService<SiteLoginLog> {
  void saveLog(User user,HttpServletRequest request);
 //userId, pageNum, pageSize
  PageInfo<SiteLoginLog> loginList(Integer userId, int pageNum, int pageSize);
  
  int deleteByUserId(Integer paramInteger);

  void del(Integer id, HttpServletRequest request);

    Long countActiveUserByDate(Integer type);
}
