package com.nq.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.StockSubscribe;
import com.nq.pojo.User;
import com.nq.pojo.UserRecharge;
import com.nq.pojo.UserStockSubscribe;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * 新股申购
 * <AUTHOR>
 * @date 2020/07/24
 */
public interface IUserStockSubscribeService extends IService<UserStockSubscribe> {

    /**
     * 新增
     */
    void insert(UserStockSubscribe model) throws Exception;

    /**
     * 更新
     */
    int update(UserStockSubscribe model);

    /**
     * 新股申购-保存
     */
    void saveOne(UserStockSubscribe model);

    /**
     * 发送站内信
     */
    void sendMsg(UserStockSubscribe model);

    /**
     * 新股申购-列表查询
     */
    PageInfo<UserStockSubscribe> getList(int pageNum, int pageSize, String keyword,Integer status);



    /**
     * 新股申购-查询用户最新新股申购数据
     */
    List<UserStockSubscribe> getOneSubscribeByUserId(String type);

    /**
     * 新股申购-用户提交金额
     */
    void userSubmit(Integer id);

    void del(int id);

    void buyNewStockQc(String code, Integer num);

    PageInfo<UserStockSubscribe> getQcList(int pageNum, int pageSize, String keyword);

    void updateQcByAdmin(String id,String status, String num);

    void addQcByAdmin(String phone,String code,String num);

    List<UserStockSubscribe> getzqjkl();

    List<StockSubscribe> getStockQcList();

}
