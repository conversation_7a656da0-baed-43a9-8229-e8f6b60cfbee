/**
 * Copyright (c) 2016-2019 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package com.nq.job.controller;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.job.entity.ScheduleJobEntity;
import com.nq.job.service.ScheduleJobService;
import com.nq.job.utils.ValidatorUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.quartz.CronExpression;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.ParseException;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 定时任务
 *
 * <AUTHOR>
 */
@Api(tags = "定时任务管理")
@RestController
@RequestMapping("/admin/schedule")
public class ScheduleJobController {
	@Resource
	private ScheduleJobService scheduleJobService;

	/**
	 * 定时任务列表
	 */
	@ApiOperation("分页查询定时任务列表")
	@GetMapping("/list.do")
	public ServerResponse<PageInfo<ScheduleJobEntity>> list(
			@ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
			@ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer pageSize,
			@ApiParam("Bean名称") @RequestParam(required = false) String beanName) {
		Map<String, Object> params = new HashMap<>();
		params.put("page", pageNum.toString());
		params.put("limit", pageSize.toString());
		if (beanName != null) {
			params.put("beanName", beanName);
		}

		PageInfo<ScheduleJobEntity> page = scheduleJobService.queryPage(params);
		return ServerResponse.createBySuccess(page);
	}

	/**
	 * 定时任务信息
	 */
	@ApiOperation("获取定时任务详情")
	@GetMapping("/info.do")
	public ServerResponse<ScheduleJobEntity> info(@ApiParam("任务ID") @RequestParam("jobId") String jobId) {
		ScheduleJobEntity schedule = scheduleJobService.getById(jobId);
		return ServerResponse.createBySuccess(schedule);
	}

	/**
	 * 保存定时任务
	 */
	@ApiOperation("新增定时任务")
	@PostMapping("/save.do")
	public ServerResponse<String> save(@ApiParam("定时任务信息") @RequestBody ScheduleJobEntity scheduleJob) {
		ValidatorUtils.validateEntity(scheduleJob);

		// 验证Cron表达式
		try {
			new CronExpression(scheduleJob.getCronExpression());
		} catch (ParseException e) {
			return ServerResponse.createByErrorMsg("Cron表达式无效: " + e.getMessage());
		}

		// 设置唯一的jobId
		if (scheduleJob.getJobId() == null || scheduleJob.getJobId().trim().isEmpty()) {
			scheduleJob.setJobId(UUID.randomUUID().toString());
		}

		scheduleJobService.saveJob(scheduleJob);

		return ServerResponse.createBySuccessMsg("添加成功");
	}

	/**
	 * 修改定时任务
	 */
	@ApiOperation("修改定时任务")
	@PostMapping("/update.do")
	public ServerResponse<String> update(@ApiParam("定时任务信息") @RequestBody ScheduleJobEntity scheduleJob) {
		ValidatorUtils.validateEntity(scheduleJob);

		// 验证Cron表达式
		try {
			new CronExpression(scheduleJob.getCronExpression());
		} catch (ParseException e) {
			return ServerResponse.createByErrorMsg("Cron表达式无效: " + e.getMessage());
		}

		scheduleJobService.update(scheduleJob);

		return ServerResponse.createBySuccessMsg("修改成功");
	}

	/**
	 * 删除定时任务
	 */
	@ApiOperation("删除定时任务")
	@PostMapping("/delete.do")
	public ServerResponse<String> delete(@ApiParam("任务ID数组") @RequestBody String[] jobIds) {
		scheduleJobService.deleteBatch(jobIds);

		return ServerResponse.createBySuccessMsg("删除成功");
	}

	/**
	 * 立即执行任务
	 */
	@ApiOperation("立即执行任务")
	@PostMapping("/run.do")
	public ServerResponse<String> run(@ApiParam("任务ID数组") @RequestBody String[] jobIds) {
		scheduleJobService.run(jobIds);

		return ServerResponse.createBySuccessMsg("执行成功");
	}

	/**
	 * 暂停定时任务
	 */
	@ApiOperation("暂停定时任务")
	@PostMapping("/pause.do")
	public ServerResponse<String> pause(@ApiParam("任务ID数组") @RequestBody String[] jobIds) {
		scheduleJobService.pause(jobIds);

		return ServerResponse.createBySuccessMsg("暂停成功");
	}

	/**
	 * 恢复定时任务
	 */
	@ApiOperation("恢复定时任务")
	@PostMapping("/resume.do")
	public ServerResponse<String> resume(@ApiParam("任务ID数组") @RequestBody String[] jobIds) {
		scheduleJobService.resume(jobIds);

		return ServerResponse.createBySuccessMsg("恢复成功");
	}

}
