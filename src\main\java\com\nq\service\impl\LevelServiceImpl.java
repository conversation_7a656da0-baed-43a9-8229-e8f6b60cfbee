package com.nq.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.dao.LevelMapper;
import com.nq.pojo.Level;
import com.nq.pojo.Mentor;
import com.nq.pojo.MentorApply;
import com.nq.pojo.User;
import com.nq.service.LevelService;
import com.nq.service.MentorService;
import com.nq.utils.BeanCopyUtil;
import com.nq.utils.PageUtil;
import com.nq.vo.admin.LevelVO;
import com.nq.vo.app.AppDayPackageVO;
import com.nq.vo.app.FollowLevelVO;
import com.nq.vo.app.MentorApplyVO;
import com.nq.vo.user.AmountChangeVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class LevelServiceImpl extends ServiceImpl<LevelMapper, Level> implements LevelService {
    @Resource
    private MentorService mentorService;

    @Override
    public PageInfo<LevelVO> pageList(Integer page, Integer size, String levelName, String sortField) {
        PageHelper.startPage(page, size);

        LambdaQueryWrapper<Level> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(levelName), Level::getLevelName, levelName);

        // 按照排序字段排序，默认按排序字段升序，排序字段相同时按ID升序
        wrapper.orderByAsc(Level::getSort)
                .orderByAsc(Level::getId);

        List<Level> levelList = this.list(wrapper);

        List<LevelVO> voList = levelList.stream().map(level -> {
            LevelVO vo = BeanCopyUtil.copyProperties(level, LevelVO.class);
            return vo;
        }).collect(Collectors.toList());


        return PageUtil.buildPageDto(levelList,voList);
    }

    @Override
    public LevelVO getDetailById(Integer id) {
        Level level = this.getById(id);
        if (level == null) {
            return null;
        }

        LevelVO vo = new LevelVO();
        BeanUtils.copyProperties(level, vo);
        return vo;
    }

    @Override
    public List<FollowLevelVO> queryTradeZD() {
        LambdaQueryWrapper<Level> q = new LambdaQueryWrapper<>();
        q.orderByAsc(Level::getSort);
        List<Level> levelList = this.list(q);
        List<FollowLevelVO> voList = levelList.stream().map(this::convertToVO).collect(Collectors.toList());
        return voList;
    }
    private FollowLevelVO convertToVO(Level level) {
        FollowLevelVO followLevelVO = BeanCopyUtil.copyProperties(level, FollowLevelVO.class);
        followLevelVO.setPositionRate(level.getPositionRate().multiply(BigDecimal.TEN).multiply(BigDecimal.TEN));
        followLevelVO.setSalaryRate(level.getSalaryRate().multiply(BigDecimal.TEN).multiply(BigDecimal.TEN));
        return  followLevelVO;
    }
    @Override
    public AppDayPackageVO queryDayPackage(User user, Integer mentorId) {
        AppDayPackageVO vo = new AppDayPackageVO();

        // 检查用户是否为空
        if (user == null) {
            return vo;
        }

        // 获取导师信息
        Mentor mentor = null;
        if (mentorId != null) {
            mentor = mentorService.getById(mentorId);
        }

        // 获取用户等级
        Integer levelInt = user.getLevel();
        if (levelInt == null) {
            // 如果用户没有等级，默认使用等级1
            levelInt = 1;
        }

        // 获取等级信息
        Level level = this.getById(levelInt);
        if (level == null) {
            return vo;
        }

        // 设置返回值
        vo.setMaxAmount(level.getMaxAmount());
        vo.setMinAmount(level.getMinAmount());
        vo.setPositionRate(level.getPositionRate().multiply(BigDecimal.TEN).multiply(BigDecimal.TEN));
        vo.setLevelName(level.getLevelName());
        // 设置导师佣金比例
        if (mentor != null) {
            vo.setSalaryRate(mentor.getSalaryRate().multiply(BigDecimal.TEN).multiply(BigDecimal.TEN));
        }
        return vo;
    }

    @Override
    public List<AppDayPackageVO> queryDayPackageInfo(Integer mentorId) {
        LambdaQueryWrapper<Level> q = new LambdaQueryWrapper<>();
        q.orderByAsc(Level::getSort);
        List<Level> levelList = this.list(q);
        ArrayList<AppDayPackageVO> arr = CollectionUtil.newArrayList();
        for (Level level : levelList) {
            AppDayPackageVO vo = new AppDayPackageVO();
            vo.setLevelName(level.getLevelName());
            vo.setMaxAmount(level.getMaxAmount());
            vo.setMinAmount(level.getMinAmount());
            convertToPackageVO(level,vo);
            arr.add(vo);
        }
        return arr;
    }
    private void convertToPackageVO(Level level,AppDayPackageVO appDayPackageVO) {
        appDayPackageVO.setPositionRate(level.getPositionRate().multiply(BigDecimal.TEN).multiply(BigDecimal.TEN));
        appDayPackageVO.setSalaryRate(level.getSalaryRate().multiply(BigDecimal.TEN).multiply(BigDecimal.TEN));
    }
}