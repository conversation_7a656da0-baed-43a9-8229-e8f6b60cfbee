package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("load_earn")
@ApiModel(description = "队长分佣表")
@AllArgsConstructor
@NoArgsConstructor
public class LoadEarn extends BaseEntity {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "订单号", example = "ORDER20240001")
    private String orderNo;

    @ApiModelProperty(value = "用户ID", example = "1")
    private Integer userId;

    @ApiModelProperty(value = "用户名", example = "zhangsan")
    private String userName;

    @ApiModelProperty(value = "真实姓名", example = "张三")
    private String realName;

    @ApiModelProperty(value = "等级", example = "1")
    private Integer level;

    @ApiModelProperty(value = "工资比例", example = "0.10")
    private BigDecimal salaryRate;

    @ApiModelProperty(value = "总投顾金额挣钱金额", example = "10000.00")
    private BigDecimal totalFollowEarnAmount;

    @ApiModelProperty(value = "分佣区间", example = "2024-01-01 00:00:00")
    private Date startTime;

    @ApiModelProperty(value = "分佣结束时间", example = "2024-01-31 23:59:59")
    private Date endTime;

    @ApiModelProperty(value = "分佣金额", example = "1000.00")
    private BigDecimal amount;

    @ApiModelProperty(value = "创建时间", example = "2024-01-01 12:00:00")
    private Date createTime;
} 