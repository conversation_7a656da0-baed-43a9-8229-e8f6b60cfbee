package com.nq.controller.admin;


import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.service.IUserPositionService;
import com.nq.vo.position.UserPositionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.http.util.TextUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;


@RestController
@RequestMapping({"/admin/position/"})
@Api(tags = "后台-持仓管理")
public class AdminPositionController {
    private static final Logger log = LoggerFactory.getLogger(AdminPositionController.class);

    @Resource
    private IUserPositionService iUserPositionService;

    //分页查询持仓管理 融资持仓单信息/融资平仓单信息及模糊查询
    @PostMapping({"list.do"})
    @ApiOperation("分页查询持仓管理信息")
    public ServerResponse<PageInfo<UserPositionVO>> list(
                                                          @RequestParam(value = "userId", required = false) Integer userId,
                                                          @RequestParam(value = "stockCode", required = false) String stockCode,
                                                          @RequestParam(value = "stockSpell", required = false) String stockSpell,
                                                          @RequestParam(value = "state", required = false) Integer state,
                                                          @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                                          @RequestParam(value = "pageSize", defaultValue = "12") int pageSize) {
        //userId stockCode, stockSpell, state, pageNum, pageSize
        PageInfo<UserPositionVO> adminPositionVOPageInfo = this.iUserPositionService.findPositionByCodeByState(userId, stockCode, stockSpell,state, pageNum, pageSize, false);
        return ServerResponse.createBySuccess(adminPositionVOPageInfo);
    }

    //持仓管理 强制平仓操作
    @PostMapping({"sell.do"})
    @ApiOperation("强制平仓操作")
    public ServerResponse<String> sell(String positionSn, String availablePositionSn) throws Exception {
        try {
            if (!TextUtils.isBlank(availablePositionSn)) {
                String[] list = availablePositionSn.split(",");
                for (String position : list) {
                    this.iUserPositionService.sell(position, 0);
                }
            } else {
                this.iUserPositionService.sell(positionSn, 0);
            }
        } catch (Exception e) {
            log.error("强制平仓 异常信息 = {}", e);
        }
        return ServerResponse.createBySuccess();
    }

    //锁仓/解仓操作
    @PostMapping({"lock.do"})
    @ApiOperation("锁仓/解仓操作")
    public ServerResponse<String> lock(@RequestParam("positionId") Integer positionId, @RequestParam("state") Integer state, @RequestParam(value = "lockMsg", required = false) String lockMsg) {
        this.iUserPositionService.lock(positionId, state, lockMsg);
        return ServerResponse.createBySuccess();
    }


    //创建持仓单
    @PostMapping({"create.do"})
    @ApiOperation("创建持仓单")
    public ServerResponse<String> create(@RequestParam("userId") Integer userId, @RequestParam("stockCode") String stockCode, @RequestParam("buyPrice") BigDecimal buyPrice, @RequestParam("buyNum") Integer buyNum, @RequestParam("buyTime") String buyTime) {
        this.iUserPositionService.create(userId, stockCode, buyPrice, buyNum, null, buyTime);
        return ServerResponse.createBySuccess();
    }

    //新股转持仓
    @PostMapping({"addUserPosition.do"})
    @ApiOperation("新股转持仓")
    public ServerResponse<String> newStockToPosition(@RequestParam("id") Integer id) {
        this.iUserPositionService.newStockToPosition(id);
        return ServerResponse.createBySuccess();
    }

}

