package com.nq.controller.admin;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.dto.common.CommonPage;
import com.nq.pojo.Activity;
import com.nq.service.ActivityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

import javax.annotation.Resource;

@RestController
@RequestMapping({ "/admin/activity/" })
@Api(tags = { "后台-活动管理" })
public class AdminActivityController {

    @Resource
    private ActivityService activityService;

    @PostMapping({ "list.do" })
    @ApiOperation(value = "活动列表", notes = "活动列表")
    public ServerResponse<PageInfo<Activity>> list(@RequestBody Map<String, Object> params) {
        try {
            // 打印接收到的参数，用于调试
            System.out.println("查询活动参数：" + params);

            // 创建活动对象
            Activity activity = new Activity();

            // 设置查询条件
            if (params.containsKey("name")) {
                activity.setName((String) params.get("name"));
            }
            if (params.containsKey("type")) {
                activity.setType((Integer) params.get("type"));
            }

            // 创建分页对象
            CommonPage commonPage = new CommonPage();
            if (params.containsKey("pageNum")) {
                commonPage.setPageNum((Integer) params.get("pageNum"));
            }
            if (params.containsKey("pageSize")) {
                commonPage.setPageSize((Integer) params.get("pageSize"));
            }

            // 执行查询
            PageInfo<Activity> page = activityService.listByAdmin(activity, commonPage);
            return ServerResponse.createBySuccess(page);
        } catch (Exception e) {
            e.printStackTrace();
            return ServerResponse.createByErrorMsg("查询活动列表失败：" + e.getMessage());
        }
    }

    // 查询用户信息是否存在
    @PostMapping({ "detail.do" })
    public ServerResponse<Activity> detail(Integer id) {
        Activity activity = this.activityService.detail(id);
        return ServerResponse.createBySuccess(activity);
    }

    // 删除活动
    @PostMapping({ "delete.do" })
    @ApiOperation(value = "删除活动", notes = "删除活动")
    public ServerResponse<String> delete(@RequestBody Map<String, Integer> params) {
        Integer id = params.get("id");
        try {
            // 打印接收到的参数，用于调试
            System.out.println("删除活动ID：" + id);

            // 验证必填字段
            if (id == null) {
                return ServerResponse.createByErrorMsg("活动ID不能为空");
            }

            // 查询活动是否存在
            Activity activity = this.activityService.detail(id);
            if (activity == null) {
                return ServerResponse.createByErrorMsg("活动不存在");
            }

            // 执行删除操作
            boolean success = this.activityService.deleteActivity(id);
            if (success) {
                return ServerResponse.createBySuccessMsg("删除活动成功");
            } else {
                return ServerResponse.createByErrorMsg("删除活动失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ServerResponse.createByErrorMsg("删除活动失败：" + e.getMessage());
        }
    }

    // 更新活动
    @PostMapping({ "update.do" })
    @ApiOperation(value = "更新活动", notes = "更新活动")
    public ServerResponse<String> update(@RequestBody Activity activity) {
        try {
            // 打印接收到的参数，用于调试
            System.out.println("更新活动参数：" + activity);

            // 验证必填字段
            if (activity.getActivityId() == null) {
                return ServerResponse.createByErrorMsg("活动ID不能为空");
            }

            // 设置更新时间
            activity.setUpdateTime(new Date());

            boolean success = this.activityService.updateById(activity);
            if (success) {
                return ServerResponse.createBySuccessMsg("更新活动成功");
            } else {
                return ServerResponse.createByErrorMsg("更新活动失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ServerResponse.createByErrorMsg("更新活动失败：" + e.getMessage());
        }
    }

    // 新增活动
    @PostMapping({ "add.do" })
    @ApiOperation(value = "新增活动", notes = "新增活动")
    public ServerResponse<String> add(@RequestBody Activity activity) {

        try {
            // 打印接收到的参数，用于调试
            System.out.println("接收到的活动参数：" + activity);

            // 验证必填字段
            if (activity.getType() == null) {
                return ServerResponse.createByErrorMsg("任务类型不能为空");
            }
            if (activity.getName() == null || activity.getName().trim().isEmpty()) {
                return ServerResponse.createByErrorMsg("任务名称不能为空");
            }
            if (activity.getAward() == null) {
                return ServerResponse.createByErrorMsg("奖励不能为空");
            }
            if (activity.getTarget() == null) {
                return ServerResponse.createByErrorMsg("任务目标不能为空");
            }

            // 设置默认值
            if (activity.getCreateTime() == null) {
                activity.setCreateTime(new Date());
            }

            boolean success = this.activityService.save(activity);
            if (success) {
                return ServerResponse.createBySuccessMsg("新增活动成功");
            } else {
                return ServerResponse.createByErrorMsg("新增活动失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ServerResponse.createByErrorMsg("新增活动失败：" + e.getMessage());
        }
    }
}
