package com.nq.controller.agent;


import com.github.pagehelper.PageInfo;

import com.nq.common.ServerResponse;

import com.nq.pojo.UserRecharge;
import com.nq.service.IUserRechargeService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;

import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Controller;

import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;

import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping({"/agent/recharge/"})
public class AgentRechargeController {

    private static final Logger log = LoggerFactory.getLogger(AgentRechargeController.class);



}
