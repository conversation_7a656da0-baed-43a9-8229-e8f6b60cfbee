package com.nq.controller.agent;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.UserRecharge;
import com.nq.service.IUserRechargeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;


@RestController
@Api(tags = {"代理后台-充值管理"})
@RequestMapping({"/agent/recharge/"})
public class AgentRechargeController {

    private static final Logger log = LoggerFactory.getLogger(AgentRechargeController.class);
    @Resource
    private IUserRechargeService iUserRechargeService;

    //分页查询资金管理 充值列表信息及模糊查询
    @PostMapping({"list.do"})
    @ApiOperation("分页查询充值列表信息")
    public ServerResponse<PageInfo<UserRecharge>> list(@RequestParam(value = "userId", required = false) Integer userId,
                                                       @RequestParam(value = "nickName", required = false) String nickName,
                                                       @RequestParam(value = "state", required = false) Integer state,
                                                       @RequestParam(value = "orderSn", required = false) String orderSn,
                                                       @RequestParam(value = "beginTime", required = false) String beginTime,
                                                       @RequestParam(value = "endTime", required = false) String endTime,
                                                       @RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        PageInfo<UserRecharge> userRechargePageInfo = this.iUserRechargeService.listByAdmin(userId,orderSn,nickName, state, beginTime, endTime, pageNum, pageSize, true );
        return ServerResponse.createBySuccess(userRechargePageInfo);
    }

    /**
     * 分页查询资金管理 充值列表信息及模糊查询 导出
     *
     * @param userId
     * @param nickName
     * @param state
     * @param beginTime
     * @param endTime
     * @param response
     */
    @PostMapping({"export.do"})
    @ApiOperation("导出充值列表信息")
    public void export(@RequestParam(value = "userId", required = false) Integer userId,
                       @RequestParam(value = "orderSn", required = false) String orderSn,
                       @RequestParam(value = "nickName", required = false) String nickName,
                       @RequestParam(value = "state", required = false) Integer state, @RequestParam(value = "beginTime", required = false) String beginTime, @RequestParam(value = "endTime", required = false) String endTime, HttpServletResponse response) {
        List<UserRecharge> userRechargeList = this.iUserRechargeService.exportByAdmin(userId,orderSn, nickName, state, beginTime, endTime, true);
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("充值导出", "充值数据"),
                UserRecharge.class, userRechargeList);
        try {
            ServletOutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            log.error("导出充值数据失败", e);

        }
    }

}
