package com.nq.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.dao.LoadEarnMapper;
import com.nq.dto.common.CommonPage;
import com.nq.pojo.Level;
import com.nq.pojo.LoadEarn;
import com.nq.pojo.User;
import com.nq.service.IUserService;
import com.nq.service.LevelService;
import com.nq.service.LoadEarnService;
import com.nq.utils.DateUtils;
import com.nq.utils.PageUtil;
import com.nq.vo.user.LoadEarnVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class LoadEarnServiceImpl extends ServiceImpl<LoadEarnMapper, LoadEarn> implements LoadEarnService {
    @Resource
    private IUserService userService;
    @Resource
    private LevelService levelService;
    @Override
    public PageInfo<LoadEarnVO> getAgentSalaryPage(User user, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        LambdaQueryWrapper<LoadEarn> q = new LambdaQueryWrapper<>();
        q.eq(LoadEarn::getUserId, user.getId());
        q.orderByDesc(LoadEarn::getId);
        List<LoadEarn> list = this.list(q);
        List<LoadEarnVO> voList = list.stream().map(this::convertToVO).collect(Collectors.toList());
        return PageUtil.buildPageDto(list, voList);
    }

    @Override
    public PageInfo<LoadEarnVO> pageList(LoadEarn loadEarn, CommonPage commonPage) {
        LambdaQueryWrapper<LoadEarn> q = new LambdaQueryWrapper<>();
        this.buildQuery(loadEarn, q);
        PageUtil.startPage(commonPage.getPageNum(), commonPage.getPageSize());
        List<LoadEarn> list = this.list(q);
        List<LoadEarnVO> voList = list.stream().map(this::convertToVO).collect(Collectors.toList());
        return PageUtil.buildPageDto(list, voList);
    }

    private void buildQuery(LoadEarn loadEarn, LambdaQueryWrapper<LoadEarn> q) {
        List<Integer> userIdsToFilter = CollUtil.newArrayList();
        String keyword = loadEarn.getSearchValue();
        if (StrUtil.isNotBlank(keyword)) {
            LambdaQueryWrapper<User> userWrapper = new LambdaQueryWrapper<>();
            userWrapper.like(User::getPhone, keyword)
                    .or().like(User::getRealName, keyword)
                    .or().like(User::getNickName, keyword);

            List<User> matchingUsers = userService.list(userWrapper);
            userIdsToFilter = matchingUsers.stream().map(User::getId).collect(Collectors.toList());
        }
        q.in(!userIdsToFilter.isEmpty(), LoadEarn::getUserId, userIdsToFilter);
    }

    /**
     * 将Follow实体转换为VO
     */
    private LoadEarnVO convertToVO(LoadEarn loadEarn) {
        LoadEarnVO vo = new LoadEarnVO();
        BeanUtils.copyProperties(loadEarn, vo);
        Date startTime = loadEarn.getStartTime();
        Date endTime = loadEarn.getEndTime();
        Level level = levelService.getById(loadEarn.getLevel());
        if(level != null){
            vo.setLevelName(level.getLevelName());
        }
        vo.setPeriod(DateUtils.format(startTime, DateUtils.DATE_PATTERN)+"--"+DateUtils.format(endTime,  DateUtils.DATE_PATTERN));
        return vo;
    }
}