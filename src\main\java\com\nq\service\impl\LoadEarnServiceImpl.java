package com.nq.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.dao.LoadEarnMapper;
import com.nq.pojo.LoadEarn;
import com.nq.pojo.User;
import com.nq.service.LoadEarnService;
import com.nq.utils.DateUtils;
import com.nq.utils.PageUtil;
import com.nq.vo.user.LoadEarnVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class LoadEarnServiceImpl extends ServiceImpl<LoadEarnMapper, LoadEarn> implements LoadEarnService {
    @Override
    public PageInfo<LoadEarnVO> getAgentSalaryPage(User user, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        LambdaQueryWrapper<LoadEarn> q = new LambdaQueryWrapper<>();
        q.eq(LoadEarn::getUserId, user.getId());
        q.orderByDesc(LoadEarn::getId);
        List<LoadEarn> list = this.list(q);
        List<LoadEarnVO> voList = list.stream().map(this::convertToVO).collect(Collectors.toList());
        return PageUtil.buildPageDto(list, voList);
    }

    /**
     * 将Follow实体转换为VO
     */
    private LoadEarnVO convertToVO(LoadEarn loadEarn) {
        LoadEarnVO vo = new LoadEarnVO();

        BeanUtils.copyProperties(loadEarn, vo);
        Date startTime = loadEarn.getStartTime();
        Date endTime = loadEarn.getEndTime();
        vo.setPeriod(DateUtils.format(startTime, DateUtils.DATE_PATTERN)+"-"+DateUtils.format(endTime,  DateUtils.DATE_PATTERN));
        return vo;
    }
}