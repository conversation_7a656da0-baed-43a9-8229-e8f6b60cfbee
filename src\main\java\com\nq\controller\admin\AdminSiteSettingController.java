 package com.nq.controller.admin;


 import com.nq.common.ServerResponse;
 import com.nq.pojo.SiteSetting;
 import com.nq.service.ISiteSettingService;
 import io.swagger.annotations.Api;
 import io.swagger.annotations.ApiOperation;
 import org.slf4j.Logger;
 import org.slf4j.LoggerFactory;
 import org.springframework.web.bind.annotation.PostMapping;
 import org.springframework.web.bind.annotation.RequestMapping;
 import org.springframework.web.bind.annotation.RestController;

 import javax.annotation.Resource;


 @RestController
 @RequestMapping({"/admin/set/"})
 @Api(tags = {"后台-站点设置"})
 public class AdminSiteSettingController {
     private static final Logger log = LoggerFactory.getLogger(AdminSiteSettingController.class);

     @Resource
     private ISiteSettingService iSiteSettingService;

     //修改风控设置 股票风控信息
     @ApiOperation(value = "修改风控设置-股票风控信息", notes = "修改风控设置-股票风控信息")
     @PostMapping({"update.do"})
     public ServerResponse<String> update(SiteSetting siteSetting) {
          this.iSiteSettingService.update(siteSetting);
          return ServerResponse.createBySuccess();
     }
 }