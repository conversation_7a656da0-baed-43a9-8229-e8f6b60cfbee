package com.nq.controller.admin;


import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.StockSubscribe;
import com.nq.pojo.UserStockSubscribe;
import com.nq.service.IStockSubscribeService;
import com.nq.service.IUserStockSubscribeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping({"/admin/subscribe/"})
@Api(tags = {"后台-新股申购管理"})
public class AdminStockSubscribeController {
    private static final Logger log = LoggerFactory.getLogger(AdminStockSubscribeController.class);
    @Resource
    private IUserStockSubscribeService iUserStockSubscribeService;
    @Resource
    private IStockSubscribeService iStockSubscribeService;

    /**
     * @param pageNum
     * @param pageSize
     * @return
     */
    @PostMapping({"list.do"})
    @ApiOperation("分页查询新股信息")
    public ServerResponse<PageInfo<StockSubscribe>> list(@RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "10") int pageSize, @RequestParam(value = "name", required = false) String name, @RequestParam(value = "code", required = false) String code, @RequestParam(value = "zt", required = false) Integer zt, @RequestParam(value = "isLock", required = false) Integer isLock, @RequestParam(value = "type", required = false) Integer type) {
        PageInfo<StockSubscribe> EasyAdmin = this.iStockSubscribeService.listByAdmin(pageNum, pageSize, name, code, zt, isLock, type);
        return ServerResponse.createBySuccess(EasyAdmin);
    }

    /**
     * @Description: 新增新股
     * @Param:
     * @return:
     * @Author: tf
     * @Date: 2022/10/25
     */
    @PostMapping({"add.do"})
    @ApiOperation("新增新股")
    public ServerResponse<String> add(StockSubscribe model) {
        this.iStockSubscribeService.add(model);
        return ServerResponse.createBySuccess();
    }

    /**
     * @Description: 修改新股
     * @Param:
     * @return:
     * @Author: tf
     * @Date: 2022/10/25
     */
    @PostMapping({"update.do"})
    @ApiOperation("修改新股")
    public ServerResponse<String> update(StockSubscribe model) {
        this.iStockSubscribeService.update(model);
        return ServerResponse.createBySuccess();

    }

    /**
     * @Description: 删除新股
     * @Param:
     * @return:
     * @Author: tf
     * @Date: 2022/10/25
     */
    @RequestMapping({"del.do"})
    @ApiOperation("删除新股")
    public ServerResponse<String> del(@RequestParam("id") Integer id) {
        this.iStockSubscribeService.del(id);
        return ServerResponse.createBySuccess();
    }

    //申购信息列表查询
    @PostMapping({"getStockSubscribeList.do"})
    @ApiOperation("分页查询新股申购信息")
    public ServerResponse<PageInfo<UserStockSubscribe>> getStockSubscribeList(@RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "12") int pageSize, @RequestParam(value = "keyword", defaultValue = "") String keyword, @RequestParam(value = "status", required = false) Integer status) {
        PageInfo<UserStockSubscribe> pageInfo = this.iUserStockSubscribeService.getList(pageNum, pageSize, keyword, status, false);
        return ServerResponse.createBySuccess(pageInfo);
    }

    //申购信息-添加 修改
    @PostMapping({"saveStockSubscribe.do"})
    @ApiOperation("添加/修改新股申购信息")
    public ServerResponse<String> saveStockSubscribe(UserStockSubscribe model) {
        this.iUserStockSubscribeService.saveOne(model);
        return ServerResponse.createBySuccess();
    }

    //发送站内信
    @PostMapping({"sendMsg.do"})
    @ApiOperation("发送站内信")
    public ServerResponse<String> sendMsg(UserStockSubscribe model) {
        this.iUserStockSubscribeService.sendMsg(model);
        return ServerResponse.createBySuccess();
    }


    //新股申购-删除
    @PostMapping({"delStockSubscribe.do"})
    @ApiOperation("删除新股申购信息")
    public ServerResponse<String> delStockSubscribe(@RequestParam("id") int id) {
         this.iUserStockSubscribeService.del(id);
         return ServerResponse.createBySuccess();
    }

    //新股抢筹 股票列表
    @PostMapping({"getStockQcList.do"})
    @ApiOperation("获取新股抢筹股票列表")
    public ServerResponse<List<StockSubscribe>> getStockQcList() {
        List<StockSubscribe> stockQcList = this.iUserStockSubscribeService.getStockQcList();
        return ServerResponse.createBySuccess(stockQcList);
    }

    //新股抢筹 记录列表
    @PostMapping({"getStockSubscribeQcListByAdmin.do"})
    @ApiOperation("分页查询新股抢筹记录列表")
    public ServerResponse<PageInfo<UserStockSubscribe>> getStockSubscribeQcList(@RequestParam(value = "pageNum", defaultValue = "1") int pageNum, @RequestParam(value = "pageSize", defaultValue = "10") int pageSize, @RequestParam(value = "keyword", required = false) String keyword) {
        PageInfo<UserStockSubscribe> qcList = this.iUserStockSubscribeService.getQcList(pageNum, pageSize, keyword);
        return ServerResponse.createBySuccess(qcList);
    }

    //新股抢筹-审核（修改）
    @PostMapping({"updateStockSubscribeQcByAdmin.do"})
    @ApiOperation("审核新股抢筹记录")
    public ServerResponse<String> updateStockSubscribeQc(@RequestParam(value = "id") String id, @RequestParam(value = "status") String status, @RequestParam(value = "num") String num) {
         this.iUserStockSubscribeService.updateQcByAdmin(id, status, num);
         return ServerResponse.createBySuccess();
    }

    //新股抢筹-记录添加
    @PostMapping({"addStockSubscribeQcByAdmin.do"})
    @ApiOperation("添加新股抢筹记录")
    public ServerResponse<String> addStockSubscribeQc(@RequestParam("userPhone") String phone, @RequestParam("newCode") String code, @RequestParam("num") String num) {
         this.iUserStockSubscribeService.addQcByAdmin(phone, code, num);
         return ServerResponse.createBySuccess();
    }

}
