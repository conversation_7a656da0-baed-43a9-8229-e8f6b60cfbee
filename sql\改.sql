DELETE from QRTZ_CRON_TRIGGERS;
DELETE from QRTZ_TRIGGERS;
DELETE from QRTZ_JOB_DETAILS;


ALTER TABLE `stock`.`level`
    CHANGE COLUMN `commission_rate` `position_rate` decimal(10, 2) NOT NULL COMMENT '仓位比例(%)' AFTER `max_amount`;

ALTER TABLE `stock`.`follow_detail`
    CHANGE COLUMN `commission_rate` `position_rate` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '仓位比例' AFTER `amount`,
    CHANGE COLUMN `brokerage_rate` `salary_rate` decimal(10, 2) NOT NULL COMMENT '工资比例' AFTER `min_amount`,
    CH<PERSON><PERSON> COLUMN `brokerage` `salary` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '工资' AFTER `salary_rate`;
ALTER TABLE `stock`.`follow`
    CHANGE COLUMN `commission_rate` `position_rate` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '仓位比例' AFTER `amount`;

ALTER TABLE `stock`.`follow`
    CHANGE COLUMN `brokerage_rate` `salary_rate` decimal(10, 2) NULL DEFAULT NULL COMMENT '佣金比例' AFTER `commission_rate`;