package com.nq.utils;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.beans.PropertyDescriptor;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public class BeanCopyUtil {


    public static <T> T toBean(Map<String, Object> sourceMap, Class<T> targetClass) {
        try {
            T target = targetClass.getDeclaredConstructor().newInstance();
            BeanWrapper beanWrapper = new BeanWrapperImpl(target);
            PropertyDescriptor[] propertyDescriptors = beanWrapper.getPropertyDescriptors();

            for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
                String propertyName = propertyDescriptor.getName();
                if (sourceMap.containsKey(propertyName)) {
                    Object value = sourceMap.get(propertyName);
                    beanWrapper.setPropertyValue(propertyName, value);
                }
            }
            return target;
        } catch (Exception e) {
            // 处理实例化异常或属性设置异常
            e.printStackTrace();
            return null;
        }
    }

    public static <S, T> T copyProperties(S source, Class<T> targetClass) {
        T target = null;
        try {
            target = targetClass.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(source, target);
        } catch (Exception e) {
            // 处理实例化异常或属性复制异常
            e.printStackTrace();
        }

        return target;
    }


    public static <S, T> List<T> copyToList(List<S> sourceList, Class<T> targetClass) {
        List<T> targetList = new ArrayList<>();
        for (S source : sourceList) {
            try {
                T target = targetClass.getDeclaredConstructor().newInstance();
                BeanUtils.copyProperties(source, target);
                targetList.add(target);
            } catch (Exception e) {
                // 处理实例化异常或属性复制异常
                e.printStackTrace();
            }
        }
        return targetList;
    }

    public static <S, T> List<T> copyToList(Collection<S> sourceList, Class<T> targetClass) {
        List<T> targetList = new ArrayList<>();
        for (S source : sourceList) {
            try {
                T target = targetClass.getDeclaredConstructor().newInstance();
                BeanUtils.copyProperties(source, target);
                targetList.add(target);
            } catch (Exception e) {
                // 处理实例化异常或属性复制异常
                e.printStackTrace();
            }
        }
        return targetList;
    }


}
