/**
 * Copyright (c) 2016-2019 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package com.nq.utils;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;

/**
 * 日期处理
 *
 * <AUTHOR> <PERSON>@gmail.com
 */
public class DateUtils {
    /** 时间格式(yyyy-MM-dd) */
    public final static String DATE_PATTERN = "yyyy-MM-dd";
    /** 时间格式(yyyy-MM-dd HH:mm:ss) */
    public final static String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static  Date getStartTime(String timeStr) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        timeStr += " 00:00:00";
        try {
            return format.parse(timeStr);
        } catch (Exception e) {
            return null;
        }
    }
    public  static Date getEndTime(String timeStr) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        timeStr += " 23:59:59";
        try {
            return format.parse(timeStr);
        } catch (Exception e) {
            return null;
        }
    }

    public static Date getYYYMMDDEndTime(String timeStr) {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        try {
            // 1. Parse the date string (gets the start of the day 00:00:00)
            Date startDate = format.parse(timeStr);

            // 2. Use Calendar to set the time to the end of the day
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 999); // Optional: set milliseconds for precision

            // 3. Return the modified Date object
            return calendar.getTime();

        } catch (ParseException e) {
            // Log the error if necessary
            // log.error("Failed to parse date string: {}", timeStr, e);
            return null;
        } catch (Exception e) {
            // Catch other potential exceptions
            // log.error("An unexpected error occurred while processing date string: {}", timeStr, e);
            return null;
        }
    }

    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     * @param date  日期
     * @return  返回yyyy-MM-dd格式日期
     */
    public static String format(Date date) {
        return format(date, DATE_PATTERN);
    }

    public  static Date formatStart(Date date) {
        String yyymmmdd = format(date, DATE_PATTERN);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        yyymmmdd += " 00:00:00";
        try {
            return format.parse(yyymmmdd);
        } catch (Exception e) {
            return null;
        }
    }

    public  static Date formatEnd(Date date) {
        String yyymmmdd = format(date, DATE_PATTERN);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        yyymmmdd += " 23:59:59";
        try {
            return format.parse(yyymmmdd);
        } catch (Exception e) {
            return null;
        }
    }
    /**
     /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     * @param date  日期
     * @param pattern  格式，如：DateUtils.DATE_TIME_PATTERN
     * @return  返回yyyy-MM-dd格式日期
     */
    public static String format(Date date, String pattern) {
        if(date != null){
            SimpleDateFormat df = new SimpleDateFormat(pattern);
            return df.format(date);
        }
        return null;
    }

    /**
     * 字符串转换成日期
     * @param strDate 日期字符串
     * @param pattern 日期的格式，如：DateUtils.DATE_TIME_PATTERN
     */
    public static Date stringToDate(String strDate, String pattern) {
        if (StringUtils.isBlank(strDate)){
            return null;
        }

        DateTimeFormatter fmt = DateTimeFormat.forPattern(pattern);
        return fmt.parseLocalDateTime(strDate).toDate();
    }

    /**
     * 根据周数，获取开始日期、结束日期
     * @param week  周期  0本周，-1上周，-2上上周，1下周，2下下周
     * @return  返回date[0]开始日期、date[1]结束日期
     */
    public static Date[] getWeekStartAndEnd(int week) {
        DateTime dateTime = new DateTime();
        LocalDate date = new LocalDate(dateTime.plusWeeks(week));

        date = date.dayOfWeek().withMinimumValue();
        Date beginDate = date.toDate();
        Date endDate = date.plusDays(6).toDate();
        return new Date[]{beginDate, endDate};
    }

    /**
     * 对日期的【秒】进行加/减
     *
     * @param date 日期
     * @param seconds 秒数，负数为减
     * @return 加/减几秒后的日期
     */
    public static Date addDateSeconds(Date date, int seconds) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusSeconds(seconds).toDate();
    }

    /**
     * 对日期的【分钟】进行加/减
     *
     * @param date 日期
     * @param minutes 分钟数，负数为减
     * @return 加/减几分钟后的日期
     */
    public static Date addDateMinutes(Date date, int minutes) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusMinutes(minutes).toDate();
    }

    /**
     * 对日期的【小时】进行加/减
     *
     * @param date 日期
     * @param hours 小时数，负数为减
     * @return 加/减几小时后的日期
     */
    public static Date addDateHours(Date date, int hours) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusHours(hours).toDate();
    }

    /**
     * 对日期的【天】进行加/减
     *
     * @param date 日期
     * @param days 天数，负数为减
     * @return 加/减几天后的日期
     */
    public static Date addDateDays(Date date, int days) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusDays(days).toDate();
    }
    /**
     * 对日期的【天】进行加/减
     *
     * @param date 日期
     * @param days 天数，负数为减
     * @return 加/减几天后的日期
     */
    public static Date addDateDaysStart(Date date, int days) {
        DateTime dateTime = new DateTime(date);
        Date day = dateTime.plusDays(days).toDate();
        return formatStart(day);
    }

    /**
     * 对日期的【周】进行加/减
     *
     * @param date 日期
     * @param weeks 周数，负数为减
     * @return 加/减几周后的日期
     */
    public static Date addDateWeeks(Date date, int weeks) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusWeeks(weeks).toDate();
    }

    /**
     * 对日期的【月】进行加/减
     *
     * @param date 日期
     * @param months 月数，负数为减
     * @return 加/减几月后的日期
     */
    public static Date addDateMonths(Date date, int months) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusMonths(months).toDate();
    }

    /**
     * 对日期的【年】进行加/减
     *
     * @param date 日期
     * @param years 年数，负数为减
     * @return 加/减几年后的日期
     */
    public static Date addDateYears(Date date, int years) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusYears(years).toDate();
    }

    /**
     * 判断今天是否为周日
     * @return true-是周日，false-不是周日
     */
    public static boolean isSunday() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY;
    }
    /**   0 全部 1 今日 2 昨日 3 近七日 4 近一个月
     * 转换时间
     *
     * @param timeSection
     */
    public static HashMap<String, Date> changeTimeSection(Integer timeSection) {
        HashMap<String, Date> map = new HashMap<>();
        Date startTime = null;
        if (timeSection == null) {
            timeSection = 0;
        }
        //结束时间都是最后
        Date endTime = DateUtils.getEndTimeByDate(new Date());
        switch (timeSection) {

            case 1:
                // 今天
                startTime = DateUtils.getStartTimeByDate(new Date());
                break;
            case 2:
                // 昨日
                startTime = DateUtils.getStartTimeByDate(DateUtils.addDateDays(new Date(), -1));
                endTime = DateUtils.getEndTimeByDate(DateUtils.addDateDays(new Date(), -1));
                break;
            case 3:
                // 近七日
                startTime = DateUtils.getStartTimeByDate(DateUtils.addDateDays(new Date(),-7));
                break;
            case 4:
                // 近一个月
                startTime = DateUtils.getStartTimeByDate(DateUtils.addMouth(-1));
                break;
            default:
                break;
        }

        map.put("startTime", startTime);
        map.put("endTime", endTime);
        return map;
    }
    /**
     * 给一个日期返回结束
     *
     * @return {@link String[]}
     */
    public static Date getEndTimeByDate(Date date) {
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String day = DateFormatUtils.format(date, "yyyy-MM-dd");
            String end = day + " 23:59:59";
            return simpleDateFormat.parse(end);
        } catch (Exception e) {

        }
        return new Date();
    }

    /**
     * 给一个日期返回开始时间
     *
     * @return {@link String[]}
     */
    public static Date getStartTimeByDate(Date date) {
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String day = DateFormatUtils.format(date, "yyyy-MM-dd");
            String star = day + " 00:00:00";
            return simpleDateFormat.parse(star);
        } catch (Exception e) {

        }
        return new Date();
    }
    /**
     * 根据 根据给定的 数字 算出 加减月份
     *
     * @return {@link String[]}
     */
    public static Date addMouth(Integer mouth) {
        try {
            Calendar calendar = Calendar.getInstance(); // 获取当前日期
            calendar.add(Calendar.MONTH, mouth); // 将月份减去1个月
            // 输出一个月前的日期
            return calendar.getTime();
        } catch (Exception e) {

        }
        return new Date();
    }
    /**
     * 对日期的【天】进行加/减
     *
     * @param days 天数，负数为减
     * @return 加/减几天后的日期
     */

}
