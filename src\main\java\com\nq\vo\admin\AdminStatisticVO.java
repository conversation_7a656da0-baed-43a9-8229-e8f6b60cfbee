package com.nq.vo.admin;

import lombok.Data;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@Data
@ApiModel("首页统计数据VO")
public class AdminStatisticVO {
    @ApiModelProperty("总用户数")
    private Long totalUserCount;
    @ApiModelProperty("今日新增用户数")
    private Long todayNewUserCount;
    @ApiModelProperty("昨日新增用户数")
    private Long yesterdayNewUserCount;
    @ApiModelProperty("近七日新增用户数")
    private Long last7DaysNewUserCount;
    @ApiModelProperty("本月新增用户数")
    private Long monthNewUserCount;
    @ApiModelProperty("今日申请跟单数")
    private Long todayApplyFollowCount;
    @ApiModelProperty("今日申请跟单金额")
    private BigDecimal todayApplyFollowAmount;
    @ApiModelProperty("昨日申请跟单数")
    private Long yesterdayApplyFollowCount;
    @ApiModelProperty("昨日申请跟单金额")
    private BigDecimal yesterdayApplyFollowAmount;
    @ApiModelProperty("总提现金额")
    private BigDecimal totalWithdrawAmount;
    @ApiModelProperty("今日提现金额")
    private BigDecimal todayWithdrawAmount;
    @ApiModelProperty("今日提现人数")
    private Long todayWithdrawCount;
    @ApiModelProperty("总充值金额")
    private BigDecimal totalChargeAmount;
    @ApiModelProperty("今日充值金额")
    private BigDecimal todayChargeAmount;
    @ApiModelProperty("今日充值人数")
    private Long todayChargeCount;
    @ApiModelProperty("今日首冲人数")
    private Long todayFirstChargeCount;
    @ApiModelProperty("今日首冲金额")
    private BigDecimal todayFirstChargeAmount;
    @ApiModelProperty("今日活跃人数")
    private Long todayActiveCount;
    @ApiModelProperty("昨日活跃人数")
    private Long yesterdayActiveCount;
    @ApiModelProperty("追加跟单待审核")
    private Long appendFollowPendingCount;
    @ApiModelProperty("充值待审核")
    private Long chargePendingCount;
    @ApiModelProperty("提现待审核")
    private Long withdrawPendingCount;
    @ApiModelProperty("实名认证待审核")
    private Long realNamePendingCount;
} 