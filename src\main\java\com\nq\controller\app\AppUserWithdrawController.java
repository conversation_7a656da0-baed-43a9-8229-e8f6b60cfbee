package com.nq.controller.app;

import com.github.pagehelper.PageInfo;
import com.nq.common.ServerResponse;
import com.nq.pojo.User;
import com.nq.pojo.UserWithdraw;
import com.nq.service.IUserService;
import com.nq.service.IUserWithdrawService;
import com.nq.utils.SymmetricCryptoUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping({ "/user/withdraw/" })
@Api(tags = "APP-提现")
public class AppUserWithdrawController {
    private static final Logger log = LoggerFactory.getLogger(AppUserWithdrawController.class);

    @Resource
    private IUserWithdrawService iUserWithdrawService;

    @Resource
    private IUserService iUserService;

    // 分页查询所有提现记录
    @ApiOperation("分页查询所有提现记录")
    @PostMapping({ "list.do" })
    public ServerResponse<PageInfo<UserWithdraw>> list(@RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @RequestParam(value = "pageSize", defaultValue = "8") int pageSize,
            @RequestParam(value = "withStatus", required = false) String withStatus) {
        PageInfo<UserWithdraw> userWithList = iUserWithdrawService.findUserWithList(withStatus, pageNum, pageSize);
        return ServerResponse.createBySuccess(userWithList);
    }

    // 用户提现
    @ApiOperation("用户提现")
    @PostMapping({ "outMoney.do" })
    public ServerResponse<String> outMoney(String amt, String withPwd, HttpServletRequest request) throws Exception {
        try {
            // 打印接收到的参数，用于调试
            log.info("用户提现参数：amt={}, withPwd={}", amt, withPwd != null ? "***" : null);

            User user = iUserService.getCurrentUser();
            if (user == null) {
                return ServerResponse.createByErrorMsg("用户未登录");
            }

            // 验证资金密码
            if (withPwd == null || withPwd.trim().isEmpty()) {
                return ServerResponse.createByErrorMsg("资金密码不能为空");
            }

            if (user.getWithPwd() == null) {
                return ServerResponse.createByErrorMsg("请先设置资金密码");
            }

            if (!SymmetricCryptoUtil.encryptPassword(withPwd).equals(user.getWithPwd())) {
                return ServerResponse.createByErrorMsg("资金密码错误");
            }

            // 调用提现服务
            String result = this.iUserWithdrawService.outMoney(amt, user.getWithPwd(), user, request);

            // 判断结果
            if ("提现订单提交成功".equals(result)) {
                return ServerResponse.createBySuccessMsg(result);
            } else {
                return ServerResponse.createByErrorMsg(result);
            }
        } catch (Exception e) {
            log.error("用户提现失败", e);
            return ServerResponse.createByErrorMsg("提现失败：" + e.getMessage());
        }
    }

}