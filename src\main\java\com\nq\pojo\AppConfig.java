package com.nq.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nq.dto.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("app_config")
@ApiModel(description = "APP配置信息")
public class AppConfig extends BaseEntity {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "配置组信息", example = "系统配置")
    private String groupName;

    @ApiModelProperty(value = "配置每一项key", example = "site.name")
    private String configName;

    @ApiModelProperty(value = "配置内容", example = "交易系统")
    private String configValue;

    @ApiModelProperty(value = "类型(text,mp3,mp4,jpg)", example = "text")
    private String type;

    @ApiModelProperty(value = "备注", example = "系统名称配置")
    private String remark;

    @ApiModelProperty(value = "创建时间", example = "2024-01-01 12:00:00")
    private Date createdTime;
} 