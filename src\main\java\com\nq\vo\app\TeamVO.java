package com.nq.vo.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "团队信息展示对象")
public class TeamVO {
    
    @ApiModelProperty(value = "团队总人数", example = "100")
    private Integer teamTotalNum;
    
    @ApiModelProperty(value = "直属人数", example = "20")
    private Long directMemberNum;
    
    @ApiModelProperty(value = "三日未跟单人数", example = "5")
    private Integer threeNoFollowNum;
    
    @ApiModelProperty(value = "今日首充人数", example = "3")
    private Long todayFirstChargeNum;
} 