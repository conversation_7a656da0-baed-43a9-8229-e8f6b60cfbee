package com.nq.vo.app;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
@Data
@ApiModel(description = "交易记录每一项")
public class TradeDetailVO {
    @ApiModelProperty(value = "股票名称", example = "维信诺")
    private String stockName;

    @ApiModelProperty(value = "股票代码", example = "002387")
    private String stockCode;

    @ApiModelProperty(value = "买入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date buyTime;

    @ApiModelProperty(value = "卖出时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sellTime;

    @ApiModelProperty(value = "盈亏百分比", example = "1.75")
    private BigDecimal profitLossPercent;


}
