package com.nq.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.nq.constant.OrderConstant;
import com.nq.dao.*;
import com.nq.enums.OrderTypeEnum;
import com.nq.enums.TypeEnum;
import com.nq.excepton.CustomException;
import com.nq.function.AmountConsumer;
import com.nq.manage.UserAmountChangeManage;
import com.nq.pojo.*;
import com.nq.service.ISiteMessageService;
import com.nq.service.IUserService;
import com.nq.service.IUserWithdrawService;
import com.nq.service.MerchantPayService;
import com.nq.utils.DateTimeUtil;
import com.nq.utils.DateUtils;
import com.nq.utils.SnowIdUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Service("iUserWithdrawService")
public class UserWithdrawServiceImpl extends ServiceImpl<UserWithdrawMapper, UserWithdraw>
        implements IUserWithdrawService {
    private static final Logger log = LoggerFactory.getLogger(UserWithdrawServiceImpl.class);

    @Resource
    private UserWithdrawMapper userWithdrawMapper;
    @Resource
    private IUserService iUserService;
    @Resource
    private UserMapper userMapper;

    @Resource
    private SiteMessageMapper siteMessageMapper;
    @Resource
    private SiteSettingMapper siteSettingMapper;
    @Resource
    private ISiteMessageService iSiteMessageService;
    @Resource
    private UserBankMapper userBankMapper;
    @Resource
    private UserAmountChangeManage userAmountChangeManage;
    @Resource
    private MerchantPayService merchantPayService;

    @Transactional
    @Override
    public String outMoney(String amt, String with_Pwd, User user, HttpServletRequest request) throws Exception {
        // 判断用户余额是否足够
        BigDecimal amount = new BigDecimal(amt);
        if (user.getUserAmt().compareTo(amount) < 0) {
            return "提现金额不足";
        }
        // 判断用户前面是否有未处理的提现订单
        Long count = this.userWithdrawMapper.selectCount(Wrappers.lambdaQuery(UserWithdraw.class)
                .eq(UserWithdraw::getUserId, user.getId()).eq(UserWithdraw::getWithStatus, 0));
        if (count > 0) {
            return "当前有提现订单正在处理,请处理完再申请提现 !";
        }

        // 获取用户的银行卡
        UserBank userBank = userBankMapper.findUserBankByUserId(user.getId());
        if (userBank == null) {
            return "请先绑定银行卡";
        }

        // 获取交易配置
        SiteSetting siteSetting = siteSettingMapper.selectById(1);
        if (siteSetting == null) {
            return "系统配置错误，请联系客服";
        }
        // 创建提现订单
        UserWithdraw userWithdraw = new UserWithdraw();
        userWithdraw.setUserId(user.getId());
        String orderNo = SnowIdUtil.getId(OrderConstant.withdrawalOrder);
        userWithdraw.setOrderSn(orderNo);
        userWithdraw.setWithAmt(amount);
        userWithdraw.setApplyTime(new Date());
        userWithdraw.setWithName(user.getRealName());
        userWithdraw.setBankNo(userBank.getBankNo());
        userWithdraw.setBankName(userBank.getBankName());
        userWithdraw.setBankAddress(userBank.getBankAddress());
        userWithdraw.setNickName(user.getNickName());
        userWithdraw.setWithStatus(0);
        BigDecimal withfee = siteSetting.getWithFeePercent().multiply(new BigDecimal(amt));
        userWithdraw.setWithFee(withfee);
        userWithdraw.setWithOutAmt(userWithdraw.getWithAmt().subtract(withfee));
        // 检查依赖注入
        if (userAmountChangeManage == null) {
            log.error("userAmountChangeManage 为 null，依赖注入失败");
            return "系统错误，请联系客服";
        }

        // 修改用户余额和插入提现订单
        AmountConsumer amountConsumer = (oldUser, newUser) -> {
            // 插入提现订单
            int insertCount = userWithdrawMapper.insert(userWithdraw);
            log.info("插入提现订单结果：insertCount={}, userWithdraw={}", insertCount, userWithdraw);
            if (insertCount > 0) {
                SiteMessage siteMessage = new SiteMessage();
                siteMessage.setUserId(user.getId());
                siteMessage.setMessageType(SiteMessage.MSG_TYPE_ADMIN);
                siteMessage.setUserName(user.getRealName());
                siteMessage.setTypeName("提现申请");
                siteMessage.setStatus(1);
                siteMessage.setContent("有新的提现申请,请及时关注哦。");
                siteMessage.setAddTime(DateTimeUtil.getCurrentDate());
                iSiteMessageService.insert(siteMessage);
            } else {
                throw new RuntimeException("插入提现订单失败");
            }
        };

        try {
            userAmountChangeManage.changeBalance(user.getId(), amount, orderNo, OrderTypeEnum.WITHDRAW,
                    TypeEnum.WITHDRAW,
                    "", "", amountConsumer);
            return "提现订单提交成功";
        } catch (Exception e) {
            log.error("提现申请失败", e);
            throw new RuntimeException("提现申请失败：" + e.getMessage());
        }
    }

    @Override
    public PageInfo<UserWithdraw> findUserWithList(String withStatus, int pageNum, int pageSize) {
        User user = this.iUserService.getCurrentUser();
        PageHelper.startPage(pageNum, pageSize);
        // 构建 MyBatis-Plus 查询条件
        LambdaQueryWrapper<UserWithdraw> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserWithdraw::getUserId, user.getId());
        queryWrapper.eq(StringUtils.isNotEmpty(withStatus), UserWithdraw::getWithStatus, withStatus);
        queryWrapper.orderByDesc(UserWithdraw::getId);

        List<UserWithdraw> userWithdraws = this.userWithdrawMapper.selectList(queryWrapper);
        return new PageInfo<>(userWithdraws);
    }

    @Override
    public PageInfo<UserWithdraw> listByAdmin(Integer userId, String orderSn, String nickName, Integer state,
            String beginTime, String endTime, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        LambdaQueryWrapper<UserWithdraw> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(userId != null, UserWithdraw::getUserId, userId);
        queryWrapper.like(StringUtils.isNotEmpty(nickName), UserWithdraw::getNickName, nickName);
        queryWrapper.like(StringUtils.isNotEmpty(orderSn), UserWithdraw::getOrderSn, orderSn);
        queryWrapper.eq(state != null, UserWithdraw::getWithStatus, state);
        queryWrapper.ge(StringUtils.isNotEmpty(beginTime), UserWithdraw::getTransTime, beginTime);
        queryWrapper.le(StringUtils.isNotEmpty(endTime), UserWithdraw::getTransTime, endTime);
        queryWrapper.orderByDesc(UserWithdraw::getId);
        List<UserWithdraw> userWithdraws = this.userWithdrawMapper.selectList(queryWrapper);
        return new PageInfo<>(userWithdraws);
    }

    @Override
    public void outMoneyByAdmin(String orderNo, String amt, String payType, Integer userId) {
        if (StringUtils.isBlank(amt) || StringUtils.isBlank(payType)) {
            throw new CustomException("参数不能为空");
        }
        User user = iUserService.findByUserId(userId);
        UserBank userBank = userBankMapper.findUserBankByUserId(user.getId());
        if (userBank == null) {
            throw new CustomException("未绑定银行号");
        }
        UserWithdraw userWithdraw = new UserWithdraw();
        userWithdraw.setOrderSn(orderNo);
        userWithdraw.setWithAmt(new BigDecimal(amt));
        userWithdraw.setApplyTime(new Date());
        userWithdraw.setWithName(user.getRealName());
        userWithdraw.setBankNo(userBank.getBankNo());
        userWithdraw.setBankName(userBank.getBankName());
        userWithdraw.setBankAddress(userBank.getBankAddress());
        userWithdraw.setWithFee(BigDecimal.ZERO);
        userWithdraw.setWithOutAmt(BigDecimal.ZERO);
        userWithdraw.setWithName(user.getRealName());
        userWithdraw.setTransTime(new Date());
        userWithdraw.setUserId(userId);
        userWithdraw.setNickName(user.getNickName());
        userWithdraw.setWithStatus(1);
        int insertCount = userWithdrawMapper.insert(userWithdraw);
        if (insertCount > 0) {
            return;
        }
        throw new CustomException("创建支付订单失败");
    }

    @Override
    public BigDecimal countWithdrawAmountByDate(Integer type) {
        HashMap<String, Date> todayMap = DateUtils.changeTimeSection(type);
        Date startTime = todayMap.get("startTime");
        Date endTime = todayMap.get("endTime");
        LambdaQueryWrapper<UserWithdraw> q = new LambdaQueryWrapper<>();
        q.orderByDesc(UserWithdraw::getId);
        q.ge(UserWithdraw::getApplyTime, startTime);
        q.le(UserWithdraw::getApplyTime, endTime);
        q.eq(UserWithdraw::getWithStatus, 1);
        List<UserWithdraw> arr = this.list(q);
        return arr.stream().map(UserWithdraw::getWithAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public Long countWithdrawNumByDate(Integer type) {
        HashMap<String, Date> todayMap = DateUtils.changeTimeSection(type);
        Date startTime = todayMap.get("startTime");
        Date endTime = todayMap.get("endTime");
        LambdaQueryWrapper<UserWithdraw> q = new LambdaQueryWrapper<>();
        q.orderByDesc(UserWithdraw::getId);
        q.ge(UserWithdraw::getApplyTime, startTime);
        q.le(UserWithdraw::getApplyTime, endTime);
        q.eq(UserWithdraw::getWithStatus, 1);
        return this.count(q);
    }

    @Override
    public Long countWithdrawPendingCount() {
        LambdaQueryWrapper<UserWithdraw> q = new LambdaQueryWrapper<>();
        q.eq(UserWithdraw::getWithStatus, 0);
        return this.count(q);
    }

    @Override
    public void updateState(Integer withId, Integer state, String authMsg) {
        UserWithdraw userWithdraw = this.userWithdrawMapper.selectById(withId);
        if (userWithdraw == null) {
            throw new RuntimeException("提现订单不存在");
        }
        if (userWithdraw.getWithStatus() != 0) {
            throw new RuntimeException("提现订单已处理，不要重复操作");
        }
        if (state == 2 && StringUtils.isBlank(authMsg)) {
            throw new RuntimeException("失败信息必填");
        }
        User user = this.userMapper.selectById(userWithdraw.getUserId());
        BigDecimal withOutAmt = userWithdraw.getWithOutAmt();
        String orderSn = userWithdraw.getOrderSn();
        if (state == 2) {
            AmountConsumer consumer = (oldUser, newUser) -> {
                userWithdraw.setWithStatus(2);
                userWithdrawMapper.updateById(userWithdraw);
                SiteMessage siteMessage = new SiteMessage();
                siteMessage.setUserId(user.getId());
                siteMessage.setMessageType(SiteMessage.MSG_TYPE_WAP);
                siteMessage.setStatus(1);
                siteMessage.setUserName(user.getRealName());
                siteMessage.setAddTime(new Date());
                siteMessage.setTypeName("提现成功");
                siteMessage.setContent("您的账户成功提现 " + withOutAmt);
                this.siteMessageMapper.insert(siteMessage);
            };
            userAmountChangeManage.changeBalance(user.getId(), withOutAmt, orderSn, OrderTypeEnum.BACK,
                    TypeEnum.WITHDRAWBACK, "", "", consumer);
        }
        userWithdraw.setWithStatus((state == 1) ? 1 : 2);
        userWithdraw.setTransTime(new Date());
        int updateCount = this.userWithdrawMapper.updateById(userWithdraw);
        if (updateCount > 0) {
            String result = "";
            switch (state) {
                case 1:
                    result = " 成功";
                    break;
                case 2:
                case 3:
                    result = " 失败，请联系客服";
                    break;
            }
            if (user != null) {
                SiteMessage siteMessage = new SiteMessage();
                siteMessage.setUserId(user.getId());
                siteMessage.setMessageType(SiteMessage.MSG_TYPE_WAP);
                siteMessage.setStatus(1);
                siteMessage.setUserName(user.getRealName());
                siteMessage.setAddTime(new Date());
                siteMessage.setTypeName("提现通知");
                siteMessage.setContent("您发起的提现 " + userWithdraw.getWithAmt() + result);
                this.siteMessageMapper.insert(siteMessage);
            }
            return;
        }
        throw new RuntimeException("操作失败！");
    }

    @Override
    public void check(Integer withId, Integer state, String authMsg) {
        // 参数验证
        if (withId == null) {
            throw new RuntimeException("提现ID不能为空");
        }
        if (state == null) {
            throw new RuntimeException("审核状态不能为空");
        }

        // 查询提现订单
        UserWithdraw userWithdraw = this.userWithdrawMapper.selectById(withId);
        if (userWithdraw == null) {
            throw new RuntimeException("提现订单不存在");
        }
        if (userWithdraw.getWithStatus() != 0) {
            throw new RuntimeException("提现订单已处理，不要重复操作");
        }

        // 查询用户信息
        User user = this.userMapper.selectById(userWithdraw.getUserId());
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        BigDecimal withOutAmt = userWithdraw.getWithOutAmt();
        String orderSn = userWithdraw.getOrderSn();

        // 检查必要的依赖是否注入成功
        if (userAmountChangeManage == null) {
            log.error("userAmountChangeManage 为 null，依赖注入失败");
            throw new RuntimeException("系统错误：用户金额变更管理器未初始化");
        }

        if (state == 2) {
            // 提现拒绝
            AmountConsumer consumer = (oldUser, newUser) -> {
                userWithdraw.setWithStatus(2);
                userWithdrawMapper.updateById(userWithdraw);
                SiteMessage siteMessage = new SiteMessage();
                siteMessage.setUserId(user.getId());
                siteMessage.setMessageType(SiteMessage.MSG_TYPE_WAP);
                siteMessage.setStatus(1);
                siteMessage.setUserName(user.getRealName());
                siteMessage.setAddTime(new Date());
                siteMessage.setTypeName("提现拒绝");
                siteMessage.setContent(StrUtil.format("您的提现金额: {}, 已退回你账户 ", withOutAmt));
                this.siteMessageMapper.insert(siteMessage);
            };

            try {
                userAmountChangeManage.changeBalance(user.getId(), withOutAmt, orderSn, OrderTypeEnum.BACK,
                        TypeEnum.WITHDRAWBACK, "", "", consumer);
            } catch (Exception e) {
                log.error("提现拒绝时账户余额变更失败", e);
                throw new RuntimeException("提现拒绝处理失败：" + e.getMessage());
            }
        } else if (state == 1) {
            // 提现通过
            // 通知支付通道打钱
            List<MerchantPay> list = merchantPayService.lambdaQuery().eq(MerchantPay::getIsWithdraw, 1).list();
            if (CollUtil.isEmpty(list)) {
                throw new RuntimeException("暂无提现支付通道");
            }
            MerchantPay merchantPay = list.get(0);
            String code = merchantPay.getCode();
            // 查询该支付渠道的处理对象,进行提现下单
            Boolean flag = true;
            if (flag) {
                // 下单成功,将订单修改为下单成功状态,等支付回调时,修改为成功
                // userWithdraw.setWithStatus(4);
                userWithdraw.setWithStatus(1);
                userWithdrawMapper.updateById(userWithdraw);
            } else {
                // 支付方下单失败,
                throw new RuntimeException(String.format("支付通道: %s, 下单失败", merchantPay.getName()));
            }
        } else {
            throw new RuntimeException("无效的审核状态：" + state);
        }
    }

    @Override
    public int deleteByUserId(Integer userId) {
        return this.userWithdrawMapper.deleteByUserId(userId);
    }

    @Override
    public JSONObject hasWithdrawMessage() {
        /* 查询用户站内消息列表 */
        LambdaQueryWrapper<SiteMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SiteMessage::getStatus, 1)
                .eq(SiteMessage::getMessageType, SiteMessage.MSG_TYPE_ADMIN)
                .orderByDesc(SiteMessage::getId);

        List<SiteMessage> list = this.siteMessageMapper.selectList(queryWrapper);
        boolean hasWithdrawMessage = false;
        if (!list.isEmpty()) {
            hasWithdrawMessage = true;
            for (SiteMessage siteMessage : list) {
                siteMessage.setStatus(2);
                this.siteMessageMapper.updateById(siteMessage);
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("hasWithdrawMessage", hasWithdrawMessage);
        jsonObject.put("messageList", list);
        return jsonObject;
    }

    @Override
    public List<UserWithdraw> exportByAdmin(Integer userId, String orderSn, String nickName, Integer state,
            String beginTime, String endTime) {
        LambdaQueryWrapper<UserWithdraw> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(userId != null, UserWithdraw::getUserId, userId);
        queryWrapper.like(StringUtils.isNotEmpty(nickName), UserWithdraw::getNickName, nickName);
        queryWrapper.like(StringUtils.isNotEmpty(orderSn), UserWithdraw::getOrderSn, orderSn);
        queryWrapper.eq(state != null, UserWithdraw::getWithStatus, state);
        queryWrapper.ge(StringUtils.isNotEmpty(beginTime), UserWithdraw::getTransTime, beginTime);
        queryWrapper.le(StringUtils.isNotEmpty(endTime), UserWithdraw::getTransTime, endTime);
        queryWrapper.orderByDesc(UserWithdraw::getId);

        return this.userWithdrawMapper.selectList(queryWrapper);
    }

}
