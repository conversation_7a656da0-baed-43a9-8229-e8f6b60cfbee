package com.nq.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nq.pojo.ConvertBondApply;
import com.nq.service.ConvertBondApplyService;
import com.nq.dao.ConvertBondApplyMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【convert_bond_apply(用户转债申请表)】的数据库操作Service实现
* @createDate 2022-12-05 16:33:44
*/
@Service
public class ConvertBondApplyServiceImpl extends ServiceImpl<ConvertBondApplyMapper, ConvertBondApply>
    implements ConvertBondApplyService{

}




